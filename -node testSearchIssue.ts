[1mdiff --git a/app/Plugins/MeiliSearch.ts b/app/Plugins/MeiliSearch.ts[m
[1mindex bc54b6d..9432fd7 100755[m
[1m--- a/app/Plugins/MeiliSearch.ts[m
[1m+++ b/app/Plugins/MeiliSearch.ts[m
[36m@@ -1,5 +1,6 @@[m
 import Database from '@ioc:Adonis/Lucid/Database'[m
 import { Prisma, products } from '@prisma/client'[m
[32m+[m[32mimport { isSize } from 'App/Helpers/isSize'[m
 import Product from 'App/Models/Product'[m
 // import { productProvider } from 'App/Providers/ProductProvider'[m
 import { $prisma } from 'App/Services/Prisma'[m
[36m@@ -21,71 +22,100 @@[m [mexport class MeiliSearchPlugin {[m
   //   return productProvider.initAndTransformToAdonisModel(hits)[m
   // }[m
 [m
[31m-  productsDB() {[m
[31m-    return this.client.index(this.index)[m
[32m+[m[32m  async checkIndexExists(indexUid: string) {[m
[32m+[m[32m    try {[m
[32m+[m[32m      const index = await this.client.getIndex(indexUid)[m
[32m+[m[32m      return !!index[m
[32m+[m[32m    } catch (error) {[m
[32m+[m[32m      if (error.response.status === 404) {[m
[32m+[m[32m        return false[m
[32m+[m[32m      }[m
[32m+[m[32m      throw error // если другая ошибка — пробрасываем дальше[m
[32m+[m[32m    }[m
   }[m
 [m
[31m-  old_generateSkuTokens(articles: string | string[]): string[] {[m
[31m-    const tokens = new Set<string>()[m
[32m+[m[32m  /**[m
[32m+[m[32m   * Генерирует уникальное имя для временного индекса[m
[32m+[m[32m   * Использует timestamp для гарантии уникальности[m
[32m+[m[32m   */[m
[32m+[m[32m  private generateTempIndexName(baseName: string = 'products'): string {[m
[32m+[m[32m    const timestamp = Date.now()[m
[32m+[m[32m    const randomSuffix = Math.random().toString(36).substring(2, 8)[m
[32m+[m[32m    return `${baseName}_temp_${timestamp}_${randomSuffix}`[m
[32m+[m[32m  }[m
 [m
[31m-    // Преобразуем входные данные в массив, если передана строка[m
[31m-    const articlesArray = Array.isArray(articles) ? articles : [articles][m
[32m+[m[32m  /**[m
[32m+[m[32m   * Копирует все настройки из исходного индекса в целевой индекс[m
[32m+[m[32m   * Необходимо для корректной работы swapIndexes[m
[32m+[m[32m   */[m
[32m+[m[32m  private async copyIndexSettings(sourceIndexName: string, targetIndexName: string, timeoutMs: number = 15000): Promise<void> {[m
[32m+[m[32m    try {[m
[32m+[m[32m      console.log(`📋 Копируем настройки из ${sourceIndexName} в ${targetIndexName}`)[m
 [m
[31m-    // Обрабатываем каждый артикул[m
[31m-    for (const article of articlesArray) {[m
[31m-      // Пропускаем пустые значения[m
[31m-      if (!article) continue[m
[32m+[m[32m      // Получаем настройки исходного индекса[m
[32m+[m[32m      const settings = await this.client.index(sourceIndexName).getSettings()[m
 [m
[31m-      // Приводим артикул к нижнему регистру и обрезаем пробелы[m
[31m-      const normalized = article.trim().toLowerCase()[m
[32m+[m[32m      // Применяем настройки к целевому индексу[m
[32m+[m[32m      const updateTask = await this.client.index(targetIndexName).updateSettings(settings)[m
 [m
[31m-      // Всегда добавляем полный артикул, независимо от длины[m
[31m-      tokens.add(normalized)[m
[32m+[m[32m      // Ждем завершения применения настроек[m
[32m+[m[32m      await this.client.tasks.waitForTask(updateTask.taskUid, { timeout: timeoutMs })[m
 [m
[31m-      // Разбиваем артикул по разделителям: дефис, слеш, пробел, запятая, точка, двоеточие и т.д.[m
[31m-      const segments = normalized.split(/[\-\/\s,.;:]+/).filter((seg) => seg.length > 0)[m
[32m+[m[32m      console.log(`✅ Настройки скопированы успешно`)[m
[32m+[m[32m    } catch (error) {[m
[32m+[m[32m      console.error(`❌ Ошибка копирования настроек:`, error)[m
[32m+[m[32m      throw error[m
[32m+[m[32m    }[m
[32m+[m[32m  }[m
 [m
[31m-      // Добавляем токен без разделителей (конкатенация сегментов)[m
[31m-      if (segments.length > 1) {[m
[31m-        const noSeparators = segments.join('')[m
[31m-        tokens.add(noSeparators)[m
[31m-      }[m
[32m+[m[32m  /**[m
[32m+[m[32m   * Выполняет swap индексов для атомарного обновления данных без простоев[m
[32m+[m[32m   * @param tempIndexName Имя временного индекса[m
[32m+[m[32m   * @param mainIndexName Имя основного индекса (по умолчанию 'products')[m
[32m+[m[32m   * @param timeoutMs Таймаут ожидания в миллисекундах[m
[32m+[m[32m   */[m
[32m+[m[32m  private async performIndexSwap([m
[32m+[m[32m    tempIndexName: string,[m
[32m+[m[32m    mainIndexName: string = 'products',[m
[32m+[m[32m    timeoutMs: number = 30000[m
[32m+[m[32m  ): Promise<void> {[m
[32m+[m[32m    try {[m
[32m+[m[32m      console.log(`🔄 Выполняем swap индексов: ${tempIndexName} ↔ ${mainIndexName}`)[m
 [m
[31m-      // Проверяем, начинается ли артикул с буквы, за которой следуют цифры[m
[31m-      const prefixMatch = normalized.match(/^([a-z]+)(\d+.*)$/)[m
[31m-      if (prefixMatch) {[m
[31m-        const prefix = prefixMatch[1] // Буквенный префикс[m
[31m-        const numericPart = prefixMatch[2] // Числовая часть[m
[32m+[m[32m      // Выполняем swap[m
[32m+[m[32m      const swapTask = await this.client.swapIndexes([[m
[32m+[m[32m        { indexes: [tempIndexName, mainIndexName] }[m
[32m+[m[32m      ])[m
 [m
[31m-        // Добавляем числовую часть как отдельный токен[m
[31m-        tokens.add(numericPart)[m
[31m-      }[m
[32m+[m[32m      console.log(`✅ Swap задача создана: ${swapTask.taskUid}`)[m
 [m
[31m-      // Для каждого сегмента генерируем все суффиксы длиной не менее 3 символов[m
[31m-      for (const segment of segments) {[m
[31m-        for (let i = 0; i < segment.length; i++) {[m
[31m-          const token = segment.substring(i)[m
[31m-          if (token.length > 3) {[m
[31m-            tokens.add(token)[m
[31m-          }[m
[31m-        }[m
[31m-      }[m
[32m+[m[32m      // Ждем завершения swap операции[m
[32m+[m[32m      await this.client.tasks.waitForTask(swapTask.taskUid, { timeout: timeoutMs })[m
[32m+[m[32m      console.log(`✅ Swap индексов завершен успешно`)[m
 [m
[31m-      // Генерируем токены для артикула без разделителей (конкатенация сегментов)[m
[31m-      const concatenated = segments.join('')[m
[31m-      for (let i = 0; i < concatenated.length; i++) {[m
[31m-        const token = concatenated.substring(i)[m
[31m-        if (token.length > 3) {[m
[31m-          tokens.add(token)[m
[31m-        }[m
[32m+[m[32m      // После успешного swap временный индекс становится основным,[m
[32m+[m[32m      // поэтому удаляем старый индекс (который теперь имеет имя tempIndexName)[m
[32m+[m[32m      await this.client.deleteIndex(tempIndexName)[m
[32m+[m[32m      console.log(`🗑️ Временный индекс ${tempIndexName} удален`)[m
[32m+[m
[32m+[m[32m    } catch (error) {[m
[32m+[m[32m      console.error(`❌ Ошибка при swap индексов:`, error)[m
[32m+[m
[32m+[m[32m      // В случае ошибки пытаемся очистить временный индекс[m
[32m+[m[32m      try {[m
[32m+[m[32m        await this.client.deleteIndex(tempIndexName)[m
[32m+[m[32m        console.log(`🧹 Временный индекс ${tempIndexName} очищен после ошибки`)[m
[32m+[m[32m      } catch (cleanupError) {[m
[32m+[m[32m        console.warn(`⚠️ Не удалось очистить временный индекс ${tempIndexName}:`, cleanupError)[m
       }[m
[32m+[m
[32m+[m[32m      throw error[m
     }[m
[32m+[m[32m  }[m
 [m
[31m-    // Дополнительная фильтрация токенов: удаляем пустые значения и дубликаты[m
[31m-    return Array.from(tokens).filter((token) => {[m
[31m-      // Удаляем только пустые токены[m
[31m-      return token && token.trim().length > 0[m
[31m-    })[m
[32m+[m
[32m+[m[32m  productsDB() {[m
[32m+[m[32m    return this.client.index(this.index)[m
   }[m
 [m
   generateSkuTokens(articles: string | string[], minSuffixLength = 4, splitPattern = /[^a-z0-9]+/): string[] {[m
[36m@@ -138,37 +168,58 @@[m [mexport class MeiliSearchPlugin {[m
 [m
     const categories = rawCategories //.map(({ cat_id: id, ...rest }) => ({ id, ...rest }))[m
 [m
[31m-    await this.client.deleteIndex('categories')[m
[31m-    await this.client.createIndex('categories', { primaryKey: 'cat_id' })[m
[31m-[m
[31m-    await this.client.index('categories').updateSettings({[m
[31m-      filterableAttributes: [[m
[31m-        'cat_id',[m
[31m-        'cat_url',[m
[31m-        'cat_rootcat',[m
[31m-        'cat_active',[m
[31m-        'cat_url_ru',[m
[31m-        'cat_url_de',[m
[31m-        'cat_url_en',[m
[31m-        'cat_url_es',[m
[31m-        'cat_url_it',[m
[31m-        'cat_url_de',[m
[31m-        'cat_url_fr',[m
[31m-        'cat_url_pl'[m
[31m-      ],[m
[31m-      sortableAttributes: ['cat_sort', 'cat_search_sort'][m
[31m-    })[m
[31m-[m
[31m-    await this.updateRankingRulesSortFirst('categories')[m
[31m-[m
[31m-    const result = await this.client.index('categories').addDocuments(categories)[m
[31m-[m
[31m-    // .then((res) => console.log('Categories synced:', res))[m
[31m-[m
[31m-    // console.log('MeiliSearch response:', result)[m
[32m+[m[32m    const indexName = 'categories'[m
[32m+[m
[32m+[m[32m    // Проверяем существование индекса[m
[32m+[m[32m    const indexExists = await this.checkIndexExists(indexName)[m
[32m+[m
[32m+[m[32m    if (indexExists) {[m
[32m+[m[32m      // Используем updateDocuments для обновления существующего индекса[m
[32m+[m[32m      console.log('🔄 Индекс categories существует, обновляем данные')[m
[32m+[m
[32m+[m[32m      // Очищаем индекс[m
[32m+[m[32m      const deleteTask = await this.client.index(indexName).deleteAllDocuments()[m
[32m+[m[32m      await this.client.tasks.waitForTask(deleteTask.taskUid, { timeout: 10000 })[m
[32m+[m
[32m+[m[32m      // Загружаем новые данные[m
[32m+[m[32m      const result = await this.client.index(indexName).addDocuments(categories)[m
[32m+[m[32m      await this.client.tasks.waitForTask(result.taskUid, { timeout: 30000 })[m
[32m+[m
[32m+[m[32m      console.log('✅ Категории обновлены')[m
[32m+[m[32m    } else {[m
[32m+[m[32m      // Создаем новый индекс[m
[32m+[m[32m      console.log('🆕 Создаем индекс categories')[m
[32m+[m
[32m+[m[32m      await this.client.createIndex(indexName, { primaryKey: 'cat_id' })[m
[32m+[m
[32m+[m[32m      await this.client.index(indexName).updateSettings({[m
[32m+[m[32m        filterableAttributes: [[m
[32m+[m[32m          'cat_id',[m
[32m+[m[32m          'cat_url',[m
[32m+[m[32m          'cat_rootcat',[m
[32m+[m[32m          'cat_active',[m
[32m+[m[32m          'cat_url_ru',[m
[32m+[m[32m          'cat_url_de',[m
[32m+[m[32m          'cat_url_en',[m
[32m+[m[32m          'cat_url_es',[m
[32m+[m[32m          'cat_url_it',[m
[32m+[m[32m          'cat_url_de',[m
[32m+[m[32m          'cat_url_fr',[m
[32m+[m[32m          'cat_url_pl'[m
[32m+[m[32m        ],[m
[32m+[m[32m        sortableAttributes: ['cat_sort', 'cat_search_sort'][m
[32m+[m[32m      })[m
[32m+[m
[32m+[m[32m      await this.updateRankingRulesSortFirst(indexName)[m
[32m+[m
[32m+[m[32m      const result = await this.client.index(indexName).addDocuments(categories)[m
[32m+[m[32m      await this.client.tasks.waitForTask(result.taskUid, { timeout: 30000 })[m
[32m+[m
[32m+[m[32m      console.log('✅ Индекс categories создан')[m
[32m+[m[32m    }[m
 [m
     //console.timeEnd('syncCategoriesByPrisma')[m
[31m-    return result[m
[32m+[m[32m    return { success: true, count: categories.length }[m
   }[m
 [m
   async syncColumnsByPrisma() {[m
[36m@@ -179,69 +230,100 @@[m [mexport class MeiliSearchPlugin {[m
       }[m
     })[m
 [m
[31m-    // const columns = rawColumns.map(({ ID: id, cat_id: catId, ...rest }) => ({ id, catId, ...rest }))[m
[31m-[m
[31m-    // const columns = rawColumns.map(c => {[m
[31m-    //   return {[m
[31m-    //     id: c.ID,[m
[31m-    //     keyname: c.keyname,[m
[31m-    //     title: c.title,[m
[31m-    //     sort: c.sort,[m
[31m-    //     cat_ide: c.cat_id,[m
[31m-    //     sorted: c.sorted,[m
[31m-    //     slot: c.slot[m
[31m-    //   }[m
[31m-    // })[m
[31m-[m
     const columns = rawColumns[m
 [m
[31m-    await this.client.deleteIndex('columns')[m
[31m-    await this.client.createIndex('columns', { primaryKey: 'ID' })[m
[31m-    await this.client.index('columns').updateSettings({[m
[31m-      filterableAttributes: ['cat_id', 'keyname'],[m
[31m-      sortableAttributes: ['sort', 'ID'][m
[31m-    })[m
[32m+[m[32m    const indexName = 'columns'[m
[32m+[m
[32m+[m[32m    // Проверяем существование индекса[m
[32m+[m[32m    const indexExists = await this.checkIndexExists(indexName)[m
 [m
[31m-    await this.updateRankingRulesSortFirst('columns')[m
[32m+[m[32m    if (indexExists) {[m
[32m+[m[32m      // Используем updateDocuments для обновления существующего индекса[m
[32m+[m[32m      console.log('🔄 Индекс columns существует, обновляем данные')[m
 [m
[31m-    await this.client[m
[31m-      .index('columns')[m
[31m-      .addDocuments(columns)[m
[31m-      .then((res) => console.log('Columns synced:', res))[m
[32m+[m[32m      // Очищаем индекс[m
[32m+[m[32m      const deleteTask = await this.client.index(indexName).deleteAllDocuments()[m
[32m+[m[32m      await this.client.tasks.waitForTask(deleteTask.taskUid, { timeout: 10000 })[m
[32m+[m
[32m+[m[32m      // Загружаем новые данные[m
[32m+[m[32m      const result = await this.client.index(indexName).addDocuments(columns)[m
[32m+[m[32m      await this.client.tasks.waitForTask(result.taskUid, { timeout: 30000 })[m
[32m+[m
[32m+[m[32m      console.log('✅ Колонки обновлены')[m
[32m+[m[32m    } else {[m
[32m+[m[32m      // Создаем новый индекс[m
[32m+[m[32m      console.log('🆕 Создаем индекс columns')[m
[32m+[m
[32m+[m[32m      await this.client.createIndex(indexName, { primaryKey: 'ID' })[m
[32m+[m[32m      await this.client.index(indexName).updateSettings({[m
[32m+[m[32m        filterableAttributes: ['cat_id', 'keyname'],[m
[32m+[m[32m        sortableAttributes: ['sort', 'ID'][m
[32m+[m[32m      })[m
[32m+[m
[32m+[m[32m      await this.updateRankingRulesSortFirst(indexName)[m
[32m+[m
[32m+[m[32m      const result = await this.client.index(indexName).addDocuments(columns)[m
[32m+[m[32m      await this.client.tasks.waitForTask(result.taskUid, { timeout: 30000 })[m
[32m+[m
[32m+[m[32m      console.log('✅ Индекс columns создан')[m
[32m+[m[32m    }[m
 [m
     //console.timeEnd('syncColumnsByPrisma')[m
[32m+[m[32m    return { success: true, count: columns.length }[m
   }[m
 [m
   async syncFiltersByPrisma() {[m
     //console.time('syncFiltersByPrisma')[m
     const filters = await $prisma.filters.findMany()[m
 [m
[31m-    await this.client.deleteIndex('filters')[m
[31m-    await this.client.createIndex('filters', { primaryKey: 'id' })[m
[32m+[m[32m    const indexName = 'filters'[m
 [m
[31m-    await this.client.index('filters').updateSettings({[m
[31m-      filterableAttributes: ['category_id', 'field'][m
[31m-    })[m
[32m+[m[32m    // Проверяем существование индекса[m
[32m+[m[32m    const indexExists = await this.checkIndexExists(indexName)[m
[32m+[m
[32m+[m[32m    if (indexExists) {[m
[32m+[m[32m      // Используем updateDocuments для обновления существующего индекса[m
[32m+[m[32m      console.log('🔄 Индекс filters существует, обновляем данные')[m
[32m+[m
[32m+[m[32m      // Очищаем индекс[m
[32m+[m[32m      const deleteTask = await this.client.index(indexName).deleteAllDocuments()[m
[32m+[m[32m      await this.client.tasks.waitForTask(deleteTask.taskUid, { timeout: 10000 })[m
[32m+[m
[32m+[m[32m      // Загружаем новые данные[m
[32m+[m[32m      const result = await this.client.index(indexName).addDocuments(filters)[m
[32m+[m[32m      await this.client.tasks.waitForTask(result.taskUid, { timeout: 30000 })[m
 [m
[31m-    await this.updateRankingRulesSortFirst('filters')[m
[32m+[m[32m      console.log('✅ Фильтры обновлены')[m
[32m+[m[32m    } else {[m
[32m+[m[32m      // Создаем новый индекс[m
[32m+[m[32m      console.log('🆕 Создаем индекс filters')[m
 [m
[31m-    await this.client[m
[31m-      .index('filters')[m
[31m-      .addDocuments(filters)[m
[31m-      .then((res) => console.log('Filters synced:', res))[m
[32m+[m[32m      await this.client.createIndex(indexName, { primaryKey: 'id' })[m
[32m+[m
[32m+[m[32m      await this.client.index(indexName).updateSettings({[m
[32m+[m[32m        filterableAttributes: ['category_id', 'field'][m
[32m+[m[32m      })[m
[32m+[m
[32m+[m[32m      await this.updateRankingRulesSortFirst(indexName)[m
[32m+[m
[32m+[m[32m      const result = await this.client.index(indexName).addDocuments(filters)[m
[32m+[m[32m      await this.client.tasks.waitForTask(result.taskUid, { timeout: 30000 })[m
[32m+[m
[32m+[m[32m      console.log('✅ Индекс filters создан')[m
[32m+[m[32m    }[m
 [m
     //console.timeEnd('syncFiltersByPrisma')[m
[32m+[m[32m    return { success: true, count: filters.length }[m
   }[m
 [m
[31m-  // Где-то при инициализации или настройке индекса[m
[31m-  async updateRankingRulesSortFirst(documentName = 'products') {[m
[32m+[m[32m  async updateRankingRulesSortFirst(documentName = 'products', timeoutMs: number = 10000) {[m
     try {[m
       const newRankingRules = ['sort', 'words', 'typo', 'proximity', 'attribute', 'exactness'][m
 [m
       const task = await this.client.index(documentName).updateRankingRules(newRankingRules)[m
       console.log('Update ranking rules task:', task)[m
       // Можно дождаться завершения задачи[m
[31m-      await this.client.tasks?.waitForTask(task.taskUid)[m
[32m+[m[32m      await this.c