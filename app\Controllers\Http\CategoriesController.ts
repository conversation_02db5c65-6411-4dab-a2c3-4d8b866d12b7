// import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Category from 'App/Models/Category'
import LangDict from 'App/Models/LangDict'

export default class CategoriesController {
    public async catalog({ request }) {

        const locale = request.headers()['x-locale'] || undefined
        // console.log('CategoriesController locale', locale)
        
        try {
            const _category = new Category()
            const categories = await _category.getCategories({})

            categories.map(category => {
                category.$attributes.cat_base64_pic = category.$extras.cat_base64_pic
                category.subcategories = categories.filter(i => i.cat_rootcat == category.cat_id)
            })

            if (locale) {
                await LangDict.categoriesTranslate(categories, request.requestQs.locale)
            }

            return categories.filter(i => i.cat_rootcat == 0)
        } catch (error) {
            console.log(error);
            
            return []
        }
    }
}
