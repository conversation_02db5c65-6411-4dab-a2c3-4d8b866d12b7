// import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import HttpContext, { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import { getModelColumns } from 'App/Helpers/getModelColumns'
import { maskBuilder } from 'App/Helpers/makeFilterMask'
import { parseExtandedQueries } from 'App/Helpers/parseExtandedQueries'
import { randomInteger } from 'App/Helpers/randomInteger'
import sliceIntoChunks from 'App/Helpers/sliceIntoChunks'
import Client from 'App/Models/Client'
import Journal from 'App/Models/Journal'
import Order from 'App/Models/Order'
import Org from 'App/Models/Org'
import Passport from 'App/Models/Passport'

export default class ClientsController {
  public async cpanResetPassword({ request, params, auth, response }: HttpContextContract) {
    let user = await auth.use('api').authenticate()

    const { id } = params
    const { value } = request.qs()

    const client = await Client.findOrFail(id)

    client.password = value || randomInteger(1200, 9800)

    await client.save()

    return response.status(200)
  }

  public async update({ request, params, auth }) {
    const client = await auth.authenticate()
    const clientData = request.requestBody.client

    if (clientData.org) {
      const orgData = clientData.org
      let org: Org

      org = await Org.findBy('org_client', client.client_number)
      
      if (!org) {
        org = new Org()
      }


      delete clientData.org

      try {
        if (org) {
          // org.merge(orgData)
          Object.keys(orgData).map((key) => (org[key] = orgData[key]))
          org.org_client = client.client_number
          await org.save()
        }
      } catch (error) {
        console.error('ClientController.ts > update: ', error)
        return []
      }
    }

    try {
      Object.keys(clientData).map((key) => (client[key] = clientData[key]))
      await client.save()
    } catch (error) {
      console.error('ClientController.ts > update: ', error)
      return []
    }

    return { msg: 'Данные обновлены' }
  }

  public async dashboardStatistics({ request, params, auth }) {
    const client = await auth.authenticate()

    const { ordersTotal } = await Order.query().count('order_id as ordersTotal').whereNot('order_status', 'Отменен').first()
    const { ordersThisMonths } = await Order.query()
      .count('order_id as ordersTotal')
      .whereNot('order_status', 'Отменен')
      .andWhereRaw('order_datetime >= UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY - INTERVAL 1 MONTH)')
      .andWhereRaw('order_datetime <  UNIX_TIMESTAMP(LAST_DAY(CURDATE()) + INTERVAL 1 DAY)')
      .first()

    return {
      ordersTotal,
      ordersThisMonths
    }
  }

  public async debtors({ request, params, auth }: HttpContextContract) {
    let authUser = await auth.use('api').authenticate()
    const { client_mail } = request.qs()

    // SELECT sum(`order_price`) as 'summ' FROM `orders` WHERE order_status not in ('Завершен', 'Оплачен', 'Не обработан', 'Отменен', 'Отправлен') and `order_client` = 999927159;

    // SELECT sum(`order_price`) as 'summ', order_client FROM `orders`
    // WHERE order_status not in ('Завершен', 'Оплачен', 'Не обработан', 'Отменен', 'Отправлен') G
    //ROUP BY order_client order by sum(`order_price`) DESC;

    const debtors = await Database.from('orders')
      // .sum('order_price', 'sum')
      .leftJoin('clients', 'clients.client_id', 'orders.order_client')
      .select(Database.raw('sum(`order_price` + `order_shippingprice`) as `sum`, `order_client`, `client_name`, `client_mail`, `client_phone`, `client_number`'))
      .whereNotIn('order_status', ['Завершен', 'Оплачен', 'Не обработан', 'Отменен', 'Отправлен'])
      .andWhere('order_locale', 'ru')
      .orderByRaw('sum(order_price) desc')
      .groupBy('order_client')
      .if(client_mail, (query) => query.andWhere('clients.client_mail', client_mail))
      .limit(100)
    // //.debug(true)

    return client_mail ? debtors?.[0] : debtors
  }

  public async id({ request, params, auth }: HttpContextContract) {
    // let user = await auth.use('api').authenticate()

    const { id } = params

    const client = await Client.findOrFail(id)

    await Promise.all([
      await client.load('orders', (query) => {
        query.orderBy('order_id', 'desc')
        // query.preload('snapshots', snapquery => snapquery.orderBy('ID', 'desc'))
        // query.paginate(page, limit)
        query.limit(10)
      }),
      await client.load('org')
    ])

    const { orders_sum, orders_count } = await client.getOrdersStat()

    const data = client.toJSON()

    data.orders_sum = orders_sum
    data.orders_count = orders_count

    await Promise.all(
      data.orders.map(async (order) => {
        order.lastSnapshot = await Order.lastSnapshot(order.order_id)
      })
    )

    return data
  }

  public async list({ request, params, auth }: HttpContextContract) {
    // const httpCtx = HttpContext.get()
    // let user = await httpCtx?.auth.use('api').authenticate()

    // SELECT * FROM `orders` WHERE order_status not in ('Завершен', 'Оплачен', 'Не обработан', 'Отменен', 'Отправлен') and order_locale = 'ru'

    // ДОБАВИТЬ ОПЛАТЫ К ЗАКАЗАМ

    // let user = await auth.use('api').authenticate()

    // Journal.createItem({
    //     entity: 'clients',
    //     entity_id: '',
    //     msg: 'просмотр списка пользователей',
    //     user_id: user.user_id,
    //     user_name: user.user_name
    // })

    const client = Database.connection()
    const columnsInfo = await client.columnsInfo('clients')
    const columns = Object.keys(columnsInfo)

    let _qs = request.qs()

    Object.keys(_qs).map((key) => {
      if (_qs[key] == 'null' || _qs[key] == 'undefined') {
        delete _qs[key]
      }
    })

    let { page = 1, limit = 200, sortField = 'client_id', sortOrder = 'desc', searchvalue = '', filters } = _qs
    let filtersActivated = false
    let extandedQueries = parseExtandedQueries(String(searchvalue))

    let rmClientCols = ['clients.password']

    let clientColumns = getModelColumns(Client, false).filter((x) => !rmClientCols.includes(x))
    let orgColumns = getModelColumns(Org, false)

    if (!page || page == '0') {
      page = 1
    }

    try {
      filters = JSON.parse(filters)
      if (Object.keys(filters).length) filtersActivated = true
    } catch (error) {
      console.log('error parse filters: ', error)
      filtersActivated = false
    }

    const clients = await Client.query()
      // .select(clientColumns)
      .if(filtersActivated, (query) => {
        Object.keys(filters).map((key) => {
          let filter = filters[key]
          maskBuilder.query(query)[filter.matchMode](filter.value, key)
        })
      })
      .andWhere((builder) => {
        if (extandedQueries.length > 1) {
          extandedQueries.map((q) => {
            builder.whereRaw(`CONCAT(${clientColumns.join(',')}) LIKE ?`, [`%${q}%`])
          })
        } else if (extandedQueries.length > 0) {
          clientColumns.map((field) => builder.orWhere(field, 'like', `%${extandedQueries[0]}%`))
        }
      })
      // .preload('orders', query => query.whereNotIn('order_status', ['Завершен', 'Оплачен', 'Не обработан', 'Отменен', 'Отправлен']))
      // .preload('unpaid_orders')
      //.preload('sum_orders')
      // .preload('orders')
      // .preload('org')
      .orderBy(sortField, sortOrder)
      // //.debug(true)
      .paginate(page, limit)

    // orgs.map(org => clients.push(org.user))

    //console.time('@@@load orgs')
    // await Promise.all(clients.map(async (client) => client.load('org')))
    await Promise.all(
      sliceIntoChunks(clients, 30).map(async (chunk) => {
        await Promise.all(
          chunk.map(async (client: Client) => {
            try {
              await client.load('org')
            } catch (error) {
              console.error(error)
            }
            // await client.load('unpaid_orders')
            // await client.load('sum_orders')
          })
        )
      })
    )
    //console.timeEnd('@@@load orgs')

    let clientsByOrgs: Array<Client> = []
    let orgs: Array<Org> = []
    if (extandedQueries.length) {
      orgs = await Org.query().andWhere((builder) => {
        if (extandedQueries.length > 1) {
          extandedQueries.map((q) => {
            builder.whereRaw(`CONCAT(${orgColumns.join(',')}) LIKE ?`, [`%${q}%`])
          })
        } else if (extandedQueries.length > 0) {
          orgColumns.map((field) => builder.orWhere(field, 'like', `%${extandedQueries[0]}%`))
        }
      })
      // .preload('user')
      // .paginate(page, limit)

      //console.time('@@@load org user')
      // await Promise.all(clients.map(async (client) => client.load('org')))
      await Promise.all(
        sliceIntoChunks(orgs, 30).map(async (chunk) => {
          await Promise.all(
            chunk.map(async (org: Org) => {
              // await org?.load('user')
              const _user = await Client.findBy('client_number', org.org_client)
              let user = _user?.toJSON()
              if (user) {
                user.org = org
                clientsByOrgs.push(user)
              }
            })
          )
        })
      )
      //console.timeEnd('@@@load org user')

      // console.log('@@@@@orgs: ', orgs)
    }

    return {
      clients,
      clientsByOrgs,
      columns
    }
  }

  //cpanDelete
  public async cpanDelete({ request, params, auth }: HttpContextContract) {
    const { id } = params

    const client = await Client.findOrFail(id)
    await client.delete()
  }

  public async cpanUpdate({ request, params, auth }: HttpContextContract) {
    const clientData = request.body()

    let client: Client
    let orgData: Org = undefined

    if (!clientData.client_id) {
      client = new Client()
    } else {
      client = await Client.findOrFail(clientData.client_id)
    }

    if (clientData.org) {
      orgData = { ...clientData.org }
      delete clientData.org
    } else {
      if (client.client_number) {
        const forg = await Org.findBy('org_client', client.client_number)
        if (forg) {
          await forg.delete()
        }
      }
    }

    let clientPassport = { ...clientData?.passport }
    delete clientData.passport

    try {
      client.merge(clientData)
      await client.save()
    } catch (error) {
      console.error('ClientController.ts > update: ', error)
      return []
    }

    if (clientData.passport) {
      await Client.createOrUpdatePassport(client, clientPassport)
    }

    if (orgData) {
      let org: Org

      if (orgData.org_id) {
        org = await Org.find(orgData.org_id)
      } else {
        org = new Org()
      }

      org.merge(orgData)
      org.org_client = client.client_number

      delete org.createdAt
      delete org.updatedAt

      await org.save()
    }

    return { msg: 'Данные обновлены' }
  }
}
