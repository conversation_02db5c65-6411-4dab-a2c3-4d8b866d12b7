import Env from '@ioc:Adonis/Core/Env'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import * as path from 'path'
import * as fs from 'fs'
import asyncFs from 'fs/promises'
import * as util from 'util'
import { FilePlugin } from 'App/Plugins/Files'
import User from 'App/Models/User'
import Product from 'App/Models/Product'

const readdir = util.promisify(fs.readdir)
type Target = 'rti' | 'rumi' | 'docs' | 'pdf'

interface FileListItem {
  fullname: string
  path: string
  ext: string
  name: string
  target: string
}

const DATA_PATH = path.resolve(Env.get('UPLOAD_PATH'))

const getDataPath = (target: Target, ...p: string[]): string => {
  return path.join(DATA_PATH, target, ...p)
}

export default class FilesController {
  public async remove({ request, auth }: HttpContextContract) {
    const user: User = await auth.use('api').authenticate()

    const target: Target = String(request.qs().target) as Target
    const fileName = request.qs().fileName

    if (!target || !fileName) return new Error('Bad request')

    FilePlugin.deleteByPath(path.join(target, fileName))
  }

  public async upload({ request }: HttpContextContract) {
    const reqFiles = request.files('files')

    const { applyToProduct, targetDir } = request.qs()

    await Promise.all(
      reqFiles.map(async (file) => {
        if (file.extname?.toLowerCase() === 'xlsx' || file.extname?.toLowerCase() === 'docx') {
          await file.move(getDataPath('docs'), { overwrite: true })
        } else {
          if (file.type === 'image') {
            
            const copyname = file.tmpPath + '10'
            fs.copyFileSync(file.tmpPath, copyname)

            await FilePlugin.save({ file, type: 'rti' })
            file.tmpPath = copyname
            await FilePlugin.save({ file, type: 'rumi' })

            if (applyToProduct && file.fileName) {
              const nameWithoutExt = file.fileName?.slice(0, file.fileName?.lastIndexOf('.'))
              const targetProduct = await Product.query()
                //format
                .where('prod_sku', nameWithoutExt)
                .orWhere('prod_analogsku', nameWithoutExt)
                .firstOrFail()

              targetProduct.prod_img = nameWithoutExt
              targetProduct.prod_img_rumi = nameWithoutExt

              await targetProduct.save()
            }
          } else if (file.type == 'pdf') {
            const p = getDataPath('pdf', targetDir)

            if (!fs.existsSync(p)) {
              fs.mkdirSync(p)
            }

            await file.move(getDataPath('pdf'), { overwrite: true })
          } else {
            throw new Error('Неизвестный тип файла')
          }
        }
      })
    )

    return true
  }

  public async renameFile({ request }: HttpContextContract) {
    const { target, oldName, newName } = request.body()

    const folderPath = getDataPath(target)
    const oldFilePath = path.join(folderPath, oldName)
    const newFilePath = path.join(folderPath, newName)

    try {
      await asyncFs.rename(oldFilePath, newFilePath)
      return { success: true, message: 'Файл успешно переименован' }
    } catch (error) {
      return { success: false, message: 'Ошибка при переименовании файла' }
    }
  }

  public async list({ request, auth }: HttpContextContract) {
    const user: User = await auth.use('api').authenticate()

    const target: Target = (String(request.qs().target) as Target) || 'rti'
    const folderPath = getDataPath(target)

    const { page = 1, pageSize = 100, search = '' } = request.qs()

    const dirfiles = await readdir(folderPath)

    const filteredFiles = search ? dirfiles.filter((fileName) => fileName.toLowerCase().includes(search.toLowerCase())) : dirfiles

    const startIndex = (page - 1) * pageSize
    const endIndex = page * pageSize
    const slicedFiles = filteredFiles.slice(startIndex, endIndex)

    const fileList: FileListItem[] = slicedFiles.map((fileName) => {
      const an = fileName.split('.')
      return {
        ext: an[an.length - 1],
        fullname: fileName,
        name: an.slice(0, an.length - 1).join(''),
        target,
        path: `/data/${target}/${fileName}`
      }
    })

    return {
      files: fileList,
      total: filteredFiles?.length || 0,
      page,
      pageSize,
      search
    }
  }
}
