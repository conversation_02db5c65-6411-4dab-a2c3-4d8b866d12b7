import LangDict from 'App/Models/LangDict';
// import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

export default class LangDictsController {

    public async dict({ request, params, response, auth }) {
        const { locale = 'en' } = params
        
        //console.log("🚀 ~ file: LangDictsController.ts ~ line 8 ~ LangDictsController ~ dict ~ locale", locale)
        
        const dict = await LangDict.getDict(locale)

        return dict
    }

}
