import { randomInteger } from 'App/Helpers/randomInteger'
import { SberbankApi } from 'App/Helpers/sberbank'
// import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Order from 'App/Models/Order'
import OrderSnapshot from 'App/Models/OrderSnapshot'
//import OrderItem        from 'App/Models/OrderItem'
import Product from 'App/Models/Product'
import Cart from 'App/Models/Cart'
import CartItem from 'App/Models/CartItem'
import Client from 'App/Models/Client'
import Database from '@ioc:Adonis/Lucid/Database'
import { parseQS } from 'App/Helpers/parseQS'
import Env from '@ioc:Adonis/Core/Env'

import Mail from '@ioc:Adonis/Addons/Mail'
import Application from '@ioc:Adonis/Core/Application'
import LangDict from 'App/Models/LangDict'
// import { productsChangeCurrency } from 'App/Helpers/productsChangeCurrency'
import loadSettings from 'App/Helpers/loadSettings'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { getModelColumns, getModelKeys } from 'App/Helpers/getModelColumns'
import { parseExtandedQueries } from 'App/Helpers/parseExtandedQueries'
import { maskBuilder } from 'App/Helpers/makeFilterMask'
import { DateTime } from 'luxon'
import Journal from 'App/Models/Journal'
import { SnapshotBodyInterface } from 'App/Interfaces/orders/snapshotBody'
import OrderItem from 'App/Models/OrderItem'
import { SnapshotBodyItemInterface } from 'App/Interfaces/orders/snapshotBodyItem'
import { Snapshot } from 'App/Interfaces/orders/Snapshot'
import sliceIntoChunks from 'App/Helpers/sliceIntoChunks'
import Gtd from 'App/Plugins/Gtd'
import { Yookassa } from 'App/Plugins/Yookassa'
// import View from '@ioc:Adonis/Core/View'
//import { viewLoader } from 'App/Helpers/viewLoader'

const EMAIL_FROM = Env.get('EMAIL_FROM')
const LEGACY_MODE = Env.get('LEGACY_MODE') || true
//console.log('LEGACY_MODE', LEGACY_MODE)

export default class OrdersController {
  public async create({ request, auth, params, response }: HttpContextContract) {
    // const sessionUser = await auth.authenticate()

    const LOCALE = request.headers()['x-locale'] || 'ru'
    const _cart = new Cart()

    const { orderItems, sysnotice } = request.all()

    let idField: string, cartId: number

    let cartItems: Array<CartItem> = []

    if (orderItems) {
      let cart = await Cart.create({
        cart_cookie: Buffer.from(randomInteger(3 ** 9, 9 ** 9)).toString('base64')
      })

      idField = 'cart_id'
      cartId = cart.cart_id

      try {
        let _cartItems = await CartItem.updateOrCreateMany(
          'cart_id',
          orderItems.map((item) => ({ qty: item.qty, cart_id: cart.cart_id, prod_id: item.id }))
        )
        cartItems = await CartItem.query().where(idField, cartId).preload('product')
      } catch (error) {
        response.status(500).send('Error processing the list of cart items')
        return false
      }

      //return cartItems
    } else {
      let cart = await _cart.getCartId(request, response, auth)
      // console.log('🚀 ~ OrdersController ~ create ~ cart:', cart)

      idField = cart.idField
      cartId = cart.cartId

      //console.time('await CartItem.query()')
      cartItems = await CartItem.query().where(idField, cartId).preload('product').has('product')
      //console.timeEnd('await CartItem.query()')
    }

    if (!cartItems || !cartItems.length) {
      response.status(404).send('Error: Cart is empty')
      return false
    }

    const forTemplateCartItems = cartItems.map((item) => item.toJSON())

    const cartsum = await _cart.getCartSum(idField, cartId)
    const whosalePrices = cartsum.whosalePrices

    const orderData = request.all()
    //console.time('await Cart.checkStockAvailability')
    const { stockErrors, stockWarns } = await Cart.checkStockAvailability(cartItems)
    //console.timeEnd('await Cart.checkStockAvailability')

    if (stockErrors.length || stockWarns.length) {
      return {
        errors: stockErrors,
        stockWarns
      }
    }

    //-----------------TEST 14.09.22
    // try {
    //     await CartItem.query().where(idField, cartId).delete()
    //     response.clearCookie('_basket')
    // } catch (error) {
    //     console.error('OrdersController.ts::clear cartItems: ', error)
    // }
    //-----------------TEST 14.09.22

    const { bigDiscountStartSum } = _cart.getDiscountMeta()

    let _coupons = {
      discountVal: cartsum.discountValue,
      personal:
        cartsum.personalDiscountValue && cartsum.personalDiscountValue > cartsum.bigDiscountValue
          ? cartsum.personalDiscountValue
          : cartsum.bigDiscount
          ? cartsum.bigDiscountValue
          : 0
    }

    if (request.qs().isTest) {
      return {
        _coupons,
        cartsum,
        whosalePrices,
        cartItems
      }
    }

    const client: Client = await Client.getClient(auth, LEGACY_MODE ? Client.legacyMerge(orderData) : orderData.client)

    if (orderData.client?.passport?.number) {
      try {
        await Client.createOrUpdatePassport(client, orderData.client.passport)
      } catch (error) {
        console.error('OrdersController.ts::create passport: ', error)
      }
    }

    const order = new Order()

    if (whosalePrices) {
      forTemplateCartItems.forEach((item) => {
        item.product.prod_price = item.product.whosaleprice || item.product.prod_price
      })
    }

    await Database.transaction(async (trx) => {
      await order.fillData(orderData, client, cartsum.sum, LOCALE)
      order.useTransaction(trx)

      // if (orderItems) order.order_shippingprice = 0

      order.order_coupons = JSON.stringify(_coupons)
      order.order_notice = sysnotice || ''

      //console.time('await order.save()')
      await order.save()

      console.log('created order: ', {
        ip: request.ip(),
        orderId: order.order_id,
        locale: LOCALE
      })

      try {
        let _weight = await order.orderWeight()

        if (_weight) {
          order.order_weight = _weight
          await order.save()
        }
      } catch (error) {
        console.error('Calc order weight error: ', error)
      }

      //console.timeEnd('await order.save()')

      //-----------------TEST 13.02.23 @@@@@@@@@@@@
      //console.time('Delete cart items')
      try {
        await CartItem.query({ client: trx }).where(idField, cartId).delete()
        response.clearCookie('_basket')
      } catch (error) {
        console.error('OrdersController.ts::clear cartItems: ', error)
      }
      //console.timeEnd('Delete cart items')

      //-----------------TEST 13.02.23

      // try {
      //   Journal.createItem({
      //     entity: 'orders',
      //     entity_id: order.order_id,
      //     user_id: 0,
      //     user_name: client.client_name,
      //     msg: 'Создал заказ'
      //   })
      // } catch (error) {
      //   console.error(error)
      // }

      //-----------------TEST 14.09.22
      let sberData: any = {}

      // if (orderData.payment?.methodName === 'card' && orderData.payment?.payLater == false) {
      //     try {
      //         sberData = await order.sberRegOrder({
      //             cartItems: forTemplateCartItems,
      //             client,
      //             shipping: orderData.shipping
      //         })
      //     } catch (error) {
      //         console.error('OrdersController.ts::sberRegOrder: ', error)
      //     }
      // }

      //-----------------TEST 14.09.22

      //------------ 01.04.25
      let confirmation_token = ''

      if (orderData?.payment?.methodName === 'card' && orderData?.shipping?.shortName === 'Почта РФ') {
        const { YOOKASSA_ACTIVE } = await loadSettings(['YOOKASSA_ACTIVE'])
        // console.log('🚀 ~ OrdersController ~ awaitDatabase.transaction ~ YOOKASSA_ACTIVE:', YOOKASSA_ACTIVE)
        if (YOOKASSA_ACTIVE) {
          const yookassa = new Yookassa()

          try {
            const payData = await yookassa.createPayment({
              amount: { value: yookassa.formatAmount(order.order_price + order.order_shippingprice) }, // currency автоматически будет 'RUB'
              description: `Заказ №${order.order_id}`,
              orderId: String(order.order_id),
              customerEmail: client.client_mail,
              customerName: client.client_name
            })

            confirmation_token = payData.confirmation.confirmation_token
          } catch (error) {
            console.error('OrdersController.ts::yookassa.createPayment: ', error)
          }
        }
      }

      if (order.$isPersisted) response.status(201).send({ orderId: order.order_id, paymentLink: sberData?.formUrl, confirmation_token })
      else response.status(500).send('error')
      //------------
      // @@тестовое СПИСАНИЕ товаров

      //console.time('make product groups')
      // const groups = {}

      // cartItems
      //   .filter((i) => i.product.prod_group)
      //   .map((item) => {
      //     if (!groups[item.product.prod_group]) {
      //       groups[item.product.prod_group] = {
      //         qty: item.qty
      //       }
      //     } else {
      //       groups[item.product.prod_group].qty += item.qty
      //     }
      //   })
      const groups = cartItems
        .filter((i) => i.product.prod_group)
        .reduce((acc, item) => {
          if (!acc[item.product.prod_group]) {
            acc[item.product.prod_group] = {
              qty: item.qty
            }
          } else {
            acc[item.product.prod_group].qty += item.qty
          }
          return acc
        }, {})

      cartItems.map((item) => {
        Journal.createItem({
          entity: 'product',
          entity_id: item.prod_id,
          msg: `Новый заказ #${order.order_id}, Кол-во: ${item.qty}, Наличие: ${item.product.prod_count}`
        })
      })

      //console.timeEnd('make product groups')

      //console.time('update GROUPS')
      // await Promise.all(
      //   sliceIntoChunks(Object.keys(groups), 20).map(async (keyGroup) => {
      //     await Promise.all(
      //       keyGroup.map(async (groupName) => {
      //         console.log('Object.keys ~ groupName:', groupName)
      //         await Database.from('products')
      //           .useTransaction(trx)
      //           .where('prod_group', groupName)
      //           .decrement('prod_count', groups[groupName].qty)
      //           .decrement('prod_group_count', groups[groupName].qty)
      //       })
      //     )
      //   })
      // )
      await Promise.all(
        sliceIntoChunks(Object.keys(groups), 20).map(async (keyGroup) => {
          const promises = keyGroup.map(async (groupName) => {
            await Database.from('products')
              // .useTransaction(trx)
              .where('prod_group', groupName)
              .decrement('prod_count', groups[groupName].qty)
              .decrement('prod_group_count', groups[groupName].qty)
            // //.debug(true)
          })
          await Promise.all(promises)
        })
      )
      //console.timeEnd('update GROUPS')

      // await Promise.all(
      //   Object.keys(groups).map(async (groupName) => {
      //     await Database.from('products').useTransaction(trx).where('prod_group', groupName).decrement('prod_count', groups[groupName].qty).decrement('prod_group_count', groups[groupName].qty)
      //   })
      // )

      // console.log('groups:', groups)
      //console.time('UPDATE other prods')
      await Promise.all(
        sliceIntoChunks(
          cartItems.filter((i) => !i.product.prod_group),
          90
        ).map(async (itemsGroup) => {
          await Promise.all(
            itemsGroup.map(async (item) => {
              await item.product._stock(item.qty, trx).decrement()
            })
          )
        })
      )
      //console.timeEnd('UPDATE other prods')

      // await Promise.all(
      //   cartItems
      //     .filter((i) => !i.product.prod_group)
      //     .map(async (item) => {
      //       await item.product._stock(item.qty, trx).decrement()
      //     })
      // )

      //  console.log('BEFORE OrdersController ~ cartItems.map ~ item', { sku: item.product.prod_sku, prod_count: item.product.prod_count, id: item.product.prod_id })

      //console.time("await order.related('items').createMany")
      await order.related('items').createMany(cartItems.map((item) => ({ item_count: item.qty, item_id: item.product.prod_id })))
      //console.timeEnd("await order.related('items').createMany")

      //console.time('await trx.commit()')
      await trx.commit()
      //console.timeEnd('await trx.commit()')

      // let sberData: any = {}

      // if (orderData.payment?.methodName === 'card' && orderData.payment?.payLater == false) {
      //     //console.log('generate sber pay order');

      //     try {
      //         sberData = await order.sberRegOrder({
      //             cartItems: forTemplateCartItems,
      //             client,
      //             shipping: orderData.shipping
      //         })
      //     } catch (error) {
      //         console.error('OrdersController.ts::sberRegOrder: ', error)
      //     }
      // }

      // if (order.$isPersisted) response.status(201).send({ orderId: order.order_id, paymentLink: sberData?.formUrl })
      // else response.status(500).send('error')

      // try {
      //     await CartItem.query().where(idField, cartId).delete()
      //     response.clearCookie('_basket')
      // } catch (error) {
      //     console.error('OrdersController.ts::clear cartItems: ', error)
      // }
    })

    console.log('start create snapshot')

    //console.time('OrderSnapshot.makeInitSnap')
    try {
      // @@СНЭПШОТ , 'NA', client
      await OrderSnapshot.makeInitSnap({
        orderId: order.order_id,
        snapItems: cartItems,
        clientData: client,
        coupons: _coupons,
        cartsum
      })
    } catch (error) {
      console.error('SNAPSHOT ERROR: ', error)
    }
    //console.timeEnd('OrderSnapshot.makeInitSnap')

    if (LOCALE != 'ru') {
      let setting = await loadSettings(['currency'])
      let currencies = setting.currency

      let crnc = currencies[LOCALE == 'pl' ? 'zl' : 'eur']

      forTemplateCartItems.forEach((item) => {
        try {
          item.product.prod_price = Number((item.product.prod_price / crnc).toFixed(2))
        } catch (error) {
          console.error('productsChangeCurrency: ' + item.product.product, error)
        }
      })

      order.order_shippingprice = Number((order.order_shippingprice / crnc).toFixed(2))
      order.order_price = Number((order.order_price / crnc).toFixed(2))
    }

    if (Application.inProduction) {
      let mailer = LOCALE == 'ru' ? 'smtp' : 'rumi'
      try {
        Mail.use(mailer).send((message) => {
          message
            .from(LOCALE == 'ru' ? EMAIL_FROM : Env.get('RUMI_SMTP_FROM', '<EMAIL>'), LOCALE === 'ru' ? Env.get('EMAIL_FROM_NAME') : '')
            .to(LOCALE == 'ru' ? EMAIL_FROM : Env.get('RUMI_SMTP_FROM', '<EMAIL>'))
            .subject(LOCALE == 'ru' ? 'Новый заказ' : 'RUMISOTA Новый заказ' + ' #' + order.order_id)
            .htmlView(LOCALE != 'ru' ? 'mail_neworder_en' : 'mail_neworder', {
              locale: LOCALE,
              order,
              client,
              cartItems: forTemplateCartItems,
              whosalePrices,
              discountValue: cartsum.discountValue,
              clientIP: request.ip()
            })
        })
      } catch (error) {
        console.error('Mail.send::mail_neworder: ', error)
      }

      try {
        let htmlViewName = LOCALE == 'ru' ? 'mail_orderdone' : 'mail_orderdone_' + LOCALE

        Mail.use(mailer).send((message) => {
          message
            .from(LOCALE == 'ru' ? EMAIL_FROM : '<EMAIL>', LOCALE === 'ru' ? Env.get('EMAIL_FROM_NAME') : '')
            .to(client.client_mail)
            .subject(LOCALE == 'ru' ? 'Заказ' : 'Order' + ' №' + order.order_id)
            .htmlView(htmlViewName, { locale: LOCALE, order, client, cartItems: forTemplateCartItems, whosalePrices, discountValue: cartsum.discountValue })
        })
      } catch (error) {
        console.error('Mail.send::mail_orderdone: ', error)
      }
    }

    try {
      await Promise.all(
        cartItems
          // .filter((i) => !i.product.prod_rk)
          .map((item) => {
            Journal.createItem({
              entity: 'product',
              entity_id: item.prod_id,
              msg: `Новый заказ #${order.order_id}, Кол-во: ${item.qty}, Наличие: ${item.product.prod_count}`
            })
          })
      )
    } catch (error) {
      console.error('Ошибка записи журнала:', error)
    }
  }

  public async list({ auth, request, params }: HttpContextContract) {
    const locale = request.headers()['x-locale'] || undefined
    const user = await auth.authenticate()
    console.log('🚀 ~ OrdersController ~ list ~ user:', user)
    const { clientProductDBfields } = Product.getProductColumns()

    let parsedQS: any = parseQS(request.qs().qs)

    let { page, debug } = request.qs()
    let limit = parsedQS && typeof parsedQS.limit !== 'undefined' ? parsedQS.limit : 4

    let orderByParams = {
      key: 'order_id',
      ad: 'desc'
    }

    let searchValue = ''

    const orderSearchFields = ['order_id', 'order_price', 'order_shippingprice', 'order_shipping', 'order_desc', 'order_payment', 'order_tracknumber']

    page = !page || page == 0 ? 1 : page

    if (parsedQS && parsedQS.ad && parsedQS.key) {
      orderByParams.key = parsedQS.key
      orderByParams.ad = parsedQS.ad
    }

    if (parsedQS && parsedQS.searchvalue) {
      try {
        searchValue = parsedQS.searchvalue.replace(/\s*/gm, '')
      } catch (error) {
        searchValue = parsedQS.searchvalue
      }
    }

    let orders: any[]

    if (searchValue) {
      orders = await Order.query()
        .where('order_client', user.client_id)
        .leftJoin('order_items', 'orders.order_id', 'order_items.items_order_id')
        .leftJoin('products', 'order_items.item_id', 'products.prod_id')
        .whereRaw(`CONCAT(${clientProductDBfields.join(',')}) LIKE '%${searchValue}%'`)
        .orWhereRaw(`CONCAT(${orderSearchFields.join(',')}) LIKE '%${searchValue}%'`)
        //.whereHas('snapshots', query => query.where('body', 'like', `%${searchValue}%`))
        .debug(debug)
        .orderBy(orderByParams.key, orderByParams.ad)
        .paginate(page, limit)
    } else {
      orders = await Order.query().where('order_client', user.client_id).orderBy(orderByParams.key, orderByParams.ad).debug(debug).paginate(page, limit)
    }

    return orders
  }

  // return order items by last snapshot
  public async items({ auth, request, params, response }) {
    const locale = request.headers()['x-locale'] || undefined
    const user = await auth.authenticate()
    const { id } = params

    const snapshot = await OrderSnapshot.query().select('orderid', 'body').where('orderid', id).orderBy('ID', 'desc').first()

    if (locale && locale != 'ru' && snapshot?.body?.items) {
      await LangDict.prodsTranslate(snapshot.body.items)
    }

    if (!snapshot) {
      return response.status(404)
    }

    try {
      return snapshot
    } catch (error) {
      throw new Error('OrdersController.ts > items: ' + error)
    }
  }

  public async cpanItems({ auth, request, params, response }: HttpContextContract) {
    const locale = request.headers()['x-locale'] || undefined
    const user = await auth.use('api').authenticate()
    const { id } = params

    const snapshot = await OrderSnapshot.query().select('orderid', 'body').where('orderid', id).orderBy('ID', 'desc').first()

    if (locale && locale != 'ru' && snapshot?.body?.items) {
      await LangDict.prodsTranslate(snapshot.body.items)
    }

    if (!snapshot) {
      return response.status(404)
    }

    try {
      return snapshot
    } catch (error) {
      throw new Error('OrdersController.ts > items: ' + error)
    }
  }

  public async checkList({ auth, request, params, response }: HttpContextContract) {
    const toEmail = request.qs().toEmail || false
    const isRumi = request.qs().isRumi || false

    return await Order.checkList({ toEmail, isRumi })
  }

  public async cpanMerge({ auth, request, params, response }: HttpContextContract) {
    const user = await auth.use('api').authenticate()

    const trx = await Database.transaction()

    type PayloadParams = { ids: number[] | string }
    let { ids }: PayloadParams = request.all()

    try {
      if (typeof ids === 'string') {
        ids = ids
          .split(',')
          .filter((i) => i)
          .map((i) => i.trim())
      }
    } catch (error) {}

    if (!ids || !Array.isArray(ids)) {
      throw new Error('cpanMerge: ids not is array', ids)
    }

    console.log(' ids:', ids)

    const mainOrderId = ids?.shift()
    // console.log('🚀 ~ OrdersController ~ cpanMerge ~ mainOrderId:', mainOrderId)

    if (!mainOrderId) {
      throw new Error('cpanMerge: !mainOrderId')
    }

    const orders = await await Database.from('orders').whereIn('order_id', ids)

    if (orders.some((order) => order.order_status == 'Отменен')) {
      throw new Error('Заказы со статусом "ОТМЕНЕН" не могут быть объеденины.')
    }

    const allItems = await Database.from('order_items')
      .useTransaction(trx)
      .select('*')
      .sum('item_count as sum_count')
      .whereIn('items_order_id', [...ids, mainOrderId])
      .groupBy('item_id')

    allItems.forEach((item) => {
      item.items_order_id = mainOrderId
      item.item_count = item.sum_count

      delete item.sum_count
      delete item.created_at
      delete item.updated_at
      delete item.timestamp
    })

    await Database.from('order_items')
      .useTransaction(trx)
      .whereIn('items_order_id', [...ids, mainOrderId])
      .delete()

    const updateOrCreateManyItems = await OrderItem.updateOrCreateMany(['ID', 'item_id'], allItems, {
      client: trx
    })

    // console.log('updateOrCreateManyItems:', [...updateOrCreateManyItems])

    await Database.from('orders').useTransaction(trx).whereIn('order_id', ids).update({ order_status: 'Отменен', order_price: 0, order_shippingprice: 0 })

    await Database.from('orders')
      .useTransaction(trx)
      .where('order_id', mainOrderId)
      .update({
        'order_status': 'Не обработан',
        'order_notice': `ЗАКАЗ был объединен с #(${ids})'`,
        'order_desc': `ЗАКАЗ был объединен с #(${ids})'`
      })

    await Database.from('orders')
      .useTransaction(trx)
      .whereIn('order_id', ids)
      .update({
        'order_status': 'Отменен',
        'order_notice': `ЗАКАЗ был объединен с #${mainOrderId}'`,
        'order_desc': `ЗАКАЗ был объединен с #${mainOrderId}'`
      })

    //TODO: // не коммитить транзакцию до создания снапшотов
    await trx.commit()

    await Promise.all(
      [...ids, mainOrderId].map(async (orderId) => {
        const _order = await Order.query()
          .where('order_id', orderId)
          .preload('snapshots', (query) => {
            query.orderBy('ID', 'desc')
          })
          .preload('items', (query) => query.preload('product'))
          .firstOrFail()

        const order = _order.toObject()

        const lastSnaphotBody: SnapshotBodyInterface = order.snapshots[0].body

        const snapItems = order.items.map((item) => {
          const prevState = lastSnaphotBody.items.find((x) => x.item_id == item.item_id)

          const updItem = {
            ...item,
            ...item.product,
            prod_price: prevState?.prod_price || item.product.prod_price,
            prod_discount: prevState?.prod_discount || item.product.prod_discount
          }

          delete updItem.product

          return updItem
        })

        lastSnaphotBody.items = snapItems

        if (orderId !== mainOrderId) {
          lastSnaphotBody.order_price = 0
          lastSnaphotBody.order_shippingprice = 0
        }

        lastSnaphotBody.order_notice = order.order_notice
        lastSnaphotBody.order_desc = order.order_desc
        lastSnaphotBody.order_status = order.order_status

        // console.log('final snapItems: ', snapItems)

        // console.log('order state: ', { order_notice: order.order_notice, order_desc: order.order_desc, order_status: order.order_status })
        // console.log('final state snap: ', { order_notice: lastSnaphotBody.order_notice, order_desc: lastSnaphotBody.order_desc, order_status: lastSnaphotBody.order_status })

        const newSnapshot: Snapshot = {
          orderid: order.order_id,
          body: JSON.stringify(lastSnaphotBody),
          user: user.user_name
        }

        // console.log('🚀 ~ OrdersController ~ [...ids,mainOrderId].map ~ newSnapshot:', newSnapshot)

        await OrderSnapshot.create(newSnapshot, {
          // client: trx
        })
      })
    )

    // await trx.commit()

    return { isOk: true }
  }

  public async cpanSave({ auth, request, params, response }: HttpContextContract) {
    const user = await auth.use('api').authenticate()
    // const trx = await Database.transaction()

    const newOrderData: SnapshotBodyInterface = request.body() as SnapshotBodyInterface

    if (newOrderData.client.org) {
      // legacy support
      newOrderData.client['client_company'] = newOrderData.client.org
    }

    const orderState = await Order.query()
      .where('order_id', newOrderData.order_id)
      .preload('snapshots')
      .preload('items', (query) => query.preload('product'))
      .firstOrFail()

    const oldGtdState = orderState.order_gtd

    // if (newOrderData.order_status == 'Удален' && orderState.order_status != 'Удален') {
    //   if (orderState.order_status != 'Отменен') {
    //       // удалить отмененный заказ
    //   } else {
    //     // вернуть сток и удалить
    //   }
    // }

    const returnStockStatuses = ['Отменен', 'Удален']

    if (returnStockStatuses.some((s) => s == newOrderData.order_status)) {
      newOrderData.items = []
      newOrderData.order_price = 0
      newOrderData.order_shippingprice = 0
    }

    const nonUpdatableOrderFields: Array<keyof Order> = ['order_datetime', 'client', 'snapshots', 'order_id', 'items']

    getModelKeys(Order).map((orderColumn) => {
      if (typeof newOrderData[orderColumn] !== 'undefined' && !nonUpdatableOrderFields.includes(orderColumn)) {
        orderState[orderColumn] = newOrderData[orderColumn]
      }
    })

    // orderState.useTransaction(trx)

    // if (!orderState.$dirty['order_weight']) {
    //   orderState.order_weight = await orderState.orderWeight()
    //   newOrderData.order_weight = orderState.order_weight
    // }

    await orderState.save()

    const results = await Product.updateOrderItems(newOrderData.items, orderState.items, newOrderData.order_id)

    if (results.stockUpdateErrors) {
      return results
    }

    // const _order = await Order.query().where('order_id', orderState.order_id).preload('items', q => q.preload('product')).firstOrFail()
    // const currentOrderItems = newOrderData.items //await OrderItem.query().where('items_irder_id', orderState.order_id).preload('product')

    if (newOrderData.order_status == 'Удален') {
      // await Order.query().where('order_id', orderState.order_id).del()
      await orderState.delete()
      await OrderItem.query().where('items_order_id', orderState.order_id).del()
      await OrderSnapshot.query().where('orderid', orderState.order_id).del()

      if (orderState.$isDeleted) {
        return response.status(200).send({ isOk: true })
      } else {
        return response.status(500).send({ isOk: false })
      }
      // TODO: notification!
    }

    const snapOrderItems: SnapshotBodyItemInterface[] = newOrderData.items.map((item) => {
      delete item.currentState
      return item
    })

    const snapshot: Snapshot = {
      orderid: orderState.order_id,
      body: JSON.stringify({
        ...newOrderData,
        items: snapOrderItems
      }),
      // date: DateTime.now().toSQL(),
      user: user.user_name
    }

    await OrderSnapshot.create(
      snapshot /*{
      client: trx
    }*/
    )

    if (oldGtdState != newOrderData.order_gtd) {
      const gtd = new Gtd()

      const _orderItems = await OrderItem.query().where('items_order_id', orderState.order_id).preload('product')

      await gtd.reserveGtd({
        action: oldGtdState < newOrderData.order_gtd, // action ? await _decrement(списание) : await _increment(возврат, отмена)
        orderId: orderState.order_id,
        reserveList: _orderItems.map((i) => {
          return {
            ...i.product,
            orderCount: i.item_count
          }
        })
      })
    }

    return results
    // 1. | Обновить все поля в таблице orders
    // 2. | СОздать снапшот (удалить лишнее)
    // 3. | Обновить наличие у товаров
    // 4. | Обновить таблицу orderItems
    // 5. | Транзакция

    return 'ok'
  }

  public async checkFullGtd({ auth, request, params, response }: HttpContextContract) {
    const { id } = params
    // const user = await auth.use('api').authenticate()

    return await Order.checkFullGtd(id)
  }
  public async cpanId({ auth, request, params, response }: HttpContextContract) {
    const { id } = params
    const user = await auth.use('api').authenticate()

    const res = await Order.cpanOrderByID(id)

    if (!res) {
      return response.status(404)
    }

    res.data.fullGtd = await Order.checkFullGtd(id)

    return res
  }

  public async orderPrice({ auth, request, params }: HttpContextContract) {
    const { id } = params
    const user = await auth.use('api').authenticate()

    return await Order.getOrderPrice(id)
  }

  public async cpanList({ auth, request, params }: HttpContextContract) {
    // const user = await auth.use('api').authenticate()

    // try {
    //     Journal.createItem({
    //         entity: 'orders',
    //         entity_id: 0,
    //         msg: 'просмотр заказов'
    //     })
    // } catch (error) {   }
    // const user = await auth.use('api').authenticate()

    let {
      page = 1,
      limit = 10,
      searchvalue = '',
      filters,
      sortField = 'order_id',
      sortOrder = 'desc',
      debug = false,
      deepsearch = false,
      showСanceled = false,
      isRumi = false,
      checkFullGtd = false
    } = request.qs()

    deepsearch = deepsearch == 'true'

    let filtersActivated = false

    if (filters) {
      try {
        filters = JSON.parse(filters)
        if (Object.keys(filters).length) filtersActivated = true
      } catch (error) {
        console.log('error parse filters: ', error)
        filtersActivated = false
      }
    }

    // console.log('cpanList parsed FILTERS: ', filters)

    const orderColumns = getModelColumns(Order)
    const snapshotColumns = getModelColumns(OrderSnapshot)

    let extandedQueries = parseExtandedQueries(searchvalue || '')

    // const rres = await Database.raw()

    const res = await Order.query()
      .if(searchvalue, (builder) => {
        builder.leftJoin('orders_snapshots', 'orders.order_id', 'orders_snapshots.orderid')
        builder
          .where((snapquery) => {
            if (extandedQueries.length > 1) {
              extandedQueries.map((q) => {
                snapquery.where('orders_snapshots.body', 'like', `%${q}%`)
              })
            } else if (extandedQueries.length > 0) {
              snapquery.where('orders_snapshots.body', 'like', `%${extandedQueries[0]}%`)
            }

            snapquery.if(!deepsearch, (query) => {
              query.where((subquery) => {
                subquery.whereBetween('order_datetime', [DateTime.now().minus({ months: 2 }).toSQL(), DateTime.now().toSQL()])
              })
            })
          })
          .if(!deepsearch, (query) => {
            query.where((subquery) => {
              subquery.whereBetween('order_datetime', [DateTime.now().minus({ months: 2 }).toSQL(), DateTime.now().toSQL()])
              // .orWhereIn('order_status', ['Не обработан', 'В ожидании оплаты'])
            })
          })
      })
      .if(filtersActivated, (query) => {
        Object.keys(filters).map((key) => {
          let filter = filters[key]
          maskBuilder.query(query)[filter.matchMode](filter.value, key)
        })
      })
      .if(!showСanceled, (query) => query.andWhere('order_status', '!=', 'Отменен'))
      .preload('snapshots', (query) => {
        query.orderBy('ID', 'desc')
      })
      .if(isRumi, (query) => {
        query.andWhere('order_locale', '!=', 'ru')
      })
      .if(searchvalue, (query) => {
        query.groupBy('orders.order_id')
      })
      .orderBy(sortField, sortOrder)
      //.debug(true)
      .paginate(page || 1, limit > 500 ? 500 : limit)

    res.map((item) => {
      item.order_datetime = item.order_datetime.setLocale('ru').toFormat('EEE, dd LLL HH:mm:ss') //.toFormat('dd.LL.yy HH:mm') //.toFormat('dd LLL yyyy TT')
    })

    const serializedOrders = res.serialize()

    if (checkFullGtd) {
      let data = []

      // console.log('start check full GTD...')
      //console.time('check full GTD')

      await Promise.all(
        serializedOrders.data.map(async (order) => {
          order.fullGtd = await Order.checkFullGtd(order.order_id)
          // console.log('🚀 ~ OrdersController ~ serializedOrders.data.map ~ order.fullGtd:', order.fullGtd)
          // order.$setAttribute('fullGtd', order.fullGtd)

          data.push({ ...order })
        })
      )

      //console.timeEnd('check full GTD')

      serializedOrders.data = data
    }

    return {
      orders: serializedOrders, //res,
      orderColumns: orderColumns.map((i) => i.split('.')[1]).filter((i) => i)
    }
  }
}
