import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
//import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import Env from '@ioc:Adonis/Core/Env'
import Product from 'App/Models/Product'
import Cart from 'App/Models/Cart'
import Category from 'App/Models/Category'

import { buildBySizeQuery } from 'App/Helpers/buildBySizeQuery'
import { searchValueHandler } from 'App/Helpers/searchValueHandler'
import { setProdCartQty } from 'App/Helpers/setProdCartQty'
import Database, { DatabaseQueryBuilderContract, DatabaseQueryBuilderSelect } from '@ioc:Adonis/Lucid/Database'
import LangDict from 'App/Models/LangDict'
import { parseExtandedQueries } from 'App/Helpers/parseExtandedQueries'
import { ModelQueryBuilderContract } from '@ioc:Adonis/Lucid/Orm'
import { maskBuilder } from 'App/Helpers/makeFilterMask'
import loadSettings from 'App/Helpers/loadSettings'
import Journal from 'App/Models/Journal'
import ProductSchema from 'App/Models/ProductSchema'
import User from 'App/Models/User'
import { getModelKeys } from 'App/Helpers/getModelColumns'
import Order from 'App/Models/Order'
import ProductSnapshot from 'App/Models/ProductSnapshot'
import Lock from 'App/Models/Lock'
import Application from '@ioc:Adonis/Core/Application'
//import { SimplePaginatorContract } from '@ioc:Adonis/Lucid/DatabaseQueryBuilder'

const prodIdField = 'prod_id'

export default class ProductsController {
  public async deleteSchema({ request, params, response, auth }: HttpContextContract) {
    const user: User = await auth.use('api').authenticate()

    const { id } = params

    const schema = await ProductSchema.findBy('product_id', id)

    return await schema?.delete()
  }
  public async createSchema({ request, params, response, auth }: HttpContextContract) {
    const user: User = await auth.use('api').authenticate()

    const { id, body } = request.body()
    const product = await Product.findOrFail(id)

    const existSchema = await ProductSchema.query().where('product_id', id).orWhereRaw('FIND_IN_SET(?, related_ids) > 0', [id]).first()

    if (existSchema) {
      existSchema.body = JSON.stringify(body)
      await existSchema.save()

      return existSchema.id
    }

    const schema = await ProductSchema.create({
      product_id: product.prod_id,
      body: JSON.stringify(body)
    })

    Journal.createItem({
      entity: 'products',
      entity_id: id,
      msg: 'Создана схема товара',
      user_id: user.user_id,
      user_name: user.user_name
    })

    return schema.id
  }

  public async delete({ request, params, response, auth }: HttpContextContract) {
    const user: User = await auth.use('api').authenticate()

    const { id } = request.params()

    const { mainProductId } = request.all() // merge mode

    const product = await Product.findOrFail(id)

    // Если указан mainProductId, обновляем все заказы
    if (mainProductId) {
      const mainProduct = await Product.findOrFail(mainProductId)

      // Обновляем все order_items, заменяя удаляемый товар на основной
      await Database.from('order_items').where('item_id', id).update({
        item_id: mainProductId
      })

      // // Обновляем количество проданного у основного товара
      // const totalSold = await Database.from('order_items').where('item_id', mainProductId).sum('item_count as total').first()

      // if (totalSold?.total) {
      //   mainProduct.prod_sold = Number(totalSold.total)
      //   await mainProduct.save()
      // }

      Journal.createItem({
        entity: 'products',
        entity_id: id,
        msg: `Товар объединен с ${mainProduct.prod_analogsku}, история продаж перенесена`,
        user_id: user.user_id,
        user_name: user.user_name
      })
    }

    await product.delete()

    Journal.createItem({
      entity: 'products',
      entity_id: id,
      msg: 'удаление товара',
      user_id: user.user_id,
      user_name: user.user_name
    })

    if (product.$isDeleted) {
      return {
        isOk: true,
        product
      }
    } else {
      response.status(500)
    }
  }

  public async getSnaphots({ request, params, response, auth }: HttpContextContract) {
    const sessionUser = await auth.use('api').authenticate()
    const { prod_id } = await request.all()

    return await ProductSnapshot.getSnapshots(prod_id)
  }

  public async update({ request, params, response, auth }: HttpContextContract) {
    let sessionUser: User | undefined = undefined

    if (Application.inProduction) {
      sessionUser = await auth.use('api').authenticate()
    }

    const productModelFields = getModelKeys(Product)

    const { prod_id = null, fields, updateGroupFields } = request.all()
    // console.log('🚀 ~ file: ProductsController.ts:113 ~ update ~ fields:', fields)
    // console.log('🚀 ~ file: ProductsController.ts:113 ~ update ~ prod_id:', prod_id)

    let product: Product | null = null //await Product.updateOrCreate({ prod_id }, fields)

    if (prod_id) {
      product = await Product.find(prod_id)
    } else {
      const existProduct = await Product.query()
        .select('prod_id', 'prod_analogsku')
        .where({ prod_analogsku: String(fields.prod_analogsku).trim() })
        .andWhere((query) => {
          query.where('prod_group', '').orWhereNull('prod_group')
        })
        .first()
      if (existProduct) {
        return response.status(502).send({ message: 'Товар с таким OEM уже есть' })
      }
    }

    if (!product) {
      product = new Product()
    }

    // if (product.prod_id && sessionUser?.user_id) {

    //   const lock = await Lock.query()
    //     .where({
    //       entity: 'products',
    //       entity_id: product.prod_id
    //     })
    //     .first()

    //   if (lock) {
    //     if (String(lock.user_id) != String(sessionUser.user_id)) {
    //       return response.status(500).json({ message: `Товар открыт на редактирование: ${lock.user_name} | ${lock.createdAt}` })
    //     }
    //   }
    // }

    Object.keys(fields)
      .filter((field) => !productModelFields.includes(field))
      // .filter((field) => field ?? false)
      .map((field) => delete fields[field])

    Object.keys(fields).forEach((key) => {
      if (fields[key] === null || fields[key] === undefined) {
        delete fields[key]
      }
    })

    for (const key in fields) {
      if (Array.isArray(fields[key])) {
        fields[key] = fields[key].join()
      }
    }

    product.merge(fields)

    // generate photos

    try {
      product.generatePhoto({ rti: true })
    } catch (error) {
      ('ProductController.update.rti ~ error:', error)
    }

    try {
      product.generatePhoto({ rumi: true })
    } catch (error) {
      console.log('ProductController.update.rti ~ rumi:', error)
    }

    // generate videos

    try {
      product.generateVideo({ rti: true })
    } catch (error) {
      console.log('ProductController.update.generateVideo.rti  ~ error:', error)
    }

    try {
      product.generateVideo({ rumi: true })
    } catch (error) {
      console.log('ProductController.update.generateVideo.rumi ~ error:', error)
    }

    await product.save()

    if (product.prod_group) {
      await Product.query().where('prod_group', product.prod_group).andWhereNot('prod_id', product.prod_id).update({
        prod_group_count: product.prod_count,
        prod_count: product.prod_count
      })
    }

    if (updateGroupFields?.length) {
      const updateGroupPayload = {}
      updateGroupFields.map((field) => (updateGroupPayload[field] = product[field]))
      await Product.query().where('prod_group', product.prod_group).andWhereNot('prod_id', product.prod_id).update(updateGroupPayload)
    }

    await Journal.createItem({
      entity: 'products',
      entity_id: product.prod_id,
      msg: product.$isNew ? `создан товар` : `обновлен товар`
    })

    return {
      isOk: true,
      product
    }
  }

  public async cpanCatalog({ request, params, response, auth }) {
    //const sessionUser = auth.authenticate()

    const locale = request.headers()['x-locale'] || undefined
    const client = Database.connection()
    const columnsInfo = await client.columnsInfo('products')
    const columns = Object.keys(columnsInfo)

    //console.log('columns', columns)

    let _qs = request.qs()
    //console.log('_qs', _qs)

    Object.keys(_qs).map((key) => {
      if (_qs[key] == 'null' || _qs[key] == 'undefined') {
        delete _qs[key]
      }
    })

    let { page = 1, limit = 200, sortField = 'prod_id', sortOrder = 'desc', searchvalue = '', filters } = _qs
    let filtersActivated = false
    let extandedQueries = parseExtandedQueries(String(searchvalue))

    if (!page || page == '0') {
      page = 1
    }

    try {
      filters = JSON.parse(filters)
      if (Object.keys(filters).length) filtersActivated = true
    } catch (error) {
      // console.log('error parse filters: ', error)
      filtersActivated = false
    }

    //console.log('parsed FILTERS: ', filters)

    const orderRaw = Product._orderBySize(sortOrder)
    const products = await Product.query()
      .if(filtersActivated, (query) => {
        Object.keys(filters).map((key) => {
          let filter = filters[key]
          maskBuilder.query(query)[filter.matchMode](filter.value, key)
        })
      })
      .andWhere((builder) => {
        if (extandedQueries.length > 1) {
          extandedQueries.map((q) => {
            builder.whereRaw(`CONCAT(${columns.join(',')}) LIKE ?`, [`%${q}%`])
          })
        } else if (extandedQueries.length > 0) {
          columns.map((field) => builder.orWhere(field, 'like', `%${extandedQueries[0]}%`))
        }
      })
      .if(sortField == 'prod_size', (query) => query.orderByRaw(orderRaw))
      .if(sortField != 'prod_size', (query) => query.orderBy(sortField, sortOrder))
      .preload('lock')
      // //.debug(true)
      .paginate(page, limit)

    return {
      products,
      columns
    }
  }

  public async catalog({ request, params, response, auth }) {
    // const sessionUser = auth.authenticate()

    const locale = request.headers()['x-locale'] || undefined

    const _cart = new Cart()

    const categoryField: any = Env.get('PROD_MODEL_CATEGORY_FIELD', 'prod_cat')
    const sortByStockCats: string[] = String(Env.get('SORT_BY_STOCK_CATS')).split(',')

    const sizeField: string = 'prod_size'
    const categoryUrlField = 'cat_url_' + (locale ? locale : 'ru')
    //console.log('categoryUrlField', categoryUrlField)
    const { orderByStockField, orderByStockQuery } = Product.orderByStock()

    const category: string = params.category || 1
    let page = request.requestQs.page || 1 //params.page || 1

    Number(page) <= 0 ? (page = 1) : ''

    _cart.checkCookie({ response, request })
    const { idField, cartId } = await _cart.getCartId(request, response, auth)

    let categoryMeta: Category = await Category.query()
      .where((query) => query.where('cat_id', category).orWhere(categoryUrlField, category))
      .andWhere('duplicate', false)
      // //.debug(true)
      .first()

    if (!categoryMeta) {
      return response.status(404)
    }

    let { productDBfields, clientProductDBfields, orderRaw, filters, limit } = Product.getParams(request, categoryMeta.cat_id)
    // console.log("🚀 ~ ProductsController ~ catalog ~ filters:", filters)
    let { searchBySize, sizeIn, sizeOut, sizeHeight, sizeTollerance } = searchValueHandler({ value: '', productDBfields, sizeField }, filters)
    // console.log('searchBySize', searchBySize)

    let columns: Array<object> = [],
      categoryFilters: Array<object> = [],
      subcategories: Array<Category> = [],
      fetchedSubcategories: any = []

    let productsPaginator = await Product.query()
      .select(clientProductDBfields.filter((i) => i != 'products.prod_images'))
      .if(sortByStockCats.includes(String(categoryMeta.cat_id)), (query) => query.select(Database.raw(orderByStockQuery)))
      .where((builder) => {
        builder.where(categoryField, categoryMeta.cat_id)
        builder.orWhereRaw(`find_in_set(?, prod_morecats)`, [categoryMeta.cat_id])
      })
      .andWhere((builder) => {
        buildBySizeQuery({ builder, sizeField, sizeIn, sizeOut, sizeHeight, sizeTollerance, searchBySize })
      })
      .andWhere((builder) => {
        let fKeys = Object.keys(filters)
        if (fKeys.length > 0) fKeys.map((key) => builder.andWhereIn(key, filters[key]))
      })
      .if(sortByStockCats.includes(String(categoryMeta.cat_id)), (query) => query.orderBy(orderByStockField, 'desc'))
      .orderByRaw(orderRaw)
      .paginate(page, limit)

    let productsData = productsPaginator.toJSON()
    let products = productsData.data.map((item) => item.toJSON())

    // перенести в модель выборку столбцов

    if (products.length > 0) {
      if (locale && locale != 'ru') {
        await LangDict.prodsTranslate(products, locale)
      }

      let [_columns, _categoryFilters] = await Promise.all([
        Category.getCategoryColumns(categoryMeta.cat_id),
        Category.getCategoryFilters(categoryMeta.cat_id),
        setProdCartQty(idField, cartId, products)
      ])

      columns = _columns
      categoryFilters = _categoryFilters
    } else {
      let [_subcategories, _categoryFilters] = await Promise.all([
        Category.query().where('cat_rootcat', categoryMeta.cat_id).andWhere('cat_active', 1).orderBy('cat_sort'),
        Category.getCategoryFilters(categoryMeta.cat_id)
      ])

      subcategories = _subcategories
      categoryFilters = _categoryFilters

      if (locale) {
        subcategories.map((category) => {
          if (category.$extras['cat_url_' + locale]) {
            category.cat_url = category.$extras['cat_url_' + locale]
          }
        })

        await LangDict.categoriesTranslate(subcategories, locale)
      }

      fetchedSubcategories = subcategories.map((i) => {
        i.toObject()
        !i.cat_base64_pic ? (i.cat_base64_pic = i.$extras.cat_base64_pic) : ''
        return i
      })
      //console.log('fetchedSubcategories', fetchedSubcategories)
      //await Promise.all(fetchedSubcategories.map(async i => i.fullurl = await Category.getFullUrl(i)))
    }

    let withSize = false

    if (categoryFilters.length) {
      let bySizeIndex = categoryFilters.findIndex((x) => x.field == 'bysize')
      if (bySizeIndex > -1) {
        categoryFilters.splice(bySizeIndex, 1)
        withSize = true
      }
    }

    return { products: { data: products, meta: productsData.meta }, columns, categoryMeta, filterslist: categoryFilters, subcategories: fetchedSubcategories, withSize }
  }

  public async cpanOrderByProduct({ request, params, response, auth }: HttpContextContract) {
    // const sessionUser = auth.authenticate()

    const { id } = params
    const product = await Product.findOrFail(id)

    const orderItems = await Database.from('order_items')
      .select('items_order_id as order_id', 'item_count as qty')
      // .distinct('items_order_id as order_id', 'item_count as qty')
      .where('item_id', product.prod_id)
      .orderBy('timestamp', 'desc')

    // const orders = await Order.query()
    //   .whereIn(
    //     'order_id',
    //     orderItems?.map((i) => i.order_id)
    //   )
    //   .andWhere('order_status', '!=', 'Отменен')
    //   .preload('client', query => query.select('client_name', 'client_mail', 'client_id'))
    //   // //.debug(true)

    const oorders = await Database.from('orders')
      .leftJoin('clients', 'orders.order_client', 'clients.client_id')
      .select('clients.client_id', 'clients.client_name', 'orders.order_id', 'orders.order_datetime')
      .whereIn(
        'orders.order_id',
        orderItems?.map((i) => i.order_id)
      )
      .andWhere('orders.order_status', '!=', 'Отменен')
      .orderBy('order_datetime', 'desc')

    // await Promise.all(orders.map(async (order) => {
    //   await order.load('client')
    // }))

    const data = oorders.map((order) => {
      const fd = orderItems.find((x) => x.order_id == order.order_id)
      if (fd) {
        return {
          date: order.order_datetime.toLocaleString(),
          id: order.order_id,
          qty: fd.qty,
          client: {
            name: order.client_name,
            id: order.client_id
          }
          // client: order.client
        }
      }
    })

    return data
  }

  public async cpanProduct({ request, params, response, auth }: HttpContextContract) {
    const { id } = params
    const product = await Product.findOrFail(id)

    await Promise.all([
      await product.load('lock'),
      await product.load('category'),
      await product.load('statlist', (q) => q.where('manual', true))
      // await product.load('schema')
      // await product.load('orders')
    ])

    const { rk } = await product.getRkСomposition()

    // product.$setAttribute('rk_meta', rk)

    const data = product.toJSON()
    const schema = await ProductSchema.query().where('product_id', id).orWhereRaw('FIND_IN_SET(?, related_ids) > 0', [id]).first()
    //
    if (schema) {
      data.schema = schema
    }

    data.rk = rk

    // return product
    return data
  }

  public async id({ request, params, response, auth }) {
    // const sessionUser = auth.authenticate()

    const locale = request.headers()['x-locale'] || undefined

    const _cart = new Cart()

    const { clientProductDBfields } = Product.getProductColumns()
    const { idField, cartId } = await _cart.getCartId(request, response, auth)
    const { id } = params

    const productData = await Product.query()
      .leftOuterJoin('cats', 'products.prod_cat', 'cats.cat_id')
      .select([...clientProductDBfields, 'cats.cat_title', 'cats.cat_active'])
      .where(prodIdField, id)
      // .andWhere('cats.cat_active', true)
      // .preload('schema', query => {
      //   query.whereRaw('FIND_IN_SET(?, related_ids) > 0', [id])
      // })
      // //.debug(true)
      .first()

    if (productData) {
      if (locale && locale != 'ru') {
        await LangDict.prodsTranslate([productData], locale)
      }

      let product = productData.toJSON()
      const schema = await ProductSchema.query().where('product_id', id).orWhereRaw('FIND_IN_SET(?, related_ids) > 0', [id]).first()

      if (schema) {
        product.schema = schema
      }

      await setProdCartQty(idField, cartId, [product])

      return product
    } else {
      response.status(404)
      return { error: 'Товар не найден' }
    }
  }

  public async findDuplicats({ request, params, response, auth }: HttpContextContract) {
    const { id } = request.params()
    const { types_conformity }: { types_conformity: Array<Object> } = await loadSettings(['types_conformity'])

    Journal.createItem({
      entity: 'products',
      entity_id: id,
      msg: 'просмотр дубликатов товара'
    })

    const mproduct = await Product.findOrFail(id)

    const crossType = types_conformity.find((x) => Object.keys(x).some((k) => x[k] == mproduct.prod_type))
    let crossArr: string[] | undefined = undefined

    if (crossType) {
      crossArr = Object.keys(crossType).map((k) => crossType[k])
    }

    const duplicats = await Product.query()
      .where({ prod_size: String(mproduct.prod_size).trim() })
      .andWhere((query) => {
        query.where('prod_material', String(mproduct.prod_material).trim()).orWhere('prod_material', '')
        if (!mproduct.prod_material) {
          query.orWhere('prod_material', '!=', '')
        }
      })
      .andWhere((subquery) => {
        subquery.where({ prod_type: String(mproduct.prod_type).trim() })
        if (crossArr) {
          subquery.orWhereIn('prod_type', crossArr)
        }
      })

    return duplicats.filter((x) => x.prod_id != mproduct.prod_id)
  }

  public async findExist({ request, params, response, auth }: HttpContextContract) {
    const { oem, id } = request.qs()

    const product = await Product.query()
      .select('prod_id', 'prod_analogsku')
      .where({ prod_analogsku: String(oem).trim() })
      .if(id, (query) => query.andWhere('prod_id', id))
      .first()

    return product || response.status(404).send('not found')
  }

  public async getRkProducts({ request, params, response }: HttpContextContract) {
    const { id } = params

    // Находим основной товар
    const mainProduct = await Product.findOrFail(id)

    if (!mainProduct.prod_rk) {
      return false
    }

    // Получаем список ID связанных РК товаров
    const rkIds = mainProduct.prod_rk
      .split(',')
      .filter(Boolean)
      .map((i) => Number(i.split('*')?.[0]))
      .filter((i) => !Number.isNaN(i))

    if (!rkIds.length) {
      return false
    }

    // Получаем связанные товары
    return await Product.query().whereIn('prod_id', rkIds).select(Product.getProductColumns().clientProductDBfields)
  }
}
