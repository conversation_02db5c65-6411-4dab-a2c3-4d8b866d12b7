import Product from 'App/Models/Product'
import Statistic from 'App/Models/Statistic'

import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import { DateTime } from 'luxon'
import Order from 'App/Models/Order'
import Category from 'App/Models/Category'
import User from 'App/Models/User'

import { SpecReportItem } from 'App/Interfaces/SpecReportItem'
import OrderItem from 'App/Models/OrderItem'
import OrderSnapshot from 'App/Models/OrderSnapshot'
import Client from 'App/Models/Client'
import Mail from '@ioc:Adonis/Addons/Mail'
import Env from '@ioc:Adonis/Core/Env'
import { SnapshotBodyInterface } from 'App/Interfaces/orders/snapshotBody'
import loadSettings from 'App/Helpers/loadSettings'
import { groupByField } from 'App/Helpers/groupBy'
import { SnapshotBodyItemInterface } from 'App/Interfaces/orders/snapshotBodyItem'
import Cache from 'App/Models/Cache'

interface OIP {
  items_order_id: number
  item_id: number
  item_count: number
  ID: number
  order_id: number
  order_price: number
  order_shippingprice: number
  order_status: string
  order_datetime: string
  prod_id: number
  prod_price: number
  prod_discount: number
  prod_analogsku: string
  prod_sku: string
  prod_wholesaleprice: number
  order_discount: number
}

//import * as XLSX from 'xlsx'
const _months = ['Январь', 'Февраль', 'Март', 'Апрель', 'Май', 'Июнь', 'Июль', 'Август', 'Сентябрь', 'Октябрь', 'Ноябрь', 'Декабрь']
export default class StatisticsController {
  public async qtyByMonths({ params, request, response }: HttpContextContract) {
    const currentMonth = new Date().getMonth() + 1
    const currentYear = new Date().getFullYear()

    const result = await Database.from('orders')
      .select(
        Database.raw('YEAR(orders.order_datetime) as year'),
        Database.raw('MONTH(orders.order_datetime) as month'),
        Database.raw('SUM(order_items.item_count) as total_sold'),
        'orders.order_locale'
      )
      .join('order_items', 'orders.order_id', '=', 'order_items.items_order_id')
      .where('orders.order_status', '!=', 'Отменен')
      .groupByRaw('YEAR(orders.order_datetime), MONTH(orders.order_datetime), orders.order_locale')
      .orderBy(Database.raw('CASE WHEN orders.order_locale = "ru" THEN 0 ELSE 1 END'))
      .orderBy('year', 'asc')
      .orderBy('month', 'asc')
    // .debug(true)

    // Преобразование результата в нужный формат
    const companies = {
      'МирСальников': {},
      'Rumisota': {}
    }

    const formattedResult = result.reduce((acc, row) => {
      const company = row.order_locale === 'ru' ? 'МирСальников' : 'Rumisota'
      const year = row.year.toString()
      const month = new Date(row.year, row.month - 1).toLocaleString('ru', { month: 'long' })

      if (!acc[company][year]) acc[company][year] = {}
      acc[company][year][month] = row.total_sold

      return acc
    }, companies)

    return formattedResult
  }
  public async salesByMonths({ request, params, auth }: HttpContextContract) {
    const _year = new Date().getFullYear()

    const currentYear = {}
    const currentYearResult = {}
    const prevYear = {}

    // получаем все заказы за год
    const orders = await Database.from('orders')
      .whereBetween('order_datetime', [`${_year}-01-00`, `${_year}-12-31`])
      .andWhere('order_status', '!=', 'Отменен')

    // извлекаем ID заказов
    const orderIds = orders.map((o) => o.order_id)

    // получаем снепшоты заказов
    const snaps = await Database.from('orders_snapshots').whereIn('orderid', orderIds).orderBy('ID', 'desc')

    // группируем заказы по месяцам
    orders.forEach((order) => {
      const month = getMonthFromDate(order.order_datetime)
      if (!currentYear[month]) {
        currentYear[month] = []
      }
      currentYear[month].push(order)
    })

    // добавляем снепшот к заказам
    orders.forEach((order) => {
      const snap = snaps.find((s) => s.orderid === order.order_id)
      if (snap) {
        try {
          snap.body = JSON.parse(snap.body)
        } catch (error) {
          console.error(error)
        }
      }
      order.snap = snap
    })

    // функция получения номера месяца из даты
    function getMonthFromDate(date) {
      return date.split('-')[1]
    }

    // for await (const month of _months) {
    //   console.log("forawait ~ month:", month)
    //   const index = _months.indexOf(month)
    //   const orders = await Database.from('orders')
    //     .whereBetween('order_datetime', [`${_year}-${index + 1}-00`, `${_year}-${index + 1}-31`])
    //     .andWhere('order_status', '!=', 'Отменен')

    //   const orderIds = orders.map((o) => o.order_id)

    //   const snaps = await Database.from('orders_snapshots').whereIn('orderid', orderIds).orderBy('ID', 'desc')

    //   for (const order of orders) {
    //     const snap = snaps.find((s) => s.orderid === order.order_id)
    //     if (snap) {
    //       try {
    //         snap.body = JSON.parse(snap.body)
    //       } catch (error) {
    //         console.error(error)
    //       }
    //     }
    //     order.snap = snap
    //   }

    //   currentYear[month] = orders
    // }

    // console.log(currentYear['Январь']);

    // return []

    Object.keys(currentYear).map((month) => {
      const orders: Order[] = currentYear[month]
      currentYearResult[month] = orders.reduce((orderAcc, order) => {
        const snap: SnapshotBodyInterface = order.snap?.body
        if (snap) {
          return orderAcc + (snap.order_price + snap.order_shippingprice)
        } else {
          return orderAcc
        }
      }, 0)
    })

    return currentYearResult
  }

  public async statsByClient({ request, params, auth }: HttpContextContract) {
    const { id } = request.qs()

    const _year = new Date().getFullYear()
    const _currentMonth = new Date().getMonth() + 1 - 9

    const client = await Client.findOrFail(id)
    // const orders = await Order.query()
    //                             .where('order_client', client.client_number)
    //                             .andWhereBetween('order_datetime', [`${_year}-${_currentMonth}-00`, `${_year}-${_currentMonth}-31`])

    let ordersCount = {}
    let prev_ordersCount = {}
    let totalSalesCount = {}
    let prev_totalSalesCount = {}

    let shippingPrice = {
      ems: {},
      pochta: {},
      rumisota: {}
    }

    let prev_shippingPrice = {
      ems: {},
      pochta: {},
      rumisota: {}
    }

    await Promise.all([
      await Promise.all(
        _months.map(async (month, index) => {
          let res = await Database.query()
            .from('orders')
            .count('*', month)
            .where('order_client', client.client_id)
            .andWhereBetween('order_datetime', [`${_year}-${index + 1}-00`, `${_year}-${index + 1}-31`])
            .andWhere('order_status', '!=', 'Отменен')
            .first()

          ordersCount[month] = res[month] || 0

          let resprev = await Database.query()
            .from('orders')
            .count('*', month)
            .where('order_client', client.client_id)
            .andWhereBetween('order_datetime', [`${_year - 1}-${index + 1}-00`, `${_year - 1}-${index + 1}-31`])
            .andWhere('order_status', '!=', 'Отменен')
            .first()

          prev_ordersCount[month] = resprev[month] || 0
        })
      ),

      await Promise.all(
        _months.map(async (month, index) => {
          let res = await Database.query()
            .from('orders')
            // .select(Database.raw(`SUM(order_price) + SUM(order_shippingprice) as ${month}`))
            .select(Database.raw(`SUM(order_price) as ${month}`))
            .where('order_client', client.client_id)
            .andWhereBetween('order_datetime', [`${_year}-${index + 1}-00`, `${_year}-${index + 1}-31`])
            .andWhere('order_status', '!=', 'Отменен')
            .first()
          totalSalesCount[month] = res[month] || 0

          let prevres = await Database.query()
            .from('orders')
            .select(Database.raw(`SUM(order_price) as ${month}`))
            // .select(Database.raw(`SUM(order_price) + SUM(order_shippingprice) as ${month}`))
            .where('order_client', client.client_id)
            .andWhereBetween('order_datetime', [`${_year - 1}-${index + 1}-00`, `${_year - 1}-${index + 1}-31`])
            .andWhere('order_status', '!=', 'Отменен')
            .first()

          prev_totalSalesCount[month] = prevres[month] || 0
        })
      )
    ])

    return {
      ordersCount,
      totalSalesCount,
      prev_totalSalesCount,
      prev_ordersCount,
      shippingPrice,
      prev_shippingPrice
    }
  }

  public async orderProcessingTime({ params, request, response }: HttpContextContract) {
    const result = await Database.from('orders_snapshots as os1')
      .join('orders_snapshots as os2', (q) => {
        q.on('os1.orderid', '=', 'os2.orderid').andOn('os1.id', '<', 'os2.id')
      })
      .join('orders', 'os1.orderid', '=', 'orders.order_id')
      // .select(Database.raw('DATE_FORMAT(os1.date, "%M %Y") AS month, AVG(TIMESTAMPDIFF(SECOND, os1.date, os2.date)) AS avg_time_spent'))
      .select(
        Database.raw(`CASE MONTH(os1.date)
        WHEN 1 THEN "Январь"
        WHEN 2 THEN "Февраль"
        WHEN 3 THEN "Март"
        WHEN 4 THEN "Апрель"
        WHEN 5 THEN "Май"
        WHEN 6 THEN "Июнь"
        WHEN 7 THEN "Июль"
        WHEN 8 THEN "Август"
        WHEN 9 THEN "Сентябрь"
        WHEN 10 THEN "Октябрь"
        WHEN 11 THEN "Ноябрь"
        WHEN 12 THEN "Декабрь"
      END AS month, AVG(TIMESTAMPDIFF(SECOND, os1.date, os2.date)) AS avg_time_spent`)
      )
      .where('orders.order_status', '=', 'Завершен')
      .andWhere('orders.order_locale', '=', 'ru')
      .whereRaw('YEAR(os1.date) = YEAR(CURDATE())')
      .groupBy('month')
      .orderByRaw('MONTH(os1.date) asc')

    return result
  }

  public async topProducts({ params, request, response }: HttpContextContract) {
    const today = new Date()
    const lastYearToday = new Date(today.getFullYear() - 1, today.getMonth() + 1, today.getDate(), 23, 59, 59)

    const res = await Database.from('products as p')
      .join('order_items as oi', 'p.prod_id', '=', 'oi.item_id')
      .join('orders as o', 'oi.items_order_id', '=', 'o.order_id')
      .select('p.prod_id', 'p.prod_sku', 'p.prod_analogsku', 'p.prod_cat', 'p.prod_count', Database.raw('SUM(oi.item_count) as total_sales'))
      .where('o.order_status', '!=', 'Отменен')
      // .andWhere(Database.raw('YEAR(o.order_datetime) = YEAR(CURDATE())'))
      .where('order_datetime', '>=', Database.raw(`DATE_FORMAT(DATE_SUB('${lastYearToday.toISOString()}', INTERVAL 1 YEAR), '%Y-01-01')`))
      .andWhere('order_datetime', '<=', Database.raw(`DATE_FORMAT(DATE_SUB('${lastYearToday.toISOString()}', INTERVAL 1 YEAR), '%Y-%m-%d')`))
      .groupBy('p.prod_analogsku')
      .orderBy('total_sales', 'desc')
      .limit(50)

    return res.map((product) => {
      return {
        oem: product.prod_analogsku,
        code: product.prod_sku,
        avg_sales: Math.ceil(product.total_sales / 12),
        stock: product.prod_count,
        reserve: Math.floor(Number((product.prod_count / Math.ceil(product.total_sales / 12)).toFixed(2)))
      }
    })

    const [reqNoMan, reqMan, topSales] = await Promise.all([
      await Database.from('statistics').select('query').sum('count as total_count').where({ manual: 0 }).groupBy('query').orderByRaw('`total_count` DESC').limit(30),
      await Database.from('statistics').select('query').sum('count as total_count').where({ manual: 1 }).groupBy('query').orderByRaw('`total_count` DESC').limit(30),
      await Database.from('products as p')
        .join('order_items as oi', 'p.prod_id', '=', 'oi.item_id')
        .join('orders as o', 'oi.items_order_id', '=', 'o.order_id')
        .select('p.prod_id', 'p.prod_sku', 'p.prod_analogsku', 'p.prod_cat', 'p.prod_count', Database.raw('SUM(oi.item_count) as total_sales'))
        .where('o.order_status', '!=', 'Отменен')
        // .andWhere(Database.raw('YEAR(o.order_datetime) = YEAR(CURDATE())'))
        .where('order_datetime', '>=', Database.raw(`DATE_FORMAT(DATE_SUB('${lastYearToday.toISOString()}', INTERVAL 1 YEAR), '%Y-01-01')`))
        .andWhere('order_datetime', '<=', Database.raw(`DATE_FORMAT(DATE_SUB('${lastYearToday.toISOString()}', INTERVAL 1 YEAR), '%Y-%m-%d')`))
        .groupBy('p.prod_id')
        .orderBy('total_sales', 'desc')
        .limit(30)
    ])

    const topRequest = [reqNoMan, reqMan].reduce((acc, curr) => {
      curr.forEach((obj) => {
        const existingObj = acc.find((o) => o.query === obj.query)
        if (existingObj) {
          existingObj.total_count += obj.total_count
        } else {
          acc.push({ ...obj })
        }
      })
      return acc
    }, [])

    return {
      topSales,
      topRequest: topRequest.sort((a, b) => b.total_count - a.total_count)
    }
  }

  public async statsByOrders({ params, request, response }: HttpContextContract) {
    const { groupby, shippingtypes, sumby = 'order_shippingprice' } = request.all()

    return await Statistic.statsByOrders({ groupby, shippingtypes, sumby })
  }

  public async salesByFilter({ request }: HttpContextContract) {
    const { currentMonth, currentYear, filters, dateRange, groupby, calcGroupSum, oldPrice = true } = request.all()

    return await Statistic.salesByFilters({ currentMonth, currentYear, filters, dateRange, groupby, calcGroupSum, oldPrice })
  }

  public async exdashboard({ params, request, response }: HttpContextContract) {
    // SESSION USER
    let { currentMonth, currentYear } = request.qs()

    const _year = currentYear || new Date().getFullYear()
    const _currentMonth = currentMonth || new Date().getMonth() + 1

    const byCategories = {}
    const canceledOrdersCount = {}

    _months.map((month) => {
      canceledOrdersCount[month] = 0
    })

    const categories = await Database.from('cats').select('cat_title', 'cat_id').where('cat_active', 1).andWhere('cat_rootcat', '!=', 0)

    await Promise.all(
      categories.map(async (category) => {
        let res = await Database.from('order_items')
          .select(Database.raw(`SUM(order_items.item_count * products.prod_price - (products.prod_price/100*products.prod_discount)) as price`))
          .sum('order_items.item_count', 'totalCount')
          .leftOuterJoin('products', 'products.prod_id', 'order_items.item_id')
          .leftOuterJoin('orders', 'orders.order_id', 'order_items.items_order_id')
          .where('orders.order_status', '!=', 'Отменен')
          .andWhereBetween('order_datetime', [`${_year}-${_currentMonth}-00`, `${_year}-${_currentMonth}-31`])
          .andWhere('products.prod_cat', category.cat_id)
          // //.debug(true)
          .first()

        // console.log('res:', res)

        byCategories[category.cat_title] = res //res.totalCount || 0
      })
    )

    await Promise.all(
      _months.map(async (month, index) => {
        let res = await Database.query()
          .from('orders')
          .count('*', month)
          .whereBetween('order_datetime', [`${_year}-${index + 1}-00`, `${_year}-${index + 1}-31`])
          .andWhere('order_status', '=', 'Отменен')
          .first()

        canceledOrdersCount[month] = res[month] || 0
      })
    )

    return {
      canceledOrdersCount,
      byCategories
    }
  }

  public async ordersCurrentStats({ params, request, response }) {
    const currentMonth = new Date().getMonth() + 1
    const currentYear = new Date().getFullYear()

    const [{ total: count }, { total: sum }, { today_count }] = await Promise.all([
      await Database.query()
        .from('orders')
        .count('*', 'total')
        .whereBetween('order_datetime', [`${currentYear}-${currentMonth}-00`, `${currentYear}-${currentMonth}-31`])
        .andWhere('order_status', '!=', 'Отменен')
        .first(),

      await Database.query()
        .from('orders')
        .select(Database.raw(`SUM(order_price) + SUM(order_shippingprice) as total`))
        .whereBetween('order_datetime', [`${currentYear}-${currentMonth}-00`, `${currentYear}-${currentMonth}-31`])
        .andWhere('order_status', '!=', 'Отменен')
        .first(),

      await Database.query()
        .from('orders')
        .select(Database.raw(`SUM(order_price) + SUM(order_shippingprice) as today_count`))
        .where('order_datetime', 'like', Database.raw("concat(CURDATE(), '%')"))
        .andWhere('order_status', '!=', 'Отменен')
        .first()

      // await Database.query().from('orders').count('order_id', 'today_count').where('order_datetime', 'like', Database.raw("concat(CURDATE(), '%')")).first()
    ])

    return {
      count,
      countToday: today_count,
      sum: sum || 0
    }
  }

  public async dashboard({ params, request, response }) {
    // SESSION USER

    let { prev_year = true, currentMonth, currentYear } = request.qs()

    const _year = currentYear || new Date().getFullYear()
    const _currentMonth = currentMonth || new Date().getMonth() + 1 - 9

    const ordersCount = {}
    const totalSalesCount = {}

    const shippingEnums = {
      'ems': ['Курьер'],
      'pochta': ['Почта РФ'],
      rumisota: ['Курьер', 'Почта РФ']
    }

    const shippingStats = {
      ems: {},
      pochta: {},
      rumisota: {}
    }

    const prev_shippingStats = {
      ems: {},
      pochta: {},
      rumisota: {}
    }

    const prev_ordersCount = {}
    const prev_totalSalesCount = {}

    const counters = {}

    _months.map((month) => {
      ordersCount[month] = 0
      totalSalesCount[month] = 0

      prev_ordersCount[month] = 0
      prev_totalSalesCount[month] = 0
    })

    await Promise.all([
      await Promise.all(
        _months.map(async (month, index) => {
          let res = await Database.query()
            .from('orders')
            .count('*', month)
            // .whereBetween('order_datetime', [`${_year}-${index + 1}-00`, `${_year}-${index + 1}-31`])
            .whereBetween('order_datetime', [`${_year}-${index + 1}-01`, `${_year}-${index + 1}-31 23:59:59`])
            .andWhere('order_status', '!=', 'Отменен')
            .first()

          ordersCount[month] = res[month] || 0

          if (prev_year) {
            let res = await Database.query()
              .from('orders')
              .count('*', month)
              // .whereBetween('order_datetime', [`${_year - 1}-${index + 1}-00`, `${_year - 1}-${index + 1}-31`])
              .whereBetween('order_datetime', [`${_year - 1}-${index + 1}-01`, `${_year}-${index + 1}-31 23:59:59`])
              .andWhere('order_status', '!=', 'Отменен')
              .first()

            prev_ordersCount[month] = res[month] || 0
          }
        })
      ),

      await Promise.all(
        _months.map(async (month, index) => {
          await Promise.all(
            Object.keys(shippingEnums).map(async (key) => {
              let res = await Database.query()
                .from('orders')
                .select(Database.raw(`SUM(order_shippingprice) as ${month}`))
                .whereBetween('order_datetime', [`${_year}-${index + 1}-01`, `${_year}-${index + 1}-31 23:59:59`])
                .andWhere((query) => {
                  if (key == 'rumisota') {
                    query.whereNotIn('order_shipping', shippingEnums[key])
                  } else {
                    query.whereIn('order_shipping', shippingEnums[key])
                  }
                })
                .andWhere('order_status', '!=', 'Отменен')
                .first()

              shippingStats[key][month] = res[month] || 0

              let prev_res = await Database.query()
                .from('orders')
                .select(Database.raw(`SUM(order_shippingprice) as ${month}`))
                .whereBetween('order_datetime', [`${_year - 1}-${index + 1}-01`, `${_year - 1}-${index + 1}-31 23:59:59`])
                .andWhere((query) => {
                  if (key == 'rumisota') {
                    query.whereNotIn('order_shipping', shippingEnums[key])
                  } else {
                    query.whereIn('order_shipping', shippingEnums[key])
                  }
                })
                .andWhere('order_status', '!=', 'Отменен')
                .first()

              prev_shippingStats[key][month] = prev_res[month] || 0
            })
          )

          let res = await Database.query()
            .from('orders')
            .select(Database.raw(`SUM(order_price) as ${month}`))
            // .select(Database.raw(`SUM(order_price) + SUM(order_shippingprice) as ${month}`))
            .whereBetween('order_datetime', [`${_year}-${index + 1}-01`, `${_year}-${index + 1}-31 23:59:59`])
            .andWhere('order_status', '!=', 'Отменен')
            .first()

          totalSalesCount[month] = res[month] || 0

          if (prev_year) {
            let res = await Database.query()
              .from('orders')
              .select(Database.raw(`SUM(order_price) as ${month}`))
              // .select(Database.raw(`SUM(order_price) + SUM(order_shippingprice) as ${month}`))
              .whereBetween('order_datetime', [`${_year - 1}-${index + 1}-01`, `${_year - 1}-${index + 1}-31 23:59:59`])
              .andWhere('order_status', '!=', 'Отменен')
              .first()

            prev_totalSalesCount[month] = res[month] || 0
          }
        })
      )
    ])

    const { orders_today } = await Database.query().from('orders').count('order_id', 'orders_today').where('order_datetime', 'like', Database.raw("concat(CURDATE(), '%')")).first()

    const { new_clients } = await Database.query()
      .from('clients')
      .count('client_id', 'new_clients')
      .where('created_at', 'like', Database.raw("concat(CURDATE(), '%')"))
      // //.debug(true)
      .first()
    const { unpaid_orders } = await Database.query().from('orders').count('order_id', 'unpaid_orders').where('order_status', '=', 'В ожидании оплаты').first()

    const { orders_sum_today } = await Database.query()
      .from('orders')
      .select(Database.raw(`SUM(order_price) + SUM(order_shippingprice) as orders_sum_today`))
      .where('order_datetime', 'like', Database.raw("concat(CURDATE(), '%')"))
      .andWhere('order_status', '!=', 'Отменен')
      .first()

    return {
      ordersCount,
      totalSalesCount,
      prev_ordersCount,
      prev_totalSalesCount,
      shippingStats,
      prev_shippingStats,
      counters: {
        orders_today,
        new_clients,
        unpaid_orders,
        orders_sum_today
      }
    }
  }

  public async remove({ params, request, response, auth }: HttpContextContract) {
    const authUser = await auth.use('api').authenticate()

    let { id } = request.all()
    if (!id) throw new Error('Invalid id')

    return await Statistic.remove(id)
  }

  public async write({ params, request, response, auth }: HttpContextContract) {
    let authUser: User | null = null

    try {
      authUser = await auth.use('api').authenticate()
    } catch (error) {}

    if (params.query == 'undefined') {
      return []
    }

    response.status(201)

    let { count = 1, info, manual = 0 } = request.all()

    try {
      await Statistic.write({
        request,
        query: params.query,
        count,
        info: (authUser?.user_name ? authUser?.user_name + ': ' : '') + info,
        manual
      })
    } catch (error) {
      console.error('StatisticsController.ts: ', error)
    }

    try {
      if (!authUser && count >= 100) {
        Mail.use('smtp').send((message) => {
          message.from(Env.get('EMAIL_FROM')).to(client.client_mail).subject('Большой предзаказ').htmlView(`${info}, предзаказ на ${count}`)
        })
      }
    } catch (error) {}
  }

  public async statByOEM({ params, request, response, auth }: HttpContextContract) {
    //const sessionUser = await auth.use('api')

    const { oem } = params
    const { sales, stat, statList, brand } = request.qs()

    const data = {
      stat: stat ? await Statistic.getStatByOem({ oem, brand, list: statList || false }) : undefined,
      sales: sales ? await Product.getProductSalesByOEM({ oem, brand }) : undefined,
      total: Number(stat || 0) + Number(sales || 0)
    }

    return data
  }

  public async workStatByPerson({ params, request, response }: HttpContextContract) {
    let { dates } = request.all()

    if (dates) {
      dates = dates.replace(/\'*|"*/gm, '').split(',')
    } else {
      let startDate = DateTime.now()
        .minus({ days: DateTime.now().day - 1 })
        .set({ hour: 0, minute: 0, second: 0 })
      let endDate = startDate.plus({ days: DateTime.now().daysInMonth }).set({ hour: 0, minute: 0, second: 0 })

      dates = [startDate.toSQLDate(), endDate.toSQLDate()]
    }

    // return dates

    const users = await User.query()

    const dataByLastPerson = await Promise.all(
      users.map(async (user) => {
        const rdata = await OrderItem.query()
          .sum('item_count', 'items_sum')
          .count('item_id', 'items_cnt')
          .join('orders', 'order_items.items_order_id', '=', 'orders.order_id')
          .where('orders.order_lastupdate_person', user.user_name)
          .andWhereBetween('timestamp', dates)
          .andWhere('order_status', '!=', 'Отменен')
          // //.debug(true)
          .first()

        return {
          user: user.user_name,
          cnt: rdata.$extras.items_cnt || 0,
          sum: rdata.$extras.items_sum || 0
        }
      })
    )

    const orders = await Order.query().select('order_id').whereBetween('order_datetime', dates).andWhere('order_status', '!=', 'Отменен')
    // //.debug(true)

    await Promise.all(
      orders.map(async (order) => {
        const lastSnap = await OrderSnapshot.query().select('user').where('orderid', order.order_id).orderBy('ID', 'desc').first()

        const bySnapData = await OrderItem.query().sum('item_count', 'items_sum').count('item_id', 'items_cnt').where('items_order_id', order.order_id).first()

        const user = dataByLastPerson.find((x) => x.user == lastSnap?.user)

        if (user && bySnapData) {
          if (!user['cnt_by_snaps']) user['cnt_by_snaps'] = 0
          if (!user['sum_by_snaps']) user['sum_by_snaps'] = 0

          user['cnt_by_snaps'] += bySnapData.$extras.items_cnt || 0
          user['sum_by_snaps'] += bySnapData.$extras.items_sum || 0
        } else {
          console.error(`user: ${user} not found`)
        }
      })
    )

    if (request.qs().xlsx) {
      let XLSX
      if (typeof require !== 'undefined') XLSX = require('xlsx')

      let data = dataByLastPerson.map((item) => {
        return {
          Работник: item.user,
          'Позиций собрано': item.cnt,
          'Кол-во': item.sum,
          'Позиций, по снимкам': item.cnt_by_snaps || '',
          'Кол-во, по снимкам': item.sum_by_snaps || ''
        }
      })

      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(data)

      XLSX.utils.book_append_sheet(wb, ws, 'Отчет')

      const buffer = XLSX.write(wb, { type: 'array', bookType: 'xlsx' })

      response.header('Content-type', 'application/zip')
      response.header('Content-Disposition', `attachment; filename="rti_personal_report_${dates.join('-')}.xlsx"`)
      response.send(Buffer.from(buffer, 'binary'))
    } else {
      return dataByLastPerson
    }
  }

  public async export({ params, request, response }: HttpContextContract) {
    console.log('0. Start export...')
    //console.time('0. Start export...')

    interface opError {
      product: Product
      msg: string
    }

    interface OAMS {
      [key: string]: [
        {
          query: string
          client_ip: string
          client_info: string
          count: number
          manual: number
          created_at: string
        }
      ]
    }

    let { toEmail, dates, cats } = request.all()

    if (dates) {
      dates = dates
        .replace(/\'*|"*/gm, '')
        .split(',')
        .filter((i) => i)
        .map((i) => decodeURIComponent(i))

      if (!dates?.length) {
        dates = undefined
      }
      console.log('StatisticsController ~ export ~ dates:', dates)
    }

    const errors: opError[] = []
    let data: {}[] = []

    const { isTEST } = request.qs() || false
    const testSkus = request.qs()['testSkus']?.split(',') || ['D770']
    const { withInfo = true } = request.qs()

    // const nomanual = await queryF(`select count(*) as qstat, query from statistics where manual = 0 group by query order by count(*) desc`)
    // const manual = await queryF(`select sum(count) as qstat, query from statistics where manual = 1 group by query order by count(*) desc`)

    // --------- PREPARE stat data

    console.log('1.Start getFullStat !manual (db query)')
    //console.time('getFullStat !manual done')
    const StatData = await Statistic.getFullStat({
      manual: false,
      test: isTEST,
      toObject: true,
      testSkus,
      dates
    })
    //console.timeEnd('getFullStat !manual done')

    console.log('2.Start getFullStat manual (db query)')
    //console.time('getFullStat manual done')
    const ManualStatData = await Statistic.getFullStat({
      manual: true,
      test: isTEST,
      toObject: true,
      testSkus,
      dates
    })
    //console.timeEnd('getFullStat manual done')

    Object.keys(StatData).map((key) => {
      if (ManualStatData[key]) {
        StatData[key] += ManualStatData[key]
      }
    })

    let additManualStatInfo: Array<Statistic> = []
    let ObjadditManualStatInfo: OAMS = {}

    if (withInfo) {
      console.log('3.Start additManualStatInfo (db query)')
      //console.time('get additManualStatInfo')
      additManualStatInfo = await Database.from('statistics')
        .select('created_at', 'query', 'client_info', 'count')
        .where({ manual: true })
        .if(dates, (query) => {
          query.where((subquery) => subquery.whereBetween('statistics.created_at', dates))
        })

      additManualStatInfo.map((item) => {
        let _key = String(item.query).toUpperCase().trim()

        if (!ObjadditManualStatInfo[_key]) {
          ObjadditManualStatInfo[_key] = []
        }
        ObjadditManualStatInfo[_key].push(item)
      })

      // console.log('ObjadditManualStatInfo:', ObjadditManualStatInfo)
      //console.timeEnd('get additManualStatInfo')
    }

    // ---------- ./PREPARE stat data

    // ---------- GET products by stat list
    console.log('4.Start get all products (db query)')
    //console.time('get catalog data')
    const catalog = await Product.query()
      .select('*')
      .leftJoin('cats', 'products.prod_cat', '=', 'cats.cat_id')
      .where('cats.cat_active', true)
      .if(isTEST, (builder) => builder.whereIn('prod_analogsku', testSkus))
    //console.timeEnd('get catalog data')
    // ---------- ./GET products by stat list

    // --------- GET products sales
    console.log('5. Start get all sales data (db query)')
    //console.time('get sales data')
    const SalesData = await Product.getProductSales({
      toObject: true,
      test: isTEST,
      dates
    })
    //console.timeEnd('get sales data')
    // ---------- ./GET products sales

    // ----------- GET GTD data
    console.log('6. Start get all SPEC data (db query)')
    //console.time('get spec data')
    const Spec_list: Array<SpecReportItem> = await Database.from('spec_list').select('spec', 'prod').sum('qty as qty').groupBy('prod')
    //console.timeEnd('get spec data')
    // ----------- ./GET GTD data

    // ---------- SET data from spec_list data
    console.log('7. Start set spec & manual stat')
    //console.time('set spec data & manual stat')
    /*             Spec_list.map(item => {
               let fnd = catalog.find(x => String(x.prod_analogsku).toLowerCase().trim() == String(item.prod).toLowerCase().trim())
               if (fnd) fnd._gtd = item.qty
            }) */

    // console.log('catalog.length: ', catalog.length)
    // console.log('additManualStatInfo.length: ', additManualStatInfo.length)
    // console.log('Spec_list,length: ', Spec_list.length)

    catalog.forEach((el) => {
      let foundItem = Spec_list.find((x) => String(el.prod_analogsku).toUpperCase().trim() == String(x.prod).toUpperCase().trim())

      foundItem && (el['_gtd'] = foundItem.qty)

      if (additManualStatInfo.length) {
        let _key = String(el.prod_analogsku).toUpperCase().trim()

        // ObjadditManualStatInfo

        let filteredItems = ObjadditManualStatInfo[_key] //additManualStatInfo.filter(x => String(el.prod_analogsku).toUpperCase().trim() == String(x.query).toUpperCase().trim())

        if (filteredItems?.length) {
          filteredItems.map((item) => {
            // console.log('item.created_at: ', item.created_at)
            //DateTime.fromISO(item.created_at).toFormat('dd.LL.yy')
            el['manualstat_info'] = `${item?.created_at || ''} | ${item.query} - ${item.count} шт. | ${String(item.client_info).split('|')[0]}; `
          })
        }
      }
    })
    //console.timeEnd('set spec data & manual stat')
    // ---------- ./SET data from spec_list

    // ---------- SET stat data to products
    console.log('8. START SET stat data to products')
    //console.time('SET stat data to products')
    catalog.forEach((product: Product) => {
      try {
        // TODO: ? merge items by prod_analogsku
        // TODO: sales FROM REPAIR KITS

        product.prod_analogsku = String(product.prod_analogsku).toUpperCase()
        product.prod_sku = String(product.prod_sku).toUpperCase()

        let ietQty = StatData[product.prod_analogsku] || 0 //+ (StatData[product.prod_sku] || 0)

        if (product.prod_analogsku != product.prod_sku) {
          ietQty += StatData[product.prod_sku] || 0
        }

        // find sales by key: prod_analogsku+prod_manuf // ex: "P00001TCS"
        let productSales = SalesData[String(product.prod_analogsku) + String(product.prod_manuf)]

        product['sales'] = productSales || 0
        product['ques'] = ietQty || 0

        const analogs = [...new Set(product.prod_analogs?.split(',')?.filter((i) => i))]

        // console.log('analogs', analogs)

        if (analogs?.length) {
          analogs.map((sku) => {
            sku = String(sku).toUpperCase()
            if (StatData[sku]) {
              if (sku != product.prod_analogsku && sku != product.prod_sku) {
                product.ques += Number(StatData[sku])
              }
            }
          })
        }

        data.push({
          'Наименование': `${product.prod_purpose} | ${product.prod_size} | (${product.prod_sku} ${product.prod_type}) | ${product.prod_analogsku} | ${product.prod_manuf}`,
          'Артикул(OEM)': product.prod_analogsku,
          'Orpav': product.prod_sku,
          'Прочие номера': '',
          'Размер': product.prod_size,
          'Вес': product.prod_weight,
          'Категория': product.cat_title,
          'Наличие': product.prod_count,
          'Цена(Розн)': product.prod_price,
          'Цена(опт)': Number((product.prod_price - (product.prod_price / 100) * product.prod_discount).toFixed(2)),
          'Скидка(%)': product.prod_discount,
          'Закуп': String(product.prod_purchasing).replace('.', ','),
          'Применение': product.prod_uses,
          'Модель': product.prod_model,
          'Тип': product.prod_type,
          'Бренд': product.prod_manuf,
          'Поставщик': product.prod_supplier,
          'Аналоги': product.prod_analogs,
          'Материал': product.prod_material,
          'Группа': product.prod_group,
          'Оповещение': product.prod_minalert,
          'Изобр.': product.prod_img,
          'Секрет': product.prod_secret,
          'Коэффициент': product.prod_coeff,
          'Ячейка': product.prod_cell,
          'Cпрос': product.ques,
          'Продано': product.sales,
          'Востреб': Number(product.ques || 0) + Number(product.sales || 0),
          'Вывоз': product._gtd || '--',
          'Ссылка': `=HYPERLINK("https://mirsalnikov.ru/catalog/product/${product.prod_id}", "Открыть")`,
          'Пометки по спросу': product.manualstat_info,
          'Примечание': product.prod_note || '',
          _key: String(product.prod_analogsku) + String(product.prod_manuf)
        })
      } catch (error) {
        console.error('setstat value: ', error)
        errors.push({ product, msg: String(error) })
      }
    })
    //console.timeEnd('SET stat data to products')

    // ---------- ./ SET stat data to products

    // SORT BY "sales"
    console.log('9. start sort')

    //console.time('sort data')
    data.sort((a, b) => b['Cпрос'] - a['Cпрос'])
    //console.timeEnd('sort data')

    console.log('9. start merge')
    //console.time('merge data')

    let skuField = '_key'
    let newData = []

    // pushItem['Прочие номера'] = duplicats.map(i => i['Orpav']).join(', ')

    let cachedObject = {}

    data.map((item) => {
      //let duplicats = data.filter(i => i[skuField] == item[skuField])
      //item['Прочие номера'] = duplicats.map(i => i['Orpav']).join(', ')

      cachedObject[item[skuField]] = item
    })

    newData = Object.values(cachedObject)

    //console.timeEnd('merge data')

    console.log('10. WRITE xlsx')
    //console.time('WRITE xlsx')
    // ------------- WRITE FILE
    let XLSX
    if (typeof require !== 'undefined') XLSX = require('xlsx')

    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.json_to_sheet(newData)

    XLSX.utils.book_append_sheet(wb, ws, 'Отчет')

    const dn = DateTime.now().toLocal()
    const buffer = XLSX.write(wb, {
      type: 'array',
      bookType: 'xlsx',
      compression: true
    })

    //console.timeEnd('WRITE xlsx')

    if (toEmail) {
      await Mail.send((message) => {
        message
          .from(Env.get('EMAIL_FROM'))
          .to(Env.get('EMAIL_FROM'))
          .subject('Выгрузка статистики')
          .attachData(Buffer.from(buffer, 'binary'), {
            filename: `rti_report_${dn}.xlsx`
          })
      })
      return []
    } else {
      response.header('Content-type', 'application/zip')
      response.header('Content-Disposition', `attachment; filename="rti_report_${dn}.xlsx"`)
      response.send(Buffer.from(buffer, 'binary'))

      //console.timeEnd('0. Start export...')
      // ---------------- ./ WRITE file
      return []
    }
  }

  public async statsListItem({ params, request, response }: HttpContextContract) {
    const { query } = request.qs()
    return await Database.from('statistics').where({ query, manual: 1 }).orderBy('id', 'desc').limit(200)
  }

  public async statsList({ params, request, response }: HttpContextContract) {
    const { limit = 80, searchvalue, isNoStock = true } = request.all()

    return await Statistic.statList({ limit, searchvalue, isNoStock })
  }

  public async removeStatItem({ params, request, auth, response }: HttpContextContract) {
    const user = await auth.use('api').authenticate()

    if (user.user_role != 'su') {
      return response.status(401)
    }

    const { id } = request.all()

    const item = await Statistic.findOrFail(id)

    await item.delete()

    if (item.$isDeleted) {
      return { isOk: true }
    } else {
      return response.status(500)
    }
  }
}
