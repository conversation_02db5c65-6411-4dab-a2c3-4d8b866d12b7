import * as fs from 'fs'
import * as path from 'path'
import Env from '@ioc:Adonis/Core/Env'

const UPLOAD_PATH = path.resolve(Env.get('UPLOAD_PATH'))

export interface ImageInfo {
  fileName: string
  fullPath: string
  size: number
  exists: boolean
  type: 'rti' | 'rumi'
}

export interface ProductImages {
  mainImage?: ImageInfo
  additionalImages: ImageInfo[]
  allImages: ImageInfo[]
  totalSize: number
}

/**
 * Утилиты для работы с изображениями товаров
 */
export class OzonImageHelper {
  
  /**
   * Получение информации об изображении
   */
  static async getImageInfo(imageName: string, type: 'rti' | 'rumi'): Promise<ImageInfo> {
    const fullPath = path.join(UPLOAD_PATH, type, imageName)
    
    let size = 0
    let exists = false
    
    try {
      const stats = await fs.promises.stat(fullPath)
      size = stats.size
      exists = true
    } catch {
      exists = false
    }

    return {
      fileName: imageName,
      fullPath,
      size,
      exists,
      type
    }
  }

  /**
   * Получение всех изображений товара
   */
  static async getProductImages(product: any): Promise<ProductImages> {
    const allImages: ImageInfo[] = []
    let mainImage: ImageInfo | undefined
    let totalSize = 0

    // Основное изображение RTI
    if (product.prod_img) {
      const imageName = product.prod_img.endsWith('.jpg') ? product.prod_img : `${product.prod_img}.jpg`
      const imageInfo = await this.getImageInfo(imageName, 'rti')
      
      if (imageInfo.exists) {
        mainImage = imageInfo
        allImages.push(imageInfo)
        totalSize += imageInfo.size
      }
    }

    // Основное изображение RUMI (если RTI не найдено)
    if (!mainImage && product.prod_img_rumi) {
      const imageName = product.prod_img_rumi.endsWith('.jpg') ? product.prod_img_rumi : `${product.prod_img_rumi}.jpg`
      const imageInfo = await this.getImageInfo(imageName, 'rumi')
      
      if (imageInfo.exists) {
        mainImage = imageInfo
        allImages.push(imageInfo)
        totalSize += imageInfo.size
      }
    }

    // Дополнительные изображения
    if (product.prod_images) {
      const imageNames = product.prod_images
        .split(',')
        .map((name: string) => name.trim())
        .filter((name: string) => name.length > 0)

      for (const imageName of imageNames) {
        // Пропускаем если это уже основное изображение
        if (mainImage && imageName === mainImage.fileName) {
          continue
        }

        // Проверяем RTI версию
        let imageInfo = await this.getImageInfo(imageName, 'rti')
        if (imageInfo.exists) {
          allImages.push(imageInfo)
          totalSize += imageInfo.size
          continue
        }

        // Проверяем RUMI версию
        imageInfo = await this.getImageInfo(imageName, 'rumi')
        if (imageInfo.exists) {
          allImages.push(imageInfo)
          totalSize += imageInfo.size
        }
      }
    }

    const additionalImages = allImages.filter(img => img !== mainImage)

    return {
      mainImage,
      additionalImages,
      allImages,
      totalSize
    }
  }

  /**
   * Проверка валидности изображения
   */
  static async validateImage(imagePath: string): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
  }> {
    const errors: string[] = []
    const warnings: string[] = []

    try {
      // Проверяем существование файла
      const stats = await fs.promises.stat(imagePath)
      
      // Проверяем расширение
      const ext = path.extname(imagePath).toLowerCase()
      const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
      
      if (!allowedExtensions.includes(ext)) {
        errors.push(`Неподдерживаемое расширение файла: ${ext}`)
      }

      // Проверяем размер файла (максимум 10MB)
      const maxSize = 10 * 1024 * 1024
      if (stats.size > maxSize) {
        errors.push(`Файл слишком большой: ${(stats.size / 1024 / 1024).toFixed(2)}MB (максимум 10MB)`)
      }

      // Предупреждения для больших файлов
      const warningSize = 5 * 1024 * 1024
      if (stats.size > warningSize) {
        warnings.push(`Большой размер файла: ${(stats.size / 1024 / 1024).toFixed(2)}MB`)
      }

      // Проверяем минимальный размер
      const minSize = 1024 // 1KB
      if (stats.size < minSize) {
        errors.push('Файл слишком маленький')
      }

    } catch (error) {
      errors.push(`Ошибка при проверке файла: ${error.message}`)
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }

  /**
   * Получение оптимального списка изображений для загрузки в Ozon
   */
  static async getOptimalImageList(product: any, maxImages = 10): Promise<{
    images: ImageInfo[]
    skipped: ImageInfo[]
    totalSize: number
  }> {
    const productImages = await this.getProductImages(product)
    const images: ImageInfo[] = []
    const skipped: ImageInfo[] = []
    let totalSize = 0

    // Сначала добавляем основное изображение
    if (productImages.mainImage) {
      const validation = await this.validateImage(productImages.mainImage.fullPath)
      if (validation.isValid) {
        images.push(productImages.mainImage)
        totalSize += productImages.mainImage.size
      } else {
        skipped.push(productImages.mainImage)
      }
    }

    // Затем добавляем дополнительные изображения
    for (const image of productImages.additionalImages) {
      if (images.length >= maxImages) {
        skipped.push(image)
        continue
      }

      const validation = await this.validateImage(image.fullPath)
      if (validation.isValid) {
        images.push(image)
        totalSize += image.size
      } else {
        skipped.push(image)
      }
    }

    return {
      images,
      skipped,
      totalSize
    }
  }

  /**
   * Создание резервной копии изображений
   */
  static async backupImages(product: any, backupDir?: string): Promise<{
    success: boolean
    backedUp: string[]
    errors: string[]
  }> {
    const backedUp: string[] = []
    const errors: string[] = []

    try {
      const productImages = await this.getProductImages(product)
      const targetDir = backupDir || path.join(UPLOAD_PATH, 'backup', `product_${product.prod_id}`)

      // Создаем директорию для бэкапа
      await fs.promises.mkdir(targetDir, { recursive: true })

      for (const image of productImages.allImages) {
        try {
          const targetPath = path.join(targetDir, `${image.type}_${image.fileName}`)
          await fs.promises.copyFile(image.fullPath, targetPath)
          backedUp.push(image.fileName)
        } catch (error) {
          errors.push(`Ошибка копирования ${image.fileName}: ${error.message}`)
        }
      }

      return {
        success: errors.length === 0,
        backedUp,
        errors
      }

    } catch (error) {
      return {
        success: false,
        backedUp,
        errors: [`Ошибка создания бэкапа: ${error.message}`]
      }
    }
  }

  /**
   * Получение статистики по изображениям
   */
  static async getImageStats(productIds?: number[]): Promise<{
    totalProducts: number
    productsWithImages: number
    productsWithoutImages: number
    totalImages: number
    totalSize: number
    averageImagesPerProduct: number
    largestImage: { fileName: string, size: number } | null
  }> {
    // Здесь можно реализовать статистику по изображениям
    // Пока возвращаем заглушку
    return {
      totalProducts: 0,
      productsWithImages: 0,
      productsWithoutImages: 0,
      totalImages: 0,
      totalSize: 0,
      averageImagesPerProduct: 0,
      largestImage: null
    }
  }

  /**
   * Очистка неиспользуемых изображений
   */
  static async cleanupUnusedImages(dryRun = true): Promise<{
    found: string[]
    deleted: string[]
    errors: string[]
    totalSize: number
  }> {
    const found: string[] = []
    const deleted: string[] = []
    const errors: string[] = []
    let totalSize = 0

    // Здесь можно реализовать логику поиска и удаления неиспользуемых изображений
    console.log('Cleanup unused images:', { dryRun })

    return {
      found,
      deleted,
      errors,
      totalSize
    }
  }

  /**
   * Конвертация изображения в нужный формат
   */
  static async convertImage(sourcePath: string, targetPath: string, format = 'jpg'): Promise<{
    success: boolean
    error?: string
    originalSize: number
    newSize: number
  }> {
    try {
      const originalStats = await fs.promises.stat(sourcePath)
      
      // Здесь можно добавить логику конвертации изображений
      // Например, используя sharp или imagemagick
      
      // Пока просто копируем файл
      await fs.promises.copyFile(sourcePath, targetPath)
      
      const newStats = await fs.promises.stat(targetPath)

      return {
        success: true,
        originalSize: originalStats.size,
        newSize: newStats.size
      }

    } catch (error) {
      return {
        success: false,
        error: error.message,
        originalSize: 0,
        newSize: 0
      }
    }
  }
}
