import Client from "App/Models/Client"
import got from "got/dist/source"
import loadSettings from "./loadSettings"

// const got = require('got')


import { randomInteger } from "./randomInteger"

interface makeURLParams {
    msg?: string
    to?: string
    json?: boolean
    api_id?: string
    test?: boolean
    ip?: string
}

interface SMSruResponse {
    status: string
    status_code: number
    sms: {
        [key:string]: {
            status: string
            status_code: number
            sms_id: string
        }
    }
    balance: number
}

export class SMSruClass {
    apiURL = 'https://sms.ru/sms/send?'
    URLSP = new URLSearchParams()

    constructor() {
        // this.URLSP.set('api_id', '1AEB2E9E-E56D-F62A-EB73-F7FDD67B6397')
        this.URLSP.set('json', 'true')
    }

    async makeURL(params: makeURLParams) {
        const { smsru_api_token } = await loadSettings(['smsru_api_token'])
        this.URLSP.set('api_id', smsru_api_token)

        Object.keys(params).map(key => params[key] && this.URLSP.set(key, params[key]))
        let apurl = this.apiURL + decodeURIComponent(this.URLSP.toString())
        
        return apurl
    }

    async balance(ip) {
        this.apiURL = 'https://sms.ru/my/balance?' 

        const url = await this.makeURL({ip})
        const res: SMSruResponse = await got(url).json()
        return res
    }

    async send(number, msg, ip) {       
        this.apiURL = 'https://sms.ru/sms/send?' 
        const url = await this.makeURL({msg, to: number, ip, test: false})
        const res: SMSruResponse = await got(url).json()
        return res
    }

    async restorePassword(clientIDM, ip) {
        const client = await Client.query()
                                    .where('client_number', String(clientIDM).trim())
                                    .orWhere('client_mail', String(clientIDM).trim())
                                    .firstOrFail()

        if (!client.client_phone || client.client_phone.length < 11) {
             throw new Error(`SMSru plugin: invalid client (${client.client_mail}) phone number`)
        }

        let newpwd = randomInteger(1120, 9989)
        let phone = String(client.client_phone).replace(/\D/gm, '')
        let msg = `Ваш новый пароль ${newpwd}. Номер клиента: ${client.client_number}`  

        // console.log('ss msg: ', msg)
        // console.log('msg length: ', msg.length)

        const smsRes = await this.send(phone, msg, ip)

        if (smsRes.status == 'OK') {
            // console.log('sms res:', smsRes);
            client.password = newpwd
            await client.save()

            return {
                msg: 'СМС с паролем отправлена на ваш номер'
            }
        } else {
            console.error('smsru res: ', smsRes)
            throw new Error('smsru error')
        }
    }
}