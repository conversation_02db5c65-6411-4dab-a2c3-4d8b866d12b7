import { getRange } from 'App/Helpers/getRange'

const _buildBySizeQuery = ({ builder, sizeField, sizeIn, sizeOut, sizeHeight, sizeTollerance, searchBySize }) => {

    if (searchBySize) {
        const _sizeHeight: any = sizeHeight > 0 ? sizeHeight : false
        const rangeArray: number[][] = getRange(sizeIn, sizeOut, _sizeHeight, sizeTollerance)

        if (rangeArray.length) {
            //rangeArray.forEach(sizes => builder.orWhere(sizeField, 'like', `${sizes[0]}*${sizes[1]}*${_sizeHeight || '%'}`))

            rangeArray.forEach(sizes => {
                if (!_sizeHeight) {
                    builder.orWhere(sizeField, 'like', `${sizes[0]}*${sizes[1]}%`)
                    builder.orWhere(sizeField, 'like', `${sizes[0]}*%/${sizes[1]}%`)

                    //let _sizes = sizes.map(s => String(s).replace('.', '\\.'))
                    //let [_in, _out] = _sizes
                    //builder.orWhere(sizeField, 'REGEXP', `^${_in}(\\*|(\/[0-9]*(\\.[0-9])?)\\*)${_out}.{1,}`)
                } else {
                    builder.orWhere(sizeField, 'like', `${sizes[0]}*${sizes[1]}*${sizes[2]}%`)
                    builder.orWhere(sizeField, 'like', `${sizes[0]}*${sizes[1]}/%*${sizes[2]}%`)

                    //let _sizes = sizes.map(s => String(s).replace('.', '\\.'))
                    //let [_in, _out, _h] = _sizes
                    //builder.orWhere(sizeField, 'REGEXP', `^${_in}(\\*|(\/[0-9]*(\\.[0-9])?)\\*)${_out}(\\*|(\/[0-9]*(\\.[0-9])?)\\*)${_h}(\/\\.{1,})?`)
                }
            })

/*             if (!_sizeHeight) {
                rangeArray.forEach(sizes => {
                    builder.orWhere(sizeField, 'like', `${sizes[0]}*${sizes[1]}%`)
                    builder.orWhere(sizeField, 'like', `${sizes[0]}/%*${sizes[1]}%*%`)
                })
            }
            else {
                //builder.orWhereIn(sizeField, rangeArray.map(item => item.join('*')))
            } */

        } else {
            builder.orWhere(sizeField, 'like', `${sizeIn}*${sizeOut}*${_sizeHeight || '%'}`)
        }
    }
}

export const buildBySizeQuery = _buildBySizeQuery