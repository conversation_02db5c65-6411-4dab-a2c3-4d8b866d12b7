import loadSettings from "./loadSettings"

export const changeCurrency = async (value: number, locale?) => {

    let currencies = await loadSettings(['currency'])
    console.log('changeCurrency currencies: ', currencies)


    if (!locale || locale == 'ru') {
        return value
    }

    try {
        return (value / currencies[locale == 'pl' ? 'zl' : 'eur']).toFixed(2)
    } catch (error) {
        return value
    }

    return value
}