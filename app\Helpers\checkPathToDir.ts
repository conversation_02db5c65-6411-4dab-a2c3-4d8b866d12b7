import fs from 'fs'
import path from 'path'

export const checkAndCreatePathToDir = (dirPath: string) => {
  const normalizedPath = path.normalize(dirPath)
  const parts = normalizedPath.split(path.sep)

  let currentPath = ''

  parts.forEach((part) => {
    if (part) {
      currentPath = currentPath ? path.join(currentPath, part) : part

      if (!fs.existsSync(currentPath)) {
        fs.mkdirSync(currentPath)
      }
    }
  })

  return normalizedPath
}
