import Application from '@ioc:Adonis/Core/Application'

import * as fs from 'fs'
import * as util from 'util'
import * as path from 'path'

import Env from '@ioc:Adonis/Core/Env'
import { transliterateToCyrillic } from './transliterate'

export class Files {
  UPLOAD_PATH: string

  constructor() {
    this.UPLOAD_PATH = process.env.NODE_ENV === 'production' ? path.resolve(Env.get('UPLOAD_PATH')) : Application.publicPath('data')
  }

  async deleteByPath(path) {
    try {
      fs.unlink(this.UPLOAD_PATH + path, (err) => {
        if (err) {
          return new Error('deleteByPath error')
        }
      })
    } catch (error) {
      return new Error(error)
    }
  }

  async getFiles(dir, page = 1, pageSize = 100) {
    // let _directory = `${this.UPLOAD_PATH}/${dir}/`

    let _directory = path.join(this.UPLOAD_PATH, dir)
    const readdir = util.promisify(fs.readdir)

    try {
      const files = await readdir(_directory)
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + pageSize
      const slicedFiles = files.slice(startIndex, endIndex)

      return slicedFiles.map((item) => ({
        name: item,
        dir,
        runame: transliterateToCyrillic(item.split('.')[0].replace(/_/g, ' ').replace(/[0-9]/gm, ''))
      }))
    } catch (error) {
      console.log("files.ts:47 ~ Files ~ getFiles ~ error:", error)
      return []
    }
  }
}
