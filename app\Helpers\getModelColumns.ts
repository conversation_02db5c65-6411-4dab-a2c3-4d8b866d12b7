import { LucidModel } from '@ioc:Adonis/Lucid/Orm'

export const getModelColumns = (Model: LucidModel, withTimestamps?) => {
    
    let columns = [...Model.$columnsDefinitions.keys()]
    
    if (!withTimestamps) {
        columns = columns.filter(i => i !== 'updatedAt' && i !== 'createdAt' && i !== 'rememberMeToken')
    } else {
        columns[columns.indexOf('updatedAt')] = 'updated_at'
        columns[columns.indexOf('createdAt')] = 'created_at'
        columns[columns.indexOf('rememberMeToken')] = 'remember_me_token'
    }
                                                        
    return columns.map(i => Model.table + '.' + i).filter(x => x)                                                
}

export const getModelKeys = (Model: LucidModel, withTimestamps?) => {
    let columns = [...Model.$columnsDefinitions.keys()]

    if (!withTimestamps) {
        columns = columns.filter(i => i !== 'updatedAt' && i !== 'createdAt' && i !== 'rememberMeToken')
    } else {
        columns[columns.indexOf('updatedAt')] = 'updated_at'
        columns[columns.indexOf('createdAt')] = 'created_at'
        columns[columns.indexOf('rememberMeToken')] = 'remember_me_token'
    }

    return columns.filter(x => x)
}