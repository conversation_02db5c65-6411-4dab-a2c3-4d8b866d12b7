export const isSize = (str: string): boolean => {
  const splitRegexp = /\*|x|х|X|Х|\s|на|НА|-|:/gm
  const dotregexp = /\,{1,}/gm
  const doubleZeroRegexp = /^00+/

  let sizes: string[] = []
  let checkArr = String(str)
    .replace(dotregexp, '.')
    .split(splitRegexp)
    .filter((x) => x)

  checkArr.map((i) => sizes.push(...i.split('/')))

  let isNums = sizes.every((i) => {
    // Check for double leading zeros
    if (doubleZeroRegexp.test(i)) {
      return false
    }

    const num = Number(i)
    return !isNaN(num) && num < 1000
  })

  return isNums && sizes.length >= 2
}

// console.log('44.45*57.15*7.9', isSize('44.45*57.15*7.9'))

// console.log('44.45x57.15x7.9', isSize('44.45x57.15x7.9'))
// console.log('20x30x10',isSize('20x30x10'))

// console.log('57.15*76.2-9.5/11.2', isSize('57.15*76.2-9.5/11.2'))

// console.log('8*18*5', isSize('8*18*5'))
// console.log('32*',isSize('32*'))
// console.log('41*54.2/59.5*6/12.5', isSize('41*54.2/59.5*6/12.5'))

// console.log('20x30x1001',isSize('20x30x1001'))
// console.log('20x30',isSize('20x30'))

// console.log('93102-25018-09',isSize('93102-25018-09'))
