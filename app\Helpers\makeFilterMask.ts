export const maskBuilder = {
    builder: undefined,
    //let query: DatabaseQueryBuilderContract = this.builder
    query: function (_builder) {
        this.builder = _builder
        return this
    },
    startsWith: function(q, field) { this.builder.where(field, 'like' ,`${q}%`) },
    contains: function (q, field) { this.builder.where(field, 'like', `%${q}%`) },
    notContains: function (q, field) { this.builder.whereNot(field, 'like', `%${q}%`) },
    endsWith: function (q, field) { this.builder.where(field, 'like', `%${q}`) },
    equals: function (q, field) { this.builder.where(field, q) },
    notEquals: function (q, field) { this.builder.where(field, '!=', q) },
    noFilter: () => {}
}