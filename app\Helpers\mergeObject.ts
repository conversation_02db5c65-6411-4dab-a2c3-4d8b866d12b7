// const _f = (payloadItems, idField, field) => {
//         let res = payloadItems.map(item => {
//             let duplicats = payloadItems.filter(x => x[idField] == item[idField])
//             let totalQty = duplicats.reduce((a, b) => a + Number(b[field]), 0)
//             duplicats.map((i, index) => {
//                 if (index === 0) {
//                     item[field] = totalQty
//                 } else {
//                     let foundIndex = payloadItems.findIndex(x => x[idField] == i[idField])
//                     if (foundIndex > -1) {
//                         payloadItems.splice(foundIndex, 1)
//                     }
//                 }
//             })
//             return item
//         })
//         return res.filter(i => i)
// }

const _f = (payloadItems, idField, field) => {
      const itemsMap = new Map()

      payloadItems.forEach((item) => {
        const id = item[idField]
        if (!itemsMap.has(id)) {
          itemsMap.set(id, { ...item, [field]: 0 })
        }
        itemsMap.get(id)[field] += Number(item[field])
      })
      
      return Array.from(itemsMap.values())
}

export const mergeObjects = _f