import { DatabaseQueryBuilderContract } from '@ioc:Adonis/Lucid/Database'

export const rebuildBySizeQuery = (builder: DatabaseQueryBuilderContract, { sizeIn, sizeIn_2, sizeOut, sizeOut_2, sizeHeight, sizeHeight_2, sizeTollerance }) => {
  // console.log('rebuildBySizeQuery: ', {sizeIn, sizeIn_2, sizeOut, sizeOut_2, sizeHeight, sizeHeight_2, sizeTollerance })

  builder.where((query) => {
    query.whereBetween('size_in', [sizeIn - sizeTollerance, sizeIn + sizeTollerance]).orWhereBetween('size_in_2', [sizeIn - sizeTollerance, sizeIn + sizeTollerance])
    if (sizeIn_2) {
      builder.andWhereBetween('size_in_2', [sizeIn_2 - sizeTollerance, sizeIn_2 + sizeTollerance])
    }
  })

  builder.andWhere((query) => {
    if (sizeOut) {
      query.andWhereBetween('size_out', [sizeOut - sizeTollerance, sizeOut + sizeTollerance]).orWhereBetween('size_out_2', [sizeOut - sizeTollerance, sizeOut + sizeTollerance])
      if (sizeOut_2) {
        query.andWhereBetween('size_out_2', [sizeOut_2 - sizeTollerance, sizeOut_2 + sizeTollerance])
      }
    }
  })

  builder.andWhere((query) => {
    if (sizeHeight) {
      query
        .andWhereBetween('size_h', [sizeHeight - sizeTollerance, sizeHeight + sizeTollerance])
        .orWhereBetween('size_h_2', [sizeHeight - sizeTollerance, sizeHeight + sizeTollerance])

      if (sizeHeight_2) {
        query.orWhereBetween('size_h_2', [sizeHeight_2 - sizeTollerance, sizeHeight_2 + sizeTollerance])
      }
    }
  })

  // builder.whereBetween('size_in', [sizeIn - sizeTollerance, sizeIn + sizeTollerance])
  // builder.andWhereBetween('size_out', [sizeOut - sizeTollerance, sizeOut + sizeTollerance])
}
