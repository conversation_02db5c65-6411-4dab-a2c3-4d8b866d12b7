import CartItem from 'App/Models/CartItem'

async function _fcqty(idField, cartId, products:any) {

    let _products = [...products]

    _products.map(product => product.qty = 0)

    const cartItems = await CartItem.query().select('qty', 'prod_id').where(idField, cartId)

    cartItems.map(item => {
        let found = _products.find(x => x.prod_id == item.prod_id)
        if (found) found.qty = item.qty
    })

}

export const setProdCartQty = _fcqty