export function transliterateToCyrillic(latinText) {
  const transliterationMap = {
    'a': 'а',
    'b': 'б',
    'v': 'в',
    'g': 'г',
    'd': 'д',
    'e': 'е',
    'yo': 'ё',
    'zh': 'ж',
    'z': 'з',
    'i': 'и',
    'y': 'й',
    'k': 'к',
    'l': 'л',
    'm': 'м',
    'n': 'н',
    'o': 'о',
    'p': 'п',
    'r': 'р',
    's': 'с',
    't': 'т',
    'u': 'у',
    'f': 'ф',
    'h': 'х',
    'c': 'ц',
    'ch': 'ч',
    'sh': 'ш',
    'shch': 'щ',
    'yi': 'ы',
    // 'e': 'е',
    'yu': 'ю',
    'ya': 'я'
  }

  const words = latinText.split(' ')
  const cyrillicWords = []

  for (const word of words) {
    let cyrillicWord = ''

    for (let i = 0; i < word.length; i++) {
      let currentChar = word[i]
      let nextChar = word[i + 1]

      if (nextChar && transliterationMap.hasOwnProperty(currentChar + nextChar)) {
        cyrillicWord += transliterationMap[currentChar + nextChar]
        i++ // Skip the next character since it has already been transliterated
      } else if (transliterationMap.hasOwnProperty(currentChar)) {
        cyrillicWord += transliterationMap[currentChar]
      } else {
        cyrillicWord += currentChar
      }
    }

    cyrillicWords.push(cyrillicWord)
  }

  return cyrillicWords.join(' ')
}
