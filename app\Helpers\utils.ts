export const flattenObject = function (ob: Object, prefix?: string, result?) {
    result = result || {}

    // Preserve empty objects and arrays, they are lost otherwise
    if (prefix && typeof ob === 'object' && ob !== null && Object.keys(ob).length === 0) {
        result[prefix] = Array.isArray(ob) ? [] : {}
        return result;
    }

    prefix = prefix ? prefix + '.' : '';

    for (const i in ob) {
        if (Object.prototype.hasOwnProperty.call(ob, i)) {
            if (typeof ob[i] === 'object' && ob[i] !== null) {
                // Recursion on deeper objects
                flattenObject(ob[i], prefix + i, result)
            } else {
                result[prefix + i] = ob[i]
            }
        }
    }
    return result
}

export const unflattenObject = function (ob: Object) {
        const result = {}

        for (const i in ob) {
            if (Object.prototype.hasOwnProperty.call(ob, i)) {
                const keys = i.match(/^\.+[^.]*|[^.]*\.+$|(?:\.{2,}|[^.])+(?:\.+$)?/g) // Just a complicated regex to only match a single dot in the middle of the string
                keys?.reduce((r, e, j) => {
                    return r[e] || (r[e] = isNaN(Number(keys[j + 1])) ? (keys.length - 1 === j ? ob[i] : {}) : [])
                }, result)
            }
        }

        return result
}

export const findValInObject = function(object: Object, key: string) {
    let value

    Object.keys(object).some(function(k) {
        if (k === key) {
            value = object[k]
            return true
        }
        if (object[k] && typeof object[k] === 'object') {
            value = findValInObject(object[k], key)
            return value !== undefined
        }
    })

    return value
}

export const findValInObjByKeysArray = (obj: Object, keys: string | string[]) => {
    let _keys: string[]
  
    _keys = Array.isArray(keys) ? keys : keys.split('.')
  
    let acc = {...obj} //JSON.parse(JSON.stringify(obj))
    _keys.map(key => {
      if (acc?.hasOwnProperty(key)) {
        acc = acc[key]
      }
    })
  
    return acc
}