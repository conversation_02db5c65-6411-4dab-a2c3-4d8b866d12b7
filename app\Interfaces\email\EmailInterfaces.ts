export interface EmailSenderData {
  lastSendDate: string
  message: string
  subject: string
  recipients: Recipient[]
  scheduleTime: number // для интервалов в минутах
  scheduleType: 'interval' | 'weekly'
  weeklySchedule?: number[] // массив дней недели (0-6, где 0=воскресенье)
  enabled: boolean
}

export interface Recipient {
  email: string
  note: string
  createdAt: string
  isActive: boolean
}

export interface FailedEmail {
  recipient: Recipient
  subject: string
  message: string
  attempts: number
  lastAttempt: Date
  campaignId?: string
  checkpointIndex?: number
}

export interface EmailSendResult {
  success: boolean
  sentCount: number
  failedCount: number
  queuedForRetry: number
  campaignId: string
  lastCheckpoint: number
  errors: EmailError[]
}

export interface EmailError {
  recipientEmail: string
  error: string
  timestamp: Date
  isTemporary: boolean
}

export interface EmailCampaignConfig {
  batchSize: number
  delayBetweenBatches: number
  delayBetweenEmails: number
  maxRetries: number
  retryDelay: number
  enableCheckpoints: boolean
  validateEmails: boolean
}

export interface EmailValidationResult {
  isValid: boolean
  error?: string
  suggestion?: string
}

export interface CampaignProgress {
  campaignId: string
  totalRecipients: number
  processed: number
  sent: number
  failed: number
  pending: number
  currentBatch: number
  lastCheckpoint: number
  startedAt: Date
  estimatedCompletion?: Date
  status: 'running' | 'paused' | 'completed' | 'failed' | 'cancelled'
}

export interface EmailTemplate {
  subject: string
  htmlContent: string
  textContent?: string
  variables?: Record<string, any>
}

export interface EmailServiceConfig {
  mailerName: string
  fromEmail: string
  fromName: string
  replyTo?: string
  defaultConfig: EmailCampaignConfig
}

export interface RetryConfig {
  maxAttempts: number
  baseDelay: number // базовая задержка в мс
  maxDelay: number // максимальная задержка в мс
  exponentialBackoff: boolean
  temporaryErrorCodes: (string | number)[]
}

export interface EmailMetrics {
  totalSent: number
  totalFailed: number
  successRate: number
  averageDeliveryTime: number
  bounceRate: number
  retryRate: number
  lastCampaignDate?: Date
}
