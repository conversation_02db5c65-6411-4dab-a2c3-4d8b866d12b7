/**
 * Упрощенные интерфейсы для Email Processing Service
 */

// Ключевые слова для фильтрации писем
export interface KeywordConfig {
  required: string[]  // Обязательные слова
  optional: string[]  // Дополнительные слова
  exclude?: string[]  // Исключающие слова
}

// Простая структура письма
export interface EmailData {
  messageId: string
  from: string
  subject: string
  body: string
  receivedDate: Date
  uid?: number  // UID письма для операций перемещения
}

// Результат анализа письма
export interface EmailAnalysis {
  isMatch: boolean
  score: number
  matchedKeywords: string[]
}

// Простой интерфейс сервиса
export interface IEmailMonitorService {
  start(): Promise<void>
  stop(): Promise<void>
  isRunning(): boolean
  processEmails(): Promise<void>
}

// Простые ключевые слова для коммерческих запросов
export const DEFAULT_KEYWORDS: KeywordConfig = {
  required: [
    'коммерческое предложение',
    'стоимост',
    'цена',
    'цены',
    'счет',
    'счёт',
    'наличи',
    'поставк',
    'cориентируйте',
    'приобрести',
    'интересует',
    'просьба обработать',
    'нужен сальник',
    'нужен подшиник',
    'нужна манжета'
  ],
  optional: [],
  exclude: ['вы получили оплату', 'состав заказа', 'трекинг', 'новый заказ', 'реклама', 'акция', 'распродажа', 'бесплатно', 'выиграли', 'лотерея', 'оплачен', 'юkassa', 'сверки']
}
