import Product from 'App/Models/Product'

export interface SnapshotBodyItemInterface {
  ID?: number
  id: number

  cancelgtd: boolean
  created_at: string
  gtd: number

  item_count?: number
  item_id?: number
  items_order_id?: number

  orderCount?: number
  qty?: number
  _qty?: number

  updated_at: string
  prod_morecats: string
  size_in: number
  size_in_2: number
  size_out: number
  size_out_2: number
  size_h: number
  size_h_2: number
  prod_buy_limit: number
  prod_supplier: string

  currentState?: Partial<Product> | undefined | null

  prod_analogsku: string
  prod_cat: number
  prod_cell: number
  prod_coeff: number
  prod_count: number

  prod_discount: number
  whosaleprice?: number
  discount?: number

  prod_group: string
  prod_group_count: number
  prod_group_gtd_qty: number
  prod_group_price: number
  prod_gtd_alert: number
  prod_gtd_qty: number
  prod_id: number
  prod_purpose: string
  prod_manuf: string
  prod_material: string
  prod_minalert: number
  prod_model: string
  prod_price: number
  prod_purchasing: number
  prod_size: string
  prod_sku: string
  prod_images: string
  prod_type: string
  prod_uses: string
  prod_weight: number
  prod_year: string

  timestamp: string

  notExported?: boolean
}
