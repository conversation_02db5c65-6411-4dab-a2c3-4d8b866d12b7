import { 
  OzonProduct, 
  OzonStock, 
  OzonPrice, 
  OzonProductInfo, 
  OzonImageUpload,
  OzonSettings,
  OzonSyncLog,
  OzonApiResponse,
  OzonProductImportRequest,
  OzonProductInfoRequest,
  OzonStockInfoRequest,
  OzonStockUpdate,
  OzonPriceUpdate,
  OzonImageUploadResponse,
  OzonCategoryTree
} from './OzonTypes'

// Интерфейс для HTTP клиента
export interface IOzonClient {
  // Основные HTTP методы
  get<T = any>(endpoint: string, params?: any): Promise<OzonApiResponse<T>>
  post<T = any>(endpoint: string, data?: any): Promise<OzonApiResponse<T>>
  
  // Методы для работы с товарами
  importProducts(request: OzonProductImportRequest): Promise<OzonApiResponse<any>>
  getProductInfo(request: OzonProductInfoRequest): Promise<OzonApiResponse<{ items: OzonProductInfo[] }>>
  
  // Методы для работы с остатками
  updateStocks(request: OzonStockUpdate): Promise<OzonApiResponse<any>>
  getStocksInfo(request: OzonStockInfoRequest): Promise<OzonApiResponse<any>>
  
  // Методы для работы с ценами
  updatePrices(request: OzonPriceUpdate): Promise<OzonApiResponse<any>>
  
  // Методы для работы с изображениями
  uploadImages(images: OzonImageUpload[]): Promise<OzonApiResponse<OzonImageUploadResponse>>
  getImagesInfo(fileNames: string[]): Promise<OzonApiResponse<any>>
  
  // Методы для работы с категориями
  getCategoryTree(categoryId?: number, language?: string): Promise<OzonApiResponse<OzonCategoryTree>>
  getCategoryAttributes(categoryId: number, language?: string): Promise<OzonApiResponse<any>>
}

// Интерфейс для провайдера
export interface IOzonProvider {
  // Синхронизация товаров
  syncProducts(productIds: number[]): Promise<{
    success: number[]
    failed: { productId: number, error: string }[]
    total: number
  }>
  
  // Создание товара
  createProduct(productData: OzonProduct): Promise<{
    success: boolean
    productId?: number
    error?: string
  }>
  
  // Обновление товара
  updateProduct(offerId: string, productData: Partial<OzonProduct>): Promise<{
    success: boolean
    error?: string
  }>
  
  // Обновление остатков
  updateStocks(stocks: OzonStock[]): Promise<{
    success: boolean
    updated: number
    failed: { offerId: string, error: string }[]
  }>
  
  // Обновление цен
  updatePrices(prices: OzonPrice[]): Promise<{
    success: boolean
    updated: number
    failed: { offerId: string, error: string }[]
  }>
  
  // Получение информации о товаре
  getProductInfo(offerId: string): Promise<OzonProductInfo | null>
  
  // Получение статуса товара
  getProductStatus(offerId: string): Promise<{
    status: string
    errors: string[]
    isActive: boolean
  }>
  
  // Загрузка изображений
  uploadProductImages(productId: number): Promise<{
    success: boolean
    uploadedImages: string[]
    failed: { fileName: string, error: string }[]
  }>
}

// Интерфейс для сервиса
export interface IOzonService {
  // Маппинг данных
  mapProductToOzon(product: any): Promise<OzonProduct>
  mapOzonToProduct(ozonData: OzonProductInfo): Promise<any>
  
  // Валидация данных
  validateProductData(data: OzonProduct): Promise<{
    isValid: boolean
    errors: string[]
  }>
  
  // Работа с изображениями
  uploadProductImages(product: any): Promise<{
    success: boolean
    imageUrls: string[]
    errors: string[]
  }>
  
  getImageUrls(product: any): Promise<string[]>
  
  // Логирование
  logSyncOperation(productId: number, action: string, status: string, error?: string): Promise<void>
  getSyncHistory(productId?: number, limit?: number): Promise<OzonSyncLog[]>
  
  // Настройки
  getSettings(): Promise<OzonSettings>
  updateSettings(settings: Partial<OzonSettings>): Promise<void>
}

// Интерфейс для конфигурации
export interface IOzonConfig {
  getSettings(): Promise<OzonSettings>
  getSetting<T = any>(key: keyof OzonSettings): Promise<T>
  updateSetting(key: keyof OzonSettings, value: any): Promise<void>
  isEnabled(): Promise<boolean>
}

// Интерфейс для работы с изображениями
export interface IOzonImageService {
  // Получение локальных изображений
  getLocalImages(product: any): Promise<{
    mainImage?: string
    additionalImages: string[]
    imagePaths: string[]
  }>
  
  // Загрузка изображений в Ozon
  uploadToOzon(imagePaths: string[]): Promise<{
    success: boolean
    uploadedImages: { fileName: string, url: string }[]
    failed: { fileName: string, error: string }[]
  }>
  
  // Проверка существования файлов
  checkImageExists(imagePath: string): Promise<boolean>
  
  // Получение URL для доступа к изображениям
  getImageUrl(imageName: string, type: 'rti' | 'rumi'): string
}

// Интерфейс для логирования
export interface IOzonLogger {
  log(productId: number, action: string, status: string, error?: string, additionalData?: any): Promise<void>
  getHistory(productId?: number, limit?: number, offset?: number): Promise<OzonSyncLog[]>
  clearOldLogs(daysToKeep?: number): Promise<number>
}

// Интерфейс для валидации
export interface IOzonValidator {
  validateProduct(product: OzonProduct): Promise<{
    isValid: boolean
    errors: string[]
    warnings: string[]
  }>
  
  validateStock(stock: OzonStock): Promise<{
    isValid: boolean
    errors: string[]
  }>
  
  validatePrice(price: OzonPrice): Promise<{
    isValid: boolean
    errors: string[]
  }>
  
  validateSettings(settings: Partial<OzonSettings>): Promise<{
    isValid: boolean
    errors: string[]
  }>
}

// Интерфейс для обработки ошибок
export interface IOzonErrorHandler {
  handleApiError(error: any, context: string): Promise<{
    shouldRetry: boolean
    retryAfter?: number
    errorMessage: string
  }>
  
  isRetryableError(error: any): boolean
  getErrorMessage(error: any): string
}

// Интерфейс для rate limiting
export interface IOzonRateLimiter {
  canMakeRequest(): Promise<boolean>
  waitForNextRequest(): Promise<void>
  recordRequest(): void
  getRemainingRequests(): number
  getResetTime(): Date
}

// Типы для результатов операций
export interface SyncResult {
  success: boolean
  productId: number
  offerId: string
  action: string
  error?: string
  ozonProductId?: number
  uploadedImages?: string[]
}

export interface BatchSyncResult {
  total: number
  successful: number
  failed: number
  results: SyncResult[]
  errors: string[]
}

// Интерфейс для batch операций
export interface IOzonBatchProcessor {
  processBatch<T, R>(
    items: T[], 
    processor: (item: T) => Promise<R>, 
    batchSize?: number,
    delayBetweenBatches?: number
  ): Promise<R[]>
  
  syncProductsBatch(productIds: number[], batchSize?: number): Promise<BatchSyncResult>
  updateStocksBatch(stocks: OzonStock[], batchSize?: number): Promise<BatchSyncResult>
  updatePricesBatch(prices: OzonPrice[], batchSize?: number): Promise<BatchSyncResult>
}

// Интерфейс для мониторинга
export interface IOzonMonitor {
  getStats(): Promise<{
    totalProducts: number
    syncedProducts: number
    failedProducts: number
    lastSyncTime: Date
    averageSyncTime: number
    errorRate: number
  }>
  
  getHealthStatus(): Promise<{
    isHealthy: boolean
    apiStatus: 'ok' | 'error' | 'timeout'
    lastApiCall: Date
    errors: string[]
  }>
}

// Экспорт всех интерфейсов
export {
  IOzonClient,
  IOzonProvider,
  IOzonService,
  IOzonConfig,
  IOzonImageService,
  IOzonLogger,
  IOzonValidator,
  IOzonErrorHandler,
  IOzonRateLimiter,
  IOzonBatchProcessor,
  IOzonMonitor,
  SyncResult,
  BatchSyncResult
}
