import CartItem from "App/Models/CartItem";
import { shippingInterface } from 'App/Interfaces/orders/shippingInterface';

export interface regOrderInterface {
    orderNumber: number
    amount: number
    returnUrl?: string
    failUrl?: string
    client: {
        email: string,
        phone?: string
    }
    cartItems: Array<CartItem>
    pageView?: string,
    shipping: shippingInterface | undefined
}