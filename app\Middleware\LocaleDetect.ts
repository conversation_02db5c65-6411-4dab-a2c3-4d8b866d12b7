import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Client from 'App/Models/Client'

export default class LocaleDetect {
  public async handle({ request, response }: HttpContextContract, next: () => Promise<void>) {

    try {
      const locale = request.headers()['x-locale'] || request.cookiesList()?.locale || request.qs()?.locale
      request['locale'] = locale
    } catch (error) {
    }

    await next()
  }
}
