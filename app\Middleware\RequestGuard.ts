import Env  from '@ioc:Adonis/Core/Env'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

const tokenTimeout = Env.get('API_TOKEN_TIMEOUT') || 10
const apiTokenGuard = Env.get('API_TOKEN_GUARD') === 'true'


export default class RequestGuard {
  public async handle({ request, response }: HttpContextContract, next: () => Promise<void>) {
    // code for middleware goes here. ABOVE THE NEXT CALL
    try {    
      if (apiTokenGuard) {
        // console.log('headers:: ', request.headers())
        
        let timestamp_token = Number(request.headers()['x-session']) / 0.27
        let now = Math.floor(Date.now() / 1000)

        if (!timestamp_token || (timestamp_token + Number(tokenTimeout)) < now) {
            console.warn('API token ERROR: ' + request.ips() + ' | req.url: ', request.url() + ' | UA: ' + request.headers()['user-agent'])

            return response.status(500).send('Internal Server Error')
        }
      }
    } catch (error) { 
      console.error('RequestGuard: ', error)
    }

    await next()
  }
}
