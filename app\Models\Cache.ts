import { DateTime } from 'luxon'
import { BaseModel, afterFetch, afterFind, beforeCreate, beforeSave, column } from '@ioc:Adonis/Lucid/Orm'

export default class Cache extends BaseModel {
  public static table = 'cache'

  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public ckey: string

  @column()
  public hash?: string

  @column()
  public body: string

  @beforeCreate()
  public static beforeCreateHook(cache: Cache) {
    if (typeof cache.body != 'string') {
      cache.body = JSON.stringify(cache.body)
    }
  }

  @beforeSave()
  public static beforeSaveHook(cache: Cache) {
    if (typeof cache.body != 'string') {
      cache.body = JSON.stringify(cache.body)
    }
  }

  @afterFetch()
  public static async afterFetchHook(caches: Cache[]) {
    caches.map((cache) => {
      try {
        cache.body = JSON.parse(cache.body)
      } catch (error) {
        console.warn(error)
      }
    })
  }

  @afterFind()
  public static async afterFindHook(cache: Cache) {
    try {
      cache.body = JSON.parse(cache.body)
    } catch (error) {
      console.warn(error)
    }
  }
}
