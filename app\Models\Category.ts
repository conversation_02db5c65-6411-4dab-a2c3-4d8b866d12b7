import Filter from 'App/Models/Filter'
import { DateTime } from 'luxon'
import { BaseModel, column, afterFetch, afterFind } from '@ioc:Adonis/Lucid/Orm'
import Database from '@ioc:Adonis/Lucid/Database'
import HttpContext from '@ioc:Adonis/Core/HttpContext'
import LangDict from 'App/Models/LangDict'
import Product from 'App/Models/Product'

export default class Category extends BaseModel {
  public static table = 'cats'

  @column({ isPrimary: true })
  public cat_id: number

  /*   @column.dateTime({ autoCreate: true })
  public createdAt: DateTime */

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public cat_title: string

  @column()
  public cat_pic: string

  @column()
  public cat_base64_pic: string

  @column()
  public cat_base_64_pic?: string

  @column()
  public cat_sort: number

  @column()
  public cat_search_sort: number

  @column()
  public cat_rootcat: number

  @column()
  public cat_active: boolean

  @column({ columnName: 'cat_url_ru' })
  public cat_url: string

  @column()
  public cat_note: string

  @column()
  public subcategories?: any

  @column()
  public imagesize?: string

  @column()
  public isnormal?: boolean

  @column()
  public duplicate?: boolean

  @afterFind()
  public static async afterFindHook(category) {
    await this.fetchHandler([category])
  }

  @afterFetch()
  public static async afterFetchHook(categories: Category[]) {
    await this.fetchHandler(categories)
  }

  public static async fetchHandler(categories: Category[]) {
    const ctx = HttpContext.get()!

    let locale = ctx.request.locale

    try {
      if (locale && locale != 'ru') {
        categories.map((category) => {
          if (category.$extras['cat_url_' + locale]) {
            category.cat_url = category.$extras['cat_url_' + locale]
          }
        })

        await LangDict.categoriesTranslate(categories, locale)
      }
    } catch (error) {
      console.log(error)
    }
  }

  async merger({ categories, products }: { categories: Category[]; products: Product[] }) {
    interface CProds {
      index: number
      product: Partial<Product>
    }

    let _categories = [...categories]
    let _products: CProds[] = [...products].map((product, index) => ({ product: product.toJSON ? product.toJSON() : product, index })) //Array.from(products, i => i)

    const categoryInds = _products.map((item) => Number(item.product.prod_cat))

    _categories.forEach((category) => (category.products = _products.filter((x) => x.product.prod_cat == category.cat_id)))
    // _categories.sort((a, b) => (a.cat_search_sort > b.cat_search_sort) ? 1 : -1)
    _categories.sort((a, b) => (categoryInds.indexOf(Number(a.cat_id)) > categoryInds.indexOf(Number(b.cat_id)) ? 1 : -1))

    // ------- 22.02.2023 - merge normal categories
    let defCategory: any = _categories.find((x) => x.cat_id == 9999)

    if (!defCategory) {
      defCategory = {} //new Category()
      defCategory.cat_title = ''
      defCategory.cat_id = 9999
      defCategory.products = []
      defCategory.columns = await Category.getCategoryColumns(9999, false, true)
    }

    const defCategoryIndex = _categories.findIndex((x) => x.cat_id == 9999)

    _categories.forEach((category: any) => {
      if (category.isnormal) {
        defCategory.products = defCategory.products.concat(category.products)
        category.products = []
      }
    })

    _categories.splice(defCategoryIndex, 1)
    _categories.unshift(defCategory)

    _categories.map((category) => {
      category.products.sort((a, b) => (a.index > b.index ? 1 : -1))
      category.products = category.products.map((i) => i.product)
    })

    // ------- ./ 22.02.2023
    const res = _categories.filter((cat) => cat.products.length > 0)
    return res
  }

  async getCategoryMeta(id /* , locale = 'ru' */) {
    //const idField: string = 'cat_id'

    const categoryMeta: object = Category.find(id)

    return categoryMeta
  }

  async getAllgetCategories() {
    const res = await Category.all()
    return res
  }

  async getCategories(params, withAnalogColumn = false) {
    let activeField: string = 'cat_active',
      idField: string = 'cat_id'

    const defColumns = await Category.getCategoryColumns(9999, withAnalogColumn, params.isSearch)

    async function insertColumns(category: Category) {
      if (category.isnormal) {
        category['columns'] = defColumns
      } else {
        category['columns'] = await Category.getCategoryColumns(category.cat_id, withAnalogColumn, params.isSearch)
      }
    }

    let res = await Category.query()
      .where(activeField, 1)
      .andWhere((builder) => {
        if (params.ids) builder.andWhereIn(idField, params.ids)
        if (params.isNotDuplicate) {
          builder.andWhere('duplicate', false)
        }
      })
      .orderBy('cat_sort', 'asc')

    if (params.withColumns && !!res.length) {
      let fetchCategories = res.map((item) => item.toJSON())
      await Promise.all(fetchCategories.map(insertColumns))

      return fetchCategories
    } else {
      return res
    }
  }

  public static async getCategoryFilters(id: number) {
    const _filter = new Filter()
    let fieldlist: Array<object> = await _filter.getList(id)

    return fieldlist
  }

  public static async getCategoryColumns(id: number, withAnalogColumn = false, isSearch) {
    const httpCtx = HttpContext.get()
    const locale = httpCtx?.request.headers()['x-locale'] || undefined
    const isRumisota = locale && locale != 'ru'

    let fields: Array<string> = ['keyname', 'title', 'sort', 'sorted', 'slot', 'cat_id']

    let columns_table: string = 'cat_columns'
    let idField: string = 'cat_id'
    let skuField: string = 'prod_sku'
    let oemField: string = 'prod_analogsku'
    let keyField: string = 'keyname'
    let standardColumnsId: string = '9999'

    const trx = await Database.transaction()

    let columns = await Database.query()
      .useTransaction(trx)
      .select(fields)
      .from(columns_table)
      .where(idField, id)
      .if(id == 9999, (query) => query.orderBy('sort', 'asc'))

    if (columns.length === 0) {
      columns = await Database.query()
        .useTransaction(trx)
        .select(fields)
        .from(columns_table)
        .where(idField, standardColumnsId)
        .if(id == 9999, (query) => query.orderBy('sort', 'asc'))
    }

    await trx.commit()

    if (withAnalogColumn && columns.findIndex((x) => x[keyField] == oemField) == -1 && !isRumisota) {
      let _sku = columns.find((x) => x[keyField] == skuField)

      if (_sku) {
        let _skuIndex = columns.findIndex((x) => x[keyField] == skuField)
        //_sku.title = 'Аналог'
        columns.splice(_skuIndex + 1, 0, {
          [keyField]: oemField,
          title: 'Аналог', //'Код',
          sort: 0,
          sorted: 1
        })
      }
    }

    if (isRumisota && isSearch) {
      let _sku = columns.find((x) => x[keyField] == skuField)

      if (_sku) {
        let _skuIndex = columns.findIndex((x) => x[keyField] == skuField)
        //_sku.title = 'Аналог'
        columns.splice(_skuIndex + 1, 0, {
          [keyField]: 'sku',
          title: 'Аналог', //'Код',
          sort: 0,
          sorted: 0
        })
      }
    }

    return columns
  }

  static async getFullUrl(_category) {
    let url: String[] = []

    const getUrl = async (category) => {
      url.push(category.cat_url)

      const categories = await this.query().where('cat_id', category.cat_rootcat)
      await Promise.all(categories.map(getUrl))
    }

    await getUrl(_category)

    return url.reverse().join('/')
  }

  static async getChildCats(id) {
    const res = await this.query().where('cat_rootcat', id).andWhere('cat_active', true)
    return res
  }
}
