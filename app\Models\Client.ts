import { DateTime } from 'luxon'
import Hash from '@ioc:Adonis/Core/Hash'
import { column, beforeSave, BaseModel, hasOne, HasOne, belongsTo, BelongsTo, hasMany, HasMany, manyToMany, ManyToMany } from '@ioc:Adonis/Lucid/Orm'

import Org from 'App/Models/Org'
import Order from 'App/Models/Order'

import { randomInteger } from 'App/Helpers/randomInteger'
import Mail from '@ioc:Adonis/Addons/Mail'
import Env from '@ioc:Adonis/Core/Env'
import HttpContext from '@ioc:Adonis/Core/HttpContext'
import i18next from 'i18next'
import Database from '@ioc:Adonis/Lucid/Database'
import Passport from 'App/Models/Passport'
import { PassportData } from 'App/Interfaces/client/Passport'

const EMAIL_FROM = Env.get('EMAIL_FROM')

export default class Client extends BaseModel {
  @column({ isPrimary: true })
  public client_id: number

  @column()
  public client_mail: string

  @column()
  public client_phone: string

  @column()
  public client_country: string

  @column()
  public client_adress: string

  @column()
  public client_city: string

  @column()
  public client_street: string

  @column()
  public client_house: string

  @column()
  public client_flat: string

  @column()
  public client_postindex: string

  @column({ serializeAs: null, columnName: 'client_password' })
  public password: string

  @column()
  public rememberMeToken?: string

  @column()
  public client_name: string

  @column()
  public client_discount: number

  @column()
  public client_number: string

  @column()
  public client_cdekid: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @hasMany(() => Order, {
    foreignKey: 'order_client',
    localKey: 'client_id'
  })
  public orders: HasMany<typeof Order>

  @hasMany(() => Order, {
    foreignKey: 'order_client',
    localKey: 'client_id',
    onQuery: (query) => query.whereNotIn('order_status', ['Завершен', 'Оплачен', 'Не обработан', 'Отменен', 'Отправлен'])
  })
  public unpaid_orders: HasMany<typeof Order>

  @hasMany(() => Order, {
    foreignKey: 'order_client',
    localKey: 'client_id',
    onQuery: (query) => {
      query.select(Database.raw('sum(order_price + order_shippingprice) as orders_sum, count(order_id) as orders_count')).where('order_status', '!=', 'Отменен')
    }
  })
  public sum_orders: HasMany<typeof Order>

  @hasOne(() => Org, {
    foreignKey: 'org_client',
    localKey: 'client_number'
  })
  public org: HasOne<typeof Org>

  @hasOne(() => Passport, {
    foreignKey: 'client_id',
    localKey: 'client_id'
  })
  public passport: HasOne<typeof Passport>

  @beforeSave()
  public static async beforeSaveHook(client: Client) {
    if (client.$isNew || !client.password || client.$dirty.password) {
      if (client.$isNew) {
        client.client_number = await this.makeClientNumber()
        //console.log('new client: ', client.client_number)
      }

      if (!client.password) {
        client.password = randomInteger(10000, 999999)
      }

      try {
        const httpCtx = HttpContext.get()
        const locale = httpCtx?.request.headers()['x-locale'] || undefined
        const isRumisota = locale && locale != 'ru'

        i18next.changeLanguage(locale || 'ru')

        let subject = !isRumisota ? 'Учетные данные' : i18next.t('Учетные данные')
        let htmlViewName = !isRumisota ? 'mail_logindata' : 'mail_logindata_' + locale

        let mailer = !isRumisota ? 'smtp' : 'rumi'

        Mail.use(mailer).send((message) => {
          message
            .from(!isRumisota ? EMAIL_FROM : Env.get('RUMI_EMAIL_FROM') || '<EMAIL>')
            .to(client.client_mail)
            .subject(subject)
            .htmlView(htmlViewName, { client })
        })
      } catch (error) {
        console.error('Client beforeSaveHook: ', error)
      }
    }
    if (client.$dirty.password) {
      client.password = await Hash.make(String(client.password).replace(/\s*/gm, ''))
    }
  }

  async getOrdersStat() {
    const { orders_sum, orders_count } = await Database.from('orders')
      .select(Database.raw('sum(order_price) as orders_sum, count(order_id) as orders_count')) //
      .leftJoin('clients', 'clients.client_id', 'orders.order_client')
      .where('order_status', '!=', 'Отменен')
      .andWhere('client_id', this.client_id)
      // //.debug(true)
      .first()

    return { orders_sum, orders_count }
  }

  public static async createOrUpdatePassport(client: Client, data: PassportData) {
    Passport.updateOrCreate(
      {
        client_id: client.client_id
      },
      {
        client_id: client.client_id,
        body: data
      }
    )
  }

  public static async makeClientNumber() {
    let n = randomInteger(10000, 99999)

    const checkExist = await this.findBy('client_number', n)

    if (checkExist) {
      return (n = await this.makeClientNumber())
    } else {
      return n
    }
  }

  public static async getClient(auth, orderClient: Client) {
    //console.log('orderClient', orderClient)

    const httpCtx = HttpContext.get()
    const locale = httpCtx?.request.headers()['x-locale'] || undefined
    const isRumisota = locale && locale != 'ru'

    console.log('getClient httpCtx locale', locale)

    i18next.changeLanguage(locale || 'ru')

    let client

    async function findOrCreate() {
      orderClient.password = randomInteger(10000, 99999)

      try {
        let _client: Client = await Client.firstOrCreate({ client_mail: orderClient.client_mail }, orderClient)

        if (orderClient.org?.org_name) {
          orderClient.org.org_client = _client.client_number
          const org = await Org.firstOrCreate({ org_client: _client.client_number }, orderClient.org)

          _client.client_company = org
          _client.org = org
          orderClient.client_company = org
        }

        if (_client.$isNew || !_client.password) {
          if (!_client.password) {
            _client.password = randomInteger(10000, 99999)
          }

          await _client.save()
          delete _client.password

          try {
            let subject = !isRumisota ? 'Учетные данные' : i18next.t('Учетные данные')
            let htmlViewName = !isRumisota ? 'mail_logindata' : 'mail_logindata_' + locale

            let mailer = !isRumisota ? 'smtp' : 'rumi'
            console.log('client model mailer', mailer)

            if (_client.client_mail) {
              Mail.use(mailer).send((message) => {
                message
                  .from(!isRumisota ? EMAIL_FROM : Env.get('RUMI_EMAIL_FROM', '<EMAIL>'))
                  .to(_client.client_mail)
                  .subject(subject)
                  .htmlView(htmlViewName, { client: _client })
              })
            }
          } catch (error) {
            console.error('Error: Cart::client.$isNew: send account data: ', error)
          }
        }

        if (!_client.$isNew) {
          orderClient.client_number = _client.client_number
          orderClient.client_id = _client.client_id
        }

        client = _client.$isNew ? _client : orderClient
      } catch (error) {
        console.error('ERROR: Client.ts > getClient: ' + error)
        client = orderClient
      }
    }

    /*     try {
      client = await auth.authenticate()
      if (!client) await findOrCreate()
    } catch (err) {
      await findOrCreate()
    } */

    await findOrCreate()
    return client
  }

  public static legacyMerge({ client, address }) {
    let _client = {
      client_name: client.name,
      client_mail: client.email,
      client_phone: client.phone,
      client_city: address.location,
      client_country: address.country,
      client_street: address.street,
      client_house: address.house,
      client_flat: address.flat,
      client_postindex: address.index,
      client_cdekid: client.cdekId,
    }

    if (client.org?.name) {
      _client.org = {
        //org_client: 0,
        org_name: client.org.name,
        org_adress: client.org.address,
        org_inn: client.org.inn,
        org_kpp: client.org.kpp,
        org_rschet: client.org.rschet,
        org_kschet: client.org.kschet,
        org_bik: client.org.bik,
        org_bank: client.org.bank,
        org_vat: client.org.vat
      }
    }

    return _client
  }
}
