import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class EmailLog extends BaseModel {
  public static table = 'email_logs'

  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public campaignId?: string

  @column()
  public recipientEmail: string

  @column()
  public recipientNote?: string

  @column()
  public subject: string

  @column()
  public messagePreview?: string

  @column()
  public status: 'pending' | 'sent' | 'failed' | 'retry'

  @column()
  public errorMessage?: string

  @column()
  public attempts: number

  @column.dateTime()
  public lastAttemptAt?: DateTime

  @column.dateTime()
  public sentAt?: DateTime

  @column()
  public batchNumber?: number

  @column()
  public checkpointIndex?: number

  @column()
  public userId?: number

  @column()
  public userName?: string

  /**
   * Создает запись лога для email рассылки
   */
  public static async createLog(data: {
    campaignId?: string
    recipientEmail: string
    recipientNote?: string
    subject: string
    messagePreview?: string
    status?: 'pending' | 'sent' | 'failed' | 'retry'
    batchNumber?: number
    checkpointIndex?: number
    userId?: number
    userName?: string
  }) {
    return await this.create({
      ...data,
      status: data.status || 'pending',
      attempts: 0
    })
  }

  /**
   * Обновляет статус отправки
   */
  public async updateStatus(
    status: 'pending' | 'sent' | 'failed' | 'retry',
    errorMessage?: string
  ) {
    this.status = status
    this.attempts += 1
    this.lastAttemptAt = DateTime.now()
    
    if (status === 'sent') {
      this.sentAt = DateTime.now()
    }
    
    if (errorMessage) {
      this.errorMessage = errorMessage
    }
    
    await this.save()
  }

  /**
   * Получает статистику по кампании
   */
  public static async getCampaignStats(campaignId: string) {
    const stats = await this.query()
      .where('campaign_id', campaignId)
      .groupBy('status')
      .count('* as total')
      .select('status')

    const result = {
      pending: 0,
      sent: 0,
      failed: 0,
      retry: 0,
      total: 0
    }

    stats.forEach(stat => {
      result[stat.status] = parseInt(stat.$extras.total)
      result.total += parseInt(stat.$extras.total)
    })

    return result
  }

  /**
   * Получает последний checkpoint для кампании
   */
  public static async getLastCheckpoint(campaignId: string) {
    const lastLog = await this.query()
      .where('campaign_id', campaignId)
      .whereNotNull('checkpoint_index')
      .orderBy('checkpoint_index', 'desc')
      .first()

    return lastLog?.checkpointIndex || 0
  }

  /**
   * Получает неудачные отправки для повторной попытки
   */
  public static async getFailedEmails(campaignId?: string, maxAttempts = 5) {
    const query = this.query()
      .whereIn('status', ['failed', 'retry'])
      .where('attempts', '<', maxAttempts)

    if (campaignId) {
      query.where('campaign_id', campaignId)
    }

    return await query.orderBy('last_attempt_at', 'asc')
  }

  /**
   * Очищает старые логи (старше указанного количества дней)
   */
  public static async cleanupOldLogs(daysOld = 90) {
    const cutoffDate = DateTime.now().minus({ days: daysOld })
    
    return await this.query()
      .where('created_at', '<', cutoffDate.toSQL())
      .delete()
  }
}
