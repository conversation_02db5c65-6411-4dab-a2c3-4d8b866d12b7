import { DateTime } from 'luxon'
import { afterSave, BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import Ws from 'App/Services/Ws'
import HttpContext from '@ioc:Adonis/Core/HttpContext'

export default class Journal extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({
    autoCreate: true,
    serialize: (value?: DateTime) => {
      try {
        return value ? value.toFormat('dd.LL.yy TT') : value
      } catch (error) {
        return value
      }
    }
  })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public user_id?: string

  @column()
  public user_name?: string

  @column()
  public entity: string

  @column()
  public entity_id?: string

  @column()
  public msg: string

  @afterSave()
  public static async afterSaveHook(item: Journal) {
    Ws.io.emit('journal', item)
  }

  public static async createItem({ user_id, user_name, entity, entity_id, msg }: any) {
    try {
      const httpCtx = HttpContext.get()
      let user

      if (!user_id) {
        try {
          user = await httpCtx?.auth.use('api').authenticate()
        } catch (error) {
          if (!user) {
            try {
              user = await httpCtx?.auth.use('web').authenticate()
            } catch (error) {}
          }
        }
      }

      return await this.create({
        entity,
        entity_id,
        msg,
        user_id: user_id || user?.user_id,
        user_name: user_name || user?.user_name
      })
    } catch (error) {
      // console.error(error)
      console.error('Ошибка записи журнала:', error)
    }
  }
}
