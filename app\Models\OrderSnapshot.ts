import { DateTime } from 'luxon'
import { BaseModel, column, afterFetch, afterFind } from '@ioc:Adonis/Lucid/Orm'
import Order from 'App/Models/Order'
import OrderItem from 'App/Models/OrderItem'
import Env from '@ioc:Adonis/Core/Env'
import CartItem from 'App/Models/CartItem'
import Product from 'App/Models/Product'
import { CartSum } from 'App/Interfaces/cart/CartSum'
import dayjs from 'dayjs'

const LEGACY_MODE = Env.get('LEGACY_MODE') || true

const rmFields = [
  'newItem',
  'prod_note',
  'prod_secret',
  'prod_rk',
  'prod_analogs',
  'prod_composition',
  'prod_img',
  'prod_img_rumi',
  'qty',
  'prod_purchasing',
  'prod_minalert',
  'product',
  '$extras'
]

interface Coupons {
  discountVal?: number
  personal?: number
}

interface SnapshotInterface {
  orderId: number
  snapItems: Array<CartItem>
  userName?: string | null
  clientData?: {} | null
  coupons?: Coupons | null
  cartsum?: CartSum
}

export default class OrderSnapshot extends BaseModel {
  public static table = 'orders_snapshots'

  @column({ isPrimary: true })
  public ID: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column.dateTime({ autoCreate: true })
  public date?: DateTime

  @column()
  public orderid: number

  @column()
  public body: object

  @column()
  public user: string

  public static parseBody(snapshot) {
    try {
      snapshot.body = JSON.parse(snapshot.body)

      try {
        snapshot.body.order_coupons = JSON.parse(snapshot.body.order_coupons)
      } catch (error) {}
    } catch (error) {
      // console.error('OrderSnapshot.ts > afterFetchHook: ' + error)
    }

    try {
      // snapshot.body.order_datetime = DateTime.fromISO(snapshot.body.order_datetime).toLocaleString()
      // snapshot.body.order_datetime = dayjs(snapshot.body.order_datetime).format('DD.MM.YYYY hh:mm')
    } catch (error) {}
  }

  @afterFetch()
  public static afterFetchHook(snapshots: OrderSnapshot[]) {
    // console.log('snapshots afterFetchHook hook')
    if (snapshots.length) {
      snapshots.map(this.parseBody)
    }
  }

  @afterFind()
  public static afterFindHook(snapshot: OrderSnapshot) {
    //console.log('afterFindHook hook');
    if (snapshot) {
      this.parseBody(snapshot)
    }
  }
  //orderId, userName?, clientData?

  public static async makeSnap() {}

  public static async makeInitSnap({ orderId, userName = null, clientData = null, snapItems, coupons, cartsum }: SnapshotInterface) {
    const transformItems = (items) => {
      return items.map((item: CartItem) => {
        let product: any = item.product.toJSON()
        let _item = item.toJSON()

        if (LEGACY_MODE) {
          product['qty'] = item.qty
          product['_qty'] = item.qty
          product['discount'] = product.prod_discount
          product.prod_discount = Number((product.prod_price - (product.prod_price / 100) * product.prod_discount).toFixed(2))
          product['orderCount'] = item.qty

          // delete product.whosaleprice
        }

        Object.keys(product).map((key) => (_item[key] = product[key]))
        rmFields.map((key) => delete _item[key])

        return _item
      })
    }

    const order = await Order.query()
      .where('order_id', orderId)
      //.preload('items', (itemsQuery) => itemsQuery.preload('product'))
      .if(!clientData, (builderQuery) => {
        builderQuery.preload('client', (clientQuery) => {
          clientQuery.preload('org')
        })
      })
      .first()

    if (!order) {
      throw new Error(`makeInitSnap: order ${orderId} not found`)
    }

    let orderItems = transformItems(snapItems /* order.items */)
    let snapshot = new OrderSnapshot()
    let _snapBody: any = order.toJSON()

    _snapBody.orderDate = order.order_datetime.toFormat('DD.MM.YYYY')
    _snapBody.orderDateTime = order.order_datetime //.toFormat('DD.MM.YYYY hh:mm')

    _snapBody.items = orderItems

    if (coupons) {
      _snapBody.order_coupons = {
        discountVal: coupons.discountVal,
        personal: coupons.personal
      }
    }

    if (cartsum) {
      _snapBody.defsum = cartsum.defsum
      _snapBody.whosalePrices = cartsum.whosalePrices
    }

    if (clientData) {
      _snapBody.client = clientData

      try {
        if (clientData.org) {
          clientData.client_company = clientData.org
        }
      } catch (error) {
        console.log('🚀 ~ file: OrderSnapshot.ts:169 ~ OrderSnapshot ~ makeInitSnap clientData.client_company = clientData.org ~ error:', error)
      }
    }

    snapshot.body = JSON.stringify(_snapBody)
    snapshot.orderid = orderId
    snapshot.user = userName || 'NA'

    try {
      await snapshot.save()
    } catch (error) {
      throw new Error('OrderSnapshot > makeSnapshot > writeSnapshot: ' + error)
    }
  }
}
