import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class Page extends BaseModel {
  @column({ isPrimary: true })
  public page_id: number

  /*   @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime */

  @column()
  public page_title: string

  @column()
  public page_key: string

  @column()
  public page_body: string

  @column()
  public page_locale: string

  @column()
  public print: string

  public static async pageByKey(page_key: string, page_locale = 'ru', print = false) {
    if (!page_key) {
      return false
    }

    const page = await this.query().where({ page_key, page_locale, print }).first()

    return page
  }
}

