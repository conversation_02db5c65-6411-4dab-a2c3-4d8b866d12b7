// model passport {
//   id         Int      @id @default(autoincrement())
//   created_at DateTime @default(now()) @db.Timestamp(0)
//   updated_at DateTime @default(dbgenerated("('0000-00-00 00:00:00')")) @db.Timestamp(0)
//   client_id  Int
//   body       Json
// }

import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo, afterFind, beforeSave } from '@ioc:Adonis/Lucid/Orm'
import Client from 'App/Models/Client'
import { afterFetch } from '@ioc:Adonis/Lucid/Orm'

export default class Passport extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public client_id: number

  @column()
  public body: JSON

  @belongsTo(() => Client, {
    foreignKey: 'client_id',
    localKey: 'id'
  })
  public client: BelongsTo<typeof Client>

  @afterFind()
  @afterFetch()
  public static afterFetch(passport: Passport) {
    if (typeof passport.body === 'string') {
      passport.body = JSON.parse(passport.body)
    }
  }

  @beforeSave()
  public static beforeSave(passport: Passport) {
    if (typeof passport.body === 'object') {
      passport.body = JSON.stringify(passport.body)
    }
  }
}
