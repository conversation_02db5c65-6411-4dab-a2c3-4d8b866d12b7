//import { DateTime } from 'luxon'
import {
  BaseModel,
  column,
  afterFetch,
  hasOne,
  HasOne,
  afterFind,
  beforeSave,
  beforeUpdate,
  afterSave,
  computed,
  scope,
  ModelQueryBuilderContract,
  hasMany,
  HasMany,
  afterDelete,
  beforeCreate
} from '@ioc:Adonis/Lucid/Orm'
import Mail from '@ioc:Adonis/Addons/Mail'

import Category from 'App/Models/Category'
import Lock from 'App/Models/Lock'
import ProductSchema from 'App/Models/ProductSchema'
import { parseQS } from 'App/Helpers/parseQS'
import Env from '@ioc:Adonis/Core/Env'
import Database, { TransactionClientContract } from '@ioc:Adonis/Lucid/Database'
import { splitProductSize } from 'App/Helpers/updateProductSizes'
import { parseExtandedQueries } from 'App/Helpers/parseExtandedQueries'
import { isSize } from 'App/Helpers/isSize'
import { replaceFirstChar } from 'App/Helpers/replaceFirstChar'
import Statistic from 'App/Models/Statistic'
import Journal from 'App/Models/Journal'
import Order from 'App/Models/Order'
import OrderItem from 'App/Models/OrderItem'
import HttpContext from '@ioc:Adonis/Core/HttpContext'
import { SnapshotBodyItemInterface } from 'App/Interfaces/orders/snapshotBodyItem'
import ProductSnapshot from 'App/Models/ProductSnapshot'
import generateImageWithTexts, { checkFileExists } from 'App/Plugins/PicGenerator'

import * as _path from 'path'
import LangDict from './LangDict'
import { DateTime } from 'luxon'
import { generateVideo } from 'App/Plugins/VideoGenerator'
import { meiliDB } from 'App/Plugins/MeiliSearch'

const UPLOAD_PATH = _path.resolve(Env.get('UPLOAD_PATH'))

// const CryptoJS = require('crypto-js')

const rmFields = ['qty', 'whosaleprice']

interface ProductSales {
  prod_id: string
  prod_manuf: string
  prod_analogsku: string
  sales: number
}

const cartProductDBfields: Array<string> = [
  'products.prod_id',
  'products.prod_sku',
  'products.prod_analogsku',
  'products.prod_cat',
  'products.prod_weight',
  'products.prod_price',
  'products.prod_count',
  'products.prod_manuf',
  'products.prod_note',
  'products.prod_year',
  'products.prod_type',
  'products.prod_uses',
  'products.prod_size',
  'products.prod_discount',
  'products.prod_purpose',
  'products.prod_analogs',
  'products.prod_material',
  'products.prod_rk',
  'products.prod_group',
  'products.prod_group_count',
  'products.prod_img',
  'products.prod_img_rumi',
  // 'products.prod_images',
  'products.prod_model',
  'products.prod_buy_limit'
]

const clientProductDBfields: Array<string> = [
  'products.prod_id',
  'products.prod_sku',
  'products.prod_analogsku',
  'products.prod_cat',
  'products.prod_weight',
  'products.prod_price',
  'products.prod_count',
  'products.prod_manuf',
  'products.prod_note',
  'products.prod_year',
  'products.prod_type',
  'products.prod_uses',
  'products.prod_size',
  'products.prod_discount',
  'products.prod_purpose',
  'products.prod_analogs',
  'products.prod_material',
  'products.prod_rk',
  'products.prod_group',
  'products.prod_group_count',
  'products.prod_img',
  'products.prod_img_rumi',
  'products.prod_images',
  'products.prod_model',
  'products.prod_buy_limit'
]

const cartListProductDBfields: Array<string> = [
  'products.prod_sku',
  'products.prod_analogsku',
  'products.prod_cat',
  'products.prod_price',
  'products.prod_count',
  'products.prod_manuf',
  'products.prod_type',
  'products.prod_size',
  'products.prod_discount',
  'products.prod_purpose',
  'products.prod_buy_limit'
]

const productDBfields: Array<string> = [
  'products.prod_images',
  'products.prod_sku',
  'products.prod_analogsku',
  'products.prod_cat',
  'products.prod_weight',
  'products.prod_price',
  'products.prod_count',
  'products.prod_manuf',
  'products.prod_note',
  'products.prod_year',
  'products.prod_type',
  'products.prod_uses',
  'products.prod_size',
  'products.prod_discount',
  'products.prod_purpose',
  'products.prod_analogs',
  'products.prod_material',
  'products.prod_rk',
  'products.prod_group',
  'products.prod_group_count',
  'products.prod_img',
  'products.prod_img_rumi',
  'products.prod_model',
  'products.prod_buy_limit'
]

const columnsDBfields: Array<string> = ['keyname', 'title', 'sort', 'sorted', 'slot', 'cat_id']

const extandedSearchFields: Array<string> = [
  'products.prod_sku',
  'products.prod_analogsku',
  'products.prod_count',
  //'products.prod_manuf',
  'products.prod_note',
  'products.prod_year',
  'products.prod_type',
  'products.prod_uses',
  'products.prod_purpose',
  'products.prod_analogs',
  'products.prod_group',
  'products.prod_model'
  // 'products.prod_secret'
]

const encryptColumns = [
  'prod_sku',
  'prod_analogsku',
  //'prod_weight',
  //'prod_manuf',
  'prod_note',
  //'prod_year',
  //'prod_type',
  //'prod_uses',
  'prod_size',
  //'prod_purpose',
  'prod_analogs',
  //'prod_material',
  'prod_rk',
  'prod_img',
  'prod_img_rumi'
  //'prod_model'
]

const skuFields = [
  'prod_sku',
  'prod_analogsku',
  // 'prod_secret',
  'prod_analogs'
  //'prod_note'
]

export default class Product extends BaseModel {
  @column({ isPrimary: true })
  public prod_id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column()
  public id?: string

  @column({serializeAs: 'isVirtual'})
  public isVirtual?: boolean

  @column()
  public prod_sku: string

  @column()
  public prod_analogsku: string

  @column()
  public prod_cat: number

  @column()
  public prod_morecats: string

  @column()
  public prod_price: number

  @column()
  public prod_count: number

  @column()
  public prod_manuf: string

  @column()
  public prod_note: string

  @column()
  public prod_year: string

  @column()
  public prod_model: string

  @column()
  public prod_type: string

  @column()
  public prod_composition: string

  @column()
  public prod_cell: string

  @column()
  public prod_supplier: string

  @column()
  public prod_uses: string

  @column()
  public prod_size: string

  @column()
  public prod_discount: number

  @column()
  public prod_purchasing: string

  @column()
  public prod_purpose: string

  @column()
  public prod_analogs: string

  @column()
  public prod_material: string

  @column()
  public prod_weight: string

  @column()
  public prod_group: string

  @column()
  public prod_group_count: number

  @column()
  public prod_gtd_alert: string

  @column()
  public prod_group_gtd_qty: string

  @column()
  public prod_gtd_qty: string

  @column()
  public prod_rk: string

  @column()
  public prod_minalert: number

  @column()
  public prod_img: string

  @column()
  public prod_img_rumi: string

  @column()
  public prod_images: string

  @column()
  public prod_secret: string

  @column()
  public prod_coeff: string

  // @column()
  // public prod_cproductl: string

  @column()
  public cat_title?: string

  @column()
  public qty?: number

  @column()
  public whosaleprice?: number

  @column()
  public size_in?: number

  @column()
  public size_in_2?: number

  @column()
  public size_out?: number

  @column()
  public size_out_2?: number

  @column()
  public size_h?: number

  @column()
  public size_h_2?: number

  @column()
  public prod_buy_limit?: number

  @column()
  public _sku?: string

  // @computed()
  // public get _sku() {
  //   return 'test'
  // }

  @computed()
  public get buy_limit() {
    if (
      this.prod_buy_limit &&
      Number(this.prod_buy_limit) > 0 &&
      (Number(this.prod_count) > Number(this.prod_buy_limit) || Number(this.prod_group_count) > Number(this.prod_buy_limit))
    ) {
      return this.prod_buy_limit
    } else {
      return 0
    }
  }

  @computed()
  public get images() {
    if (this.prod_images) {
      return String(this.prod_images)
        .split(',')
        .filter((i) => i)
        .map((i) => [
          {
            path: '/rti/' + i,
            name: i,
            type: 'rti'
          },
          {
            path: '/rumi/' + i,
            name: i,
            type: 'rumi'
          }
        ])
        .flat()
    }
  }

  /*   @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime */

  /*   public static _orderBySize = (_ascdesc) => `  cast( SUBSTRING_INDEX(products.prod_size, "*", 1) as DOUBLE ) ${_ascdesc},
                            cast(SUBSTRING_INDEX( SUBSTRING_INDEX(products.prod_size, "*", -2), "*", 1) as DOUBLE ) ${_ascdesc},
                            cast(SUBSTRING_INDEX(SUBSTRING_INDEX( SUBSTRING_INDEX(products.prod_size, "*", -2), "*", -1), "/", 1) as DOUBLE ) ${_ascdesc},
                            cast(SUBSTRING_INDEX(SUBSTRING_INDEX( SUBSTRING_INDEX(products.prod_size, "*", -2), "*", -1), "/", -1) as DOUBLE ) ${_ascdesc}` */

  public static _orderBySize = (_ascdesc) => ` size_in ${_ascdesc}, size_in_2 ${_ascdesc}, size_out ${_ascdesc}, size_out_2 ${_ascdesc}, size_h ${_ascdesc}, size_h_2 ${_ascdesc}`

  @hasOne(() => Category, {
    localKey: 'prod_cat',
    foreignKey: 'cat_id'
  })
  public category: HasOne<typeof Category>

  @hasOne(() => ProductSchema, {
    foreignKey: 'product_id'
    //localKey: 'prod_id',
    // onQuery(query) {
    //   query.orWhereRaw("FIND_IN_SET(`product_id`, related_ids) > 0")
    // }
  })
  public schema: HasOne<typeof ProductSchema>

  @hasMany(() => Statistic, {
    foreignKey: 'query',
    localKey: 'prod_analogsku'
  })
  public statlist: HasMany<typeof Statistic>

  // @hasMany(() => OrderItem, {
  //   foreignKey: 'item_id',
  //   localKey: 'prod_id',
  //   onQuery(query) {
  //     query.preload('order', (orderQuery) => orderQuery.where('order_status', '!=', 'Отменен'))
  //   }
  // })
  // public orders: HasMany<typeof OrderItem>

  @hasOne(() => Lock, {
    localKey: 'prod_id',
    foreignKey: 'entity_id',
    onQuery(query) {
      query.select('active', 'created_at', 'user_name').where({ entity: 'products', active: true })
    }
  })
  public lock: HasOne<typeof Lock>

  @hasMany(() => ProductSnapshot, {
    foreignKey: 'prod_id',
    localKey: 'prod_id',
    onQuery: (query) => query.orderBy('id', 'desc')
  })
  public snapshots: HasMany<typeof ProductSnapshot>

  public async isLock() {
    return await Lock.isLock({
      entity: 'products',
      entity_id: this.prod_id
    })
  }

  public static async getProductSalesByOEM({ oem, brand }) {
    const res = await Database.from('order_items')
      .select('products.prod_analogsku')
      .sum('item_count', 'sales')
      .leftJoin('products', 'order_items.item_id', '=', 'products.prod_id')
      .leftJoin('orders', 'order_items.items_order_id', '=', 'orders.order_id')
      .where('orders.order_status', '!=', 'Отменен')
      .andWhere('prod_analogsku', oem)
      .if(brand, (query) => query.andWhere('prod_manuf', brand))
      .groupBy('prod_analogsku')
      .orderBy('sales', 'desc')
      .first()
    return res || 0
  }

  public static async getProductSales({ toObject = false, test = false, dates } = {}) {
    ;`SELECT sum(item_count),
    products.prod_analogsku,
    cats.cat_title
    FROM order_items
    LEFT JOIN orders ON orders.order_id = order_items.items_order_id
    LEFT JOIN products on products.prod_id = order_items.item_id
    LEFT JOIN cats on cats.cat_id = products.prod_cat
    WHERE orders.order_status != 'Отменен'
    GROUP BY prod_analogsku ORDER BY sum(item_count) DESC`

    const res: ProductSales[] = await Database.from('order_items')
      .select('products.prod_analogsku', 'products.prod_id', 'products.prod_manuf')
      .sum('item_count', 'sales')
      .leftJoin('products', 'order_items.item_id', '=', 'products.prod_id')
      .leftJoin('orders', 'order_items.items_order_id', '=', 'orders.order_id')
      //.leftJoin('cats', 'products.prod_cat', '=', 'cats.cat_id')
      .where('orders.order_status', '!=', 'Отменен')
      // .if(test, builder => builder.limit(10))
      .if(dates, (query) => {
        query.where((subquery) => subquery.whereBetween('orders.order_datetime', dates))
      })
      .debug(!!test)
      .groupBy('prod_analogsku')
      .orderBy('sales', 'desc')

    if (toObject) {
      const data = {}
      res.map((item) => (data[String(item.prod_analogsku) + String(item.prod_manuf)] = item.sales))

      return data
    }

    return res || 0
  }

  public static getProductColumns() {
    return {
      productDBfields,
      columnsDBfields,
      clientProductDBfields,
      extandedSearchFields,
      cartProductDBfields,
      encryptColumns,
      skuFields
    }
  }

  public static orderByStock() {
    const orderByStockField = 'stockgroup'
    const orderByStockQuery = 'IF(prod_count > 0, 1, 0) as ' + orderByStockField

    return { orderByStockField, orderByStockQuery }
  }

  public static orderByFullMatch() {
    const orderByFullMatchQuery = `IF(?? LIKE ?, 10, 0) DESC`
    return { orderByFullMatchQuery }
  }

  public static orderByLength(value) {
    const orderByLengthField = 'occurences'
    const orderByLengthQuery = `(LENGTH(prod_sku) - LENGTH(REPLACE(prod_sku, '${value}', ''))) / LENGTH('${value}') + (LENGTH(prod_analogsku) - LENGTH(REPLACE(prod_analogsku, '${value}', ''))) / LENGTH('${value}') ${orderByLengthField}`

    return { orderByLengthField, orderByLengthQuery }
  }

  public static getParams(request, categoryId?) {
    let RK_CATS: string[] = Env.get('RK_CATS') || []
    let RK_SORT_FIELD = Env.get('RK_SORT_FIELD') || 'prod_uses'

    //console.log('getParams categoryId: ', categoryId);

    try {
      RK_CATS = String(RK_CATS).split(',')
    } catch (error) {}

    let parsedQS: any = parseQS(request.requestQs.qs)

    const defLimit = 100
    const _ascdesc = (parsedQS && parsedQS.ad) || 'ASC'

    const limit = (parsedQS && parsedQS.limit) || defLimit

    let orderRaw: string
    let filters: object = {}

    if (parsedQS && parsedQS.filters) {
      try {
        filters = JSON.parse(parsedQS.filters)
        Object.keys(filters).map((key) => {
          if (Array.isArray(filters[key]) && !filters[key].length) {
            delete filters[key]
          }
        })
      } catch (error) {}
    }

    if (parsedQS && parsedQS.key) {
      if (parsedQS.key === 'prod_size') orderRaw = this._orderBySize(_ascdesc)
      else orderRaw = `${parsedQS.key} ${parsedQS.ad}`
    } else {
      if (RK_CATS.indexOf(String(categoryId)) > -1) {
        orderRaw = `${RK_SORT_FIELD} asc` //`SUBSTRING(${RK_SORT_FIELD}, 1, 3) ${parsedQS.ad}`
      } else {
        orderRaw = this._orderBySize(_ascdesc)
      }
    }

    //console.log('orderRaw', orderRaw)
    //delete filters['size']
    return {
      productDBfields,
      columnsDBfields,
      _ascdesc,
      _orderBySize: this._orderBySize(_ascdesc),
      orderRaw,
      filters,
      clientProductDBfields,
      cartListProductDBfields,
      extandedSearchFields,
      cartProductDBfields,
      limit
    }
  }

  static async getProductsWeight(items: { qty: number; product: Product }[]) {
    const getw = (item) => {
      if (item.product?.prod_weight) {
        let w = Number(String(item.product.prod_weight).replace(',', '.'))
        return (!isNaN(w) ? w : 30) * item.qty
      } else {
        return 30 * item.qty
      }
    }

    let weight = 0

    await Promise.all(
      items.map(async (item) => {
        if (item.product?.prod_rk) {
          let rk = await item.product.getRkСomposition()
          rk.rkproducts.map((item) => {
            weight += getw(item)
          })
        } else {
          weight += getw(item)
        }
      })
    )

    return Math.ceil(weight)
  }

  async getRkСomposition() {
    const rklist = this.prod_rk.split(',').filter((i) => i)
    const prodsIds = rklist.map((i) => Number(i.split('*')[0])).filter((i) => i)
    const rkproducts = await Product.query().whereIn('prod_id', prodsIds)

    let rk = rklist.map((i) => {
      let _item = i.split('*')
      let product = rkproducts.find((x) => String(x.prod_id) == String(_item[0]))

      return {
        id: Number(_item[0]),
        qty: Number(_item[1]),
        product
      }
    })

    return {
      rklist,
      prodsIds,
      rkproducts,
      rk
    }
  }

  async getRkStock() {
    const { rkproducts, rk } = await this.getRkСomposition()

    let stocklist = rkproducts.map((rkItem) => {
      let lst = rk.find((x) => x.id == rkItem.prod_id)
      if (lst) {
        return Math.floor(rkItem.prod_count / lst.qty)
      }
    })

    if (stocklist && stocklist.length) {
      this.prod_count = Math.min(...stocklist)
    } else {
      this.prod_count = 0
    }
  }

  public static rumisotaProductTransform(products: Product[]) {
    // const httpCtx = HttpContext.get()
    // const locale = httpCtx?.request.headers()['x-locale'] || undefined
    // const isRumisota = locale && locale != 'ru'

    products.forEach(async (product) => {
      if (product.$attributes) {
        product.$attributes['_sku'] = product.prod_sku
      }

      try {
        if (product.schema?.body) {
          product.schema.body.img = product.schema.body.img_rumi
        }
      } catch (error) {}

      // product._sku = 'test'//product.prod_sku
      const sku = product.prod_sku
      const oem = product.prod_analogsku
      product.prod_sku = product.prod_id
      product.prod_analogsku = sku
      product.sku = sku
      // product.sku = sku

      product.prod_analogs = oem + ',' + product.prod_analogs
    })
  }

  public static async productInit(products: Product[]) {
    // const encryptCatalogs = Env.get('ENCRYPT_CATALOGS') === 'true' || false

    // const httpCtx = HttpContext.get()
    // const locale = httpCtx?.request.headers()['x-locale'] || undefined
    // const isRumisota = locale && locale != 'ru'

    return await Promise.all(
      products
        .filter((i) => i)
        .map(async (product) => {
          if (!product.qty) {
            product.qty = 0
          }

          product.whosaleprice = Number((product.prod_price - (product.prod_price / 100) * product.prod_discount).toFixed(2))
          product.prod_group ? (product.prod_count = product.prod_group_count) : ''

          if (product.prod_rk) {
            try {
              await product.getRkStock()
            } catch (error) {
              product.prod_count = 0
              console.error('getRkStock: ', error)
            }
          }
        })
    )
  }

  // public static async stockUpdate(products: Product[]) {

  // }

  _stock(qty, trx?: TransactionClientContract) {
    const product = this //await Product.find(item.product.prod_id)

    //!! disable transaction
    // product.useTransaction(trx)

    const process = async (increment?) => {
      if (product.prod_group) {
        await Product.query({
          //!! disable transaction
          // client: trx
        })
          .where('prod_group', product.prod_group)
          .andWhereNot('prod_id', product.prod_id)
          .update({
            prod_group_count: product.prod_count,
            prod_count: product.prod_count
          })
      }

      if (product.prod_rk) {
        const { rkproducts, rk } = await this.getRkСomposition()
        await Promise.all(
          rkproducts.map(async (rkItem) => {
            let lst = rk.find((x) => x.id == rkItem.prod_id)
            if (lst) {
              if (increment) {
                await rkItem._stock(qty * lst.qty, trx).increment()
              } else {
                await rkItem._stock(qty * lst.qty, trx).decrement()
                rkItem.checkMinStock()
              }

              Journal.createItem({
                entity: 'product',
                entity_id: lst.product?.prod_id,
                msg: `Списание по РК #${product.prod_id}, Кол-во: ${qty * lst.qty}, Наличие: ${lst.product?.prod_count}`
              })
            }
          })
        )
      }

      await product.save()
      // await trx?.commit()
      product.checkMinStock()
    }

    return {
      decrement: async () => {
        product.prod_count = product.prod_count -= qty
        await process()
      },
      increment: async () => {
        product.prod_count = product.prod_count += qty
        await process('increment')
      }
    }

    // (10) 1-01x2 | 2-02x1 -> (10-2) = 8 -> update grp (8) ->  НА МОМЕНТ ВТОРОй ИТЕРАЦИИ
  }

  async checkMinStock() {
    try {
      if (!Number.isNaN(this.prod_minalert) && Number(this.prod_minalert) > 0 && this.prod_count <= this.prod_minalert) {
        Mail.use('smtp').send((message) => {
          message
            .from(Env.get('EMAIL_FROM'))
            .to(Env.get('EMAIL_FROM'))
            .subject(`Товар заканчивается: ${this.prod_analogsku} (${this.prod_sku}), остаток ${this.prod_count}`)
            .html(`${this.prod_analogsku} (${this.prod_sku}):  осталось <b>${this.prod_count}</b>, мин.кол-во: ${this.prod_minalert}`)
        })
      }
    } catch (error) {
      console.error(error)
    }
  }

  static async updateOrderItems(newOrderItemsState: SnapshotBodyItemInterface[], prevOrderItemsState: OrderItem[], order_id: number) {
    interface OperatedItem {
      type: 'new' | 'delete' | 'update'
      difference?: number
      clearDifference?: number
      value: number
      operation: 'increment' | 'decrement'
      product: Product
      orderItem?: OrderItem
    }

    interface StockUpdateError extends OperatedItem {
      currentStock: number
      message?: string
    }

    const stockUpdateErrors: StockUpdateError[] = []
    const operatedItems: OperatedItem[] = []

    // проверка наличия перед списанием decrement, если не хватает возвращаем ошибку

    await Promise.all(
      newOrderItemsState.map(async (currentItem) => {
        const existItem = prevOrderItemsState.find((x) => x.item_id == currentItem.prod_id)

        if (!existItem) {
          // add new to orderItems
          let currentItemState = await Database.from('products')
            .where('prod_id', currentItem.prod_id || currentItem.item_id)
            .first() //await Product.findOrFail(currentItem.prod_id || currentItem.item_id)
          // let currentItemState = _currentItemState //.toJSON() as Product
          operatedItems.push({
            type: 'new',
            operation: 'decrement',
            value: (currentItem.orderCount || currentItem.item_count) as number,
            product: currentItemState,
            orderItem: {
              item_count: (currentItem.orderCount || currentItem.item_count) as number,
              items_order_id: order_id,
              item_id: currentItem.prod_id
            }
          })

          Journal.createItem({
            entity: 'product',
            entity_id: currentItem.prod_id,
            msg: `Добавлен в заказ: #${currentItem.items_order_id}, Кол-во: ${currentItem.orderCount || currentItem.item_count}, Наличие: ${
              currentItemState.prod_count
            }, списание: ${currentItem.orderCount || currentItem.item_count}`
          })
        }
      })
    )

    prevOrderItemsState.map((currentItem) => {
      let existItem = newOrderItemsState.find((x) => x.prod_id == currentItem.item_id)

      if (!existItem) {
        // remove from orderItems
        operatedItems.push({
          type: 'delete',
          operation: 'increment',
          product: currentItem.product,
          value: currentItem.item_count,
          orderItem: currentItem
        })

        Journal.createItem({
          entity: 'product',
          entity_id: currentItem.product?.prod_id,
          msg: `Удален из заказа: #${currentItem.items_order_id}, Кол-во: ${currentItem.item_count}, Наличие: ${currentItem.product?.prod_count}`
        })
      } else {
        if (typeof existItem.orderCount === 'undefined') {
          existItem.orderCount = existItem.item_count
        }

        if (currentItem.item_count != existItem.orderCount) {
          const difference = currentItem.item_count - existItem.orderCount

          currentItem.item_count = existItem.orderCount

          const operatedItem: OperatedItem = {
            operation: Math.sign(difference) === -1 ? 'decrement' : 'increment',
            product: currentItem.product,
            type: 'update',
            difference: Math.abs(difference),
            clearDifference: difference,
            value: Math.abs(difference),
            orderItem: currentItem
          }
          operatedItems.push(operatedItem)

          Journal.createItem({
            entity: 'product',
            entity_id: currentItem.product?.prod_id,
            msg: `Обновлен в заказе: #${currentItem.items_order_id}, Кол-во: ${difference}, Наличие: ${currentItem.product?.prod_count}`
          })
        }
      }
    })

    async function updateProcess(item: OperatedItem) {
      // if (item.product.prod_group) {
      //   await Product.query().where('prod_group', item.product.prod_group).update({
      //     prod_group_count: product.prod_count,
      //     prod_count: product.prod_count
      //   })
      // }

      // !!TODO: Проверка остатков на наличие перед списанием!

      // Изменения остатков
      if (item.product.prod_group) {
        await Product.query(/*{ client: trx }*/)
          .where('prod_group', item.product.prod_group)
          [item.operation]('prod_count', item.value)
          [item.operation]('prod_group_count', item.value)

        // Обновляем товары группы в MeiliSearch
        try {
          const groupProducts = await Product.query().where('prod_group', item.product.prod_group).exec()
          for (const product of groupProducts) {
            await meiliDB.updateProduct({ product, isAdonisModel: true })
          }
        } catch (error) {
          console.error(`Error updating group products in MeiliSearch: ${error}`)
        }
      } else if (item.product.prod_rk) {
        if (item.product.prod_rk) {
          if (item.product instanceof Product === false) {
            console.log(`item.product ${item.product?.prod_sku} NOT instanceof Product`)
            let _productInstance = new Product()
            _productInstance.fill(item.product, true)
            item.product = _productInstance
          }

          const { rkproducts, rk } = await item.product.getRkСomposition()
          await Promise.all(
            rkproducts.map(async (rkItem) => {
              let lst = rk.find((x) => x.id == rkItem.prod_id)
              if (lst) {
                await updateProcess({
                  ...item,
                  product: lst.product,
                  value: item.value * lst.qty
                })

                // console.log('item', item)

                Journal.createItem({
                  entity: 'product',
                  entity_id: lst.product?.prod_id,
                  msg: `Операция по РК #${item.product.prod_id}, Кол-во: ${item.clearDifference}, Наличие: ${lst.product?.prod_count}`
                })
              } else {
                console.error(`Error updating rk (${item.product.prod_sku}) stock`)
              }
            })
          )
        }
      } else {
        // Обновляем количество товара в базе данных
        await Product.query(/*{ client: trx }*/)
          .where('prod_id', item.product.prod_id || item.product.item_id)
          [item.operation]('prod_count', item.value)

        // Получаем обновленный товар из базы данных
        try {
          const updatedProduct = await Product.find(item.product.prod_id || item.product.item_id)
          if (updatedProduct) {
            // Обновляем товар в MeiliSearch
            await meiliDB.updateProduct({ product: updatedProduct, isAdonisModel: true })
          }
        } catch (error) {
          console.error(`Error updating product in MeiliSearch: ${error}`)
        }
      }
    }

    // проверка перед обновлением стока
    await Promise.all(
      operatedItems
        .filter((item) => item.operation === 'decrement')
        .map(async (item) => {
          const currentItemState = await Product.find(item.product.prod_id || item.product.item_id)
          // await Database.from('products').where('prod_id', item.product.prod_id || item.product.item_id).first()

          if (!currentItemState) {
            throw new Error(`Не найден товар для обновления: ${item.product.prod_id}`)
          }

          console.log({
            currentStateStock: currentItemState.prod_count,
            newStock: item.difference ?? item.value
          })

          if (currentItemState.prod_rk) {
            await currentItemState.getRkStock()
          }

          if (currentItemState.prod_count < (item.difference || item.value)) {
            stockUpdateErrors.push({
              ...item,
              currentStock: currentItemState.prod_count
            })
          }
        })
    )

    if (stockUpdateErrors.length) {
      return { stockUpdateErrors }
    }

    // Обновление стока товаров
    await Promise.all(operatedItems.map(updateProcess))

    //Обновление списка товаров заказа | "new"  и "update"

    // !!Запускается с мертвой транзацкией
    // await orderState.related('items').updateOrCreateMany(
    //   operatedItems.filter((item) => item.type == 'update' || item.type == 'new').map((item) => item.orderItem),
    //   'item_id'
    // )

    await Promise.all(
      operatedItems
        .filter((item) => item.type == 'update' || item.type == 'new')
        .map(async (item) => {
          if (item.type == 'update' && item.orderItem) {
            await OrderItem.query(/*{ client: trx }*/)
              .update({
                item_count: item.orderItem.item_count || item.product.orderCount
              })
              .where('ID', item.orderItem.ID)
          }
          if (item.type == 'new') {
            await OrderItem.create(
              {
                ...item.orderItem
              }
              /*{ client: trx }*/
            )
          }
        })
    )

    // await OrderItem.updateOrCreateMany(
    //   'items_order_id',
    //   operatedItems.filter((item) => item.type == 'update' || item.type == 'new').map((item) => item.orderItem)
    // )

    //Обновление списка товаров заказа | "delete"
    await OrderItem.query(/*{ client: trx }*/)
      .whereIn(
        'ID',
        operatedItems.filter((item) => item.type == 'delete').map((item) => item.orderItem?.ID || 0)
      )
      .delete()

    // console.log('operatedItems:', operatedItems)
    return operatedItems
  }

  @afterFetch()
  public static async afterFetchHook(products) {
    await this.productInit(products)
  }

  @afterFind()
  public static async afterFindHook(product) {
    await this.productInit([product])
    if (!product.$attributes['qty']) {
      product.$attributes['qty'] = 0
    }
  }

  @afterDelete()
  public static async afterDeleteHook(product: Product) {
    try {
      // Удаляем товар из MeiliSearch
      await meiliDB.deleteProduct(product.prod_id)
    } catch (error) {
      console.error('Product ~ afterDeleteHook meiliDB.deleteProduct ~ error:', error)
    }

    try {
      // Создаем запись в журнале
      await Journal.createItem({
        entity: 'products',
        entity_id: product.prod_id,
        msg: 'удален товар'
      })
    } catch (error) {
      console.error('Product ~ afterDeleteHook Journal.createItem ~ error:', error)
    }
  }

  async makeSnapshot() {
    try {
      const httpCtx = HttpContext.get()
      const user = await httpCtx?.auth.use('api').authenticate()

      if (user?.user_id) {
        delete this.$extras

        await ProductSnapshot.create({
          body: JSON.stringify(this.toObject()),
          prod_id: this.prod_id,
          user: user?.user_name,
          user_id: user?.user_id
        })
      }
    } catch (error) {}
  }

  @afterSave()
  public static async afterSaveHook(product: Product) {
    //TODO: move to beforSave hook
    if (product.prod_size) {
      try {
        splitProductSize(product, { debug: false })
      } catch (error) {
        console.error('PRODUCT MODEL::afterSave::splitProductSize: ', error)
      }
    }

    try {
      meiliDB.updateProduct({ product, isAdonisModel: true })
    } catch (error) {
      console.log('Product ~ afterSaveHook meiliDB.updateProduct ~ error:', error)
    }

    try {
      product.makeSnapshot()
    } catch (error) {
      console.log('Product ~ afterSaveHook product.makeSnapshot ~ error:', error)
    }

    try {
      Journal.createItem({
        entity: 'products',
        entity_id: product.prod_id,
        msg: `обновлен товар`
      })
    } catch (error) {
      console.log('Product ~ afterSaveHook Journal.createItem ~ error:', error)
    }
  }

  @beforeCreate()
  @beforeSave()
  public static async beforeSaveHook(product: Product) {
    const notnullkeys = ['prod_purchasing']
    const rmwWhitespaceFields = ['prod_sku', 'prod_analogsku', 'prod_size', 'prod_img', 'prod_img_rumi']

    notnullkeys.map((k) => {
      if (!product[k]) {
        product[k] = ''
      }
    })

    rmwWhitespaceFields.map((key) => {
      if (key == 'prod_size' && product.prod_rk) {
      } else {
        product[key] = String(product[key]).replace(/\s/gm, '')
      }
    })

    rmFields.map((key) => {
      delete product[key]
      delete product.$attributes[key]
    })

    product.prod_sku = product.prod_sku?.replace(/[\\/\\\\,.\s\n]/gm, '')
    product.prod_analogsku = product.prod_analogsku?.replace(/[\\/\\\\,.\s\n]/gm, '')

    try {
      if (Array.isArray(product.prod_analogs)) {
        product.prod_analogs = Array.from(new Set(product.prod_analogs)).join(', ')
      }
    } catch (error) {
      console.log(error)
    }

    if (product.prod_analogs && typeof product.prod_analogs != 'string') {
      try {
        product.prod_analogs = product.prod_analogs.join(', ')
      } catch (error) {
        product.prod_analogs = product.prod_analogs.toString()
      }
    }

    product.prod_group ? (product.prod_group_count = product.prod_count) : ''

    const sizesFields = ['size_in', 'size_in_2', 'size_out', 'size_out_2', 'size_h', 'size_h_2']

    sizesFields.map((key) => {
      if (product[key] && product[key] > 0) {
        product[key] = Number(Number(product[key]).toFixed(2))
      }
    })

    // if (!product.prod_img) {
    // try {
    //   await product.generatePhoto({ rti: true })
    // } catch (error) {
    //   console.log('Product ~ beforeSaveHook ~ error:', error)
    // }
    // // }

    // // if (!product.prod_img_rumi) {
    // try {
    //   await product.generatePhoto({ rumi: true })
    // } catch (error) {
    //   console.log('Product ~ beforeSaveHook ~ error:', error)
    // }
    // }
  }

  async generateVideo({ rti = false, rumi = false }: { rti?: boolean; rumi?: boolean }) {
    // if (rti || rumi) {
    //   const isExist = await checkFileExists(`${UPLOAD_PATH}/video/${rti ? 'rti' : 'rumi'}/${this.prod_analogsku}.mp4`)
    // }

    const sourceImages: string[] = []

    if (this.prod_images) {
      sourceImages.push(
        ...this.prod_images
          .split(',')
          .filter((i) => i)
          .slice(0, 2)
          .map((i) => `${UPLOAD_PATH}/${rti ? 'rti' : 'rumi'}/${i}`)
      )
    }

    if (rti) {
      sourceImages.push(`${UPLOAD_PATH}/${rti ? 'rti' : 'rumi'}/${this.prod_img}.jpg`)
    } else {
      sourceImages.push(`${UPLOAD_PATH}/${rti ? 'rti' : 'rumi'}/${this.prod_img_rumi}.jpg`)
    }

    if (!this.prod_images) {
      const typePicPath = `${UPLOAD_PATH}/${rti ? 'rti' : 'rumi'}/${this.prod_type}.jpg`
      if (await checkFileExists(typePicPath)) {
        sourceImages.push(typePicPath)
      }
    }

    if (sourceImages.length < 2) {
      sourceImages.push(sourceImages[0])
    }

    const videoOutputPath = `${UPLOAD_PATH}/video/${rti ? 'rti' : 'rumi'}/${this.prod_analogsku}.mp4`

    await generateVideo(sourceImages, videoOutputPath)
  }

  async generatePhoto({ rti = false, rumi = false }: { rti?: boolean; rumi?: boolean; force?: boolean }) {
    const mergeWithCatPhoto = [this.prod_img, this.prod_img_rumi].some((val) => String(val).trim() === 'кат')
    let productCategory: Category | null
    let categoryImgPath = ''

    if (mergeWithCatPhoto) {
      productCategory = await Category.findOrFail(this.prod_cat)
      categoryImgPath = productCategory.cat_pic.split('data')[1]
    }

    if (rti || rumi) {
      const isExistMainPhoto = mergeWithCatPhoto || (await checkFileExists(`${UPLOAD_PATH}/${rti ? 'rti' : 'rumi'}/${this[rti ? 'prod_img' : 'prod_img_rumi']}.jpg`))
      const isMainPhotoGenerated = this[rti ? 'prod_img' : 'prod_img_rumi']?.includes('g_')

      // console.log('this img:', this.prod_img);
      // console.log("🚀 ~ Product ~ generatePhoto ~ isMainPhotoGenerated:", isMainPhotoGenerated)
      // console.log("🚀 ~ Product ~ generatePhoto ~ isExistMainPhoto:", isExistMainPhoto)

      const genAdnlPhotos = this.images?.find((x) => x.name.includes('g_a_'))
      const isExistAddnlPhoto = false //genAdnlPhotos && (await checkFileExists(`${UPLOAD_PATH}${genAdnlPhotos?.path}`))
      const newFilename = isExistMainPhoto && !mergeWithCatPhoto ? `g_a_${this.prod_sku}.jpg` : `g_${this.prod_sku}.jpg`

      // console.log('🚀 ~ file: Product.ts:1123 ~ Product ~ generatePhoto ~ newFilename:', newFilename)
      if (isExistAddnlPhoto && isExistMainPhoto) {
        return false
      }

      const texts = [
        rti ? { text: this.prod_purpose?.length > 20 ? this.prod_purpose?.split(' ')[0] : this.prod_purpose, fontSize: 36 } : undefined,
        { text: `${!rti ? 'SKU: ' : 'Арт. '} ${this.prod_sku}`, fontSize: 36 },
        { text: `${!rti ? 'Size: ' : 'Размер: '} ${this.prod_size}`, fontSize: 36 },
        { text: `${!rti ? 'Type: ' : 'Тип: '} ${this.prod_type}`, fontSize: 36 }
      ].filter((i) => i)

      const outputFilename = `${UPLOAD_PATH}/${rti ? 'rti' : 'rumi'}/${newFilename}`

      // console.log('🚀 ~ file: Product.ts:1136 ~ Product ~ generatePhoto ~ outputFilename:', outputFilename)
      const footerText = rti ? 'Интернет-магазин\nМИР САЛЬНИКОВ' : 'UAB Rumisota\ne-shop'

      const mergeWithImage =
        (isExistMainPhoto && !isMainPhotoGenerated) || mergeWithCatPhoto
          ? {
              orientation: 'horizontal',
              path: !mergeWithCatPhoto ? `${UPLOAD_PATH}/${rti ? 'rti' : 'rumi'}/${this[rti ? 'prod_img' : 'prod_img_rumi']}.jpg` : `${UPLOAD_PATH}${categoryImgPath}`
            }
          : undefined

      await generateImageWithTexts({
        texts,
        outputFilename,
        frameWidth: 5,
        framePadding: 30,
        frameColor: '#27272a',
        gravity: 'North',
        textColor: '#27272a',
        backgroundColor: 'white',
        height: 450,
        width: 520,
        footer: {
          footerText,
          footerTextSize: '32'
        },
        mergeWithImage
      })

      if (!isExistMainPhoto || mergeWithCatPhoto) {
        this[rti ? 'prod_img' : 'prod_img_rumi'] = newFilename.replace('.jpg', '')

        console.log(`this[rti ? 'prod_img' : 'prod_img_rumi']:::::`, this[rti ? 'prod_img' : 'prod_img_rumi'])
      }

      if (isExistMainPhoto && !isExistAddnlPhoto && !isMainPhotoGenerated && !mergeWithCatPhoto) {
        this.prod_images = [...new Set([...(this.images || []).filter((i) => i).map((i) => i.name), newFilename])].join()
      }

      return newFilename
    }
  }

  async deprecated_generatePhoto({ rti = false, rumi = false }: { rti?: boolean; rumi?: boolean; force?: boolean }) {
    // если фото есть, создаем длинную карточку и добавляем в доп. фото
    // если фото нет, то добавляем маленькую карточку как основное фото
    // если основная фотография сгенерированая, дополнительную не делать.

    ///-----
    const mergeWithCatPhoto = [this.prod_img, this.prod_img_rumi].some((val) => String(val).trim() === 'кат')
    let productCategory: Category | null
    let categoryImgPath = ''

    if (mergeWithCatPhoto) {
      productCategory = await Category.findOrFail(this.prod_cat)
      categoryImgPath = productCategory.cat_pic.split('data')[1]
    }

    ///-----

    if (rti) {
      const isExistMainPhoto = mergeWithCatPhoto || (await checkFileExists(`${UPLOAD_PATH}/rti/${this.prod_img}.jpg`))
      const isMainPhotoGenerated = this.prod_img?.includes('g_')

      const genAdnlPhotos = this.images?.find((x) => x.name.includes('g_a_'))
      const isExistAddnlPhoto = genAdnlPhotos && (await checkFileExists(`${UPLOAD_PATH}${genAdnlPhotos?.path}`))

      if (isExistAddnlPhoto && isExistMainPhoto) {
        return false
      }

      // mergeWithCatPhoto?
      const newFilename = isExistMainPhoto && !mergeWithCatPhoto ? `g_a_${this.prod_sku}.jpg` : `g_${this.prod_sku}.jpg`

      await generateImageWithTexts({
        texts: [
          //prettier
          { text: this.prod_purpose?.length > 20 ? this.prod_purpose?.split(' ')[0] : this.prod_purpose, fontSize: 36 },
          { text: `Арт. ${this.prod_sku}`, fontSize: 36 },
          { text: `Размер: ${this.prod_size}`, fontSize: 36 },
          { text: `Тип: ${this.prod_type}`, fontSize: 36 }
        ],
        outputFilename: `${UPLOAD_PATH}/rti/${newFilename}`,
        frameWidth: 5,
        framePadding: 30,
        frameColor: '#27272a',
        gravity: 'North',
        textColor: '#27272a',
        backgroundColor: 'white',
        height: 450,
        width: 520,
        footer: {
          //rumisota?
          footerText: 'Интернет-магазин\nМИР САЛЬНИКОВ',
          footerTextSize: '32'
        },
        mergeWithImage:
          (isExistMainPhoto && !isMainPhotoGenerated) || mergeWithCatPhoto
            ? {
                orientation: 'horizontal',
                //rumisota?
                path: !mergeWithCatPhoto ? `${UPLOAD_PATH}/rti/${this.prod_img}.jpg` : `${UPLOAD_PATH}${categoryImgPath}`
              }
            : undefined
      })

      // console.log('generatePhoto ~ newFilename:', newFilename)

      if (!isExistMainPhoto) {
        this.prod_img = newFilename.replace('.jpg', '')
      }

      if (isExistMainPhoto && !isExistAddnlPhoto && !isMainPhotoGenerated && !mergeWithCatPhoto) {
        this.prod_images = [...new Set([...(this.images || []).filter((i) => i).map((i) => i.name)]), newFilename].join()
      }

      return newFilename
    }

    if (rumi) {
      const isExistMainPhoto = mergeWithCatPhoto || (await checkFileExists(`${UPLOAD_PATH}/rumi/${this.prod_img_rumi}.jpg`))
      const isMainPhotoGenerated = this.prod_img_rumi?.includes('g_')

      const genAdnlPhotos = this.images?.find((x) => x.name.includes('g_a_'))
      const isExistAddnlPhoto = genAdnlPhotos && (await checkFileExists(`${UPLOAD_PATH}${genAdnlPhotos?.path}`))

      const newFilename = isExistMainPhoto && !mergeWithCatPhoto ? `g_a_${this.prod_sku}.jpg` : `g_${this.prod_sku}.jpg`

      if (isExistAddnlPhoto && isExistMainPhoto) {
        return false
      }

      // LangDict.prodsTranslate

      await generateImageWithTexts({
        texts: [
          //prettier
          // { text: this.prod_purpose?.length > 20 ? this.prod_purpose?.split(' ')[0] : this.prod_purpose, fontSize: 36 },
          { text: ``, fontSize: 36 },
          { text: `SKU: ${this.prod_sku}`, fontSize: 36 },
          { text: `Size: ${this.prod_size}`, fontSize: 36 },
          { text: `Type: ${this.prod_type}`, fontSize: 36 }
        ],
        outputFilename: `${UPLOAD_PATH}/rumi/${newFilename}`,
        frameWidth: 5,
        framePadding: 30,
        frameColor: '#27272a',
        gravity: 'North',
        textColor: '#27272a',
        backgroundColor: 'white',
        height: 450,
        width: 520,
        footer: {
          //rumisota?
          footerText: 'UAB Rumisota\ne-shop',
          footerTextSize: '32'
        },
        mergeWithImage:
          (isExistMainPhoto && !isMainPhotoGenerated) || mergeWithCatPhoto
            ? {
                orientation: 'horizontal',
                path: !mergeWithCatPhoto ? `${UPLOAD_PATH}/rumi/${this.prod_img_rumi}.jpg` : `${UPLOAD_PATH}${categoryImgPath}`
              }
            : undefined
      })

      if (!isExistMainPhoto) {
        this.prod_img_rumi = newFilename.replace('.jpg', '')
      }

      if (isExistMainPhoto && !isExistAddnlPhoto && !isMainPhotoGenerated && !mergeWithCatPhoto) {
        // this.prod_images = [...(this.images || [].filter((i) => i).map((i) => i?.name)), newFilename].join()
        this.prod_images = [...new Set([...(this.images || []).filter((i) => i).map((i) => i.name)]), newFilename].join()
      }

      return newFilename
    }
  }

  public static searchQueryBuilder = ({
    fields,
    query,
    searchValue,
    exSearchValue,
    searchBySize,
    translitValue
  }: {
    fields: Array<string>
    query
    searchValue: string
    exSearchValue?: string
    searchBySize?: boolean
    translitValue?: string
  }) => {
    let extandedQueries = parseExtandedQueries(searchValue || exSearchValue).filter((x) => !isSize(x))
    let rplValue = replaceFirstChar({ str: searchValue || exSearchValue })

    query.andWhere((builder) => {
      if (searchValue || exSearchValue) {
        builder.where((query) => {
          if (extandedQueries.length) {
            extandedQueries.map((q) => {
              query.whereRaw(`CONCAT_WS(',',??) LIKE ?`, [fields, `%${q}%`])
            })

            if (rplValue != (searchValue || exSearchValue)) {
              query.orWhereRaw(`CONCAT_WS(',',??) LIKE ?`, [fields, `%${rplValue}%`])
            }
          }
          if (translitValue) {
            query.orWhereRaw(`CONCAT_WS(',',??) LIKE ?`, [fields, `%${translitValue}%`])
          }
        })
      }
    })
  }

  public static fastSearchQueryBuilder = ({
    fields = [],
    query = {},
    searchValue = '',
    translatedValues = []
  }: {
    fields: Array<string>
    query
    searchValue: string
    translatedValues: Array<any>
  }) => {
    query.andWhere((builder) => {
      if (translatedValues.length) {
        translatedValues.map((item) => {
          if (item.text) {
            fields.map((column) => builder.orWhere(column, 'LIKE', `%${item.text}%`))
            fields.map((column) => builder.orWhere(column, 'LIKE', `%${String(item?.text).slice(0, String(item?.text).length)}%`))
          }
        })
      } else {
        if (searchValue.length > 2) {
          fields.map((column) => builder.orWhere(column, 'LIKE', `%${searchValue}%`))
        }
      }

      if (searchValue.length >= 4) {
        builder.orWhere('prod_size', 'LIKE', `${searchValue}%`)
      }
    })
  }
}
