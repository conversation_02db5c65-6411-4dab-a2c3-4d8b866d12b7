import Product from 'App/Models/Product'
import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo, afterFetch, afterFind, beforeCreate, beforeUpdate } from '@ioc:Adonis/Lucid/Orm'

export default class ProductSchema extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public body: object

  @column()
  public product_id: number

  @column()
  public related_ids?: string

  @belongsTo(() => Product, {
    foreignKey: 'prod_id',
    localKey: 'product_id'
  })
  public product: BelongsTo<typeof Product>

  @afterFetch()
  public static afterFetchHook(schemas) {
    schemas.map((item) => {
      try {
        item.$attributes.body = JSON.parse(item.$attributes.body)
      } catch (error) {
        console.error('ProductSchema.ts > afterFetchHook: ', error)
      }
    })
  }

  @afterFind()
  public static afterFindHook(productSchema) {
    try {
      productSchema.$attributes.body = JSON.parse(productSchema.$attributes.body)
    } catch (error) {
      console.error('ProductSchema.ts > afterFetchHook: ', error)
    }
  }

  @beforeCreate()
  @beforeUpdate()
  public static beforeCreate(productSchema: ProductSchema) {
    try {
      const parsedBody = typeof productSchema.body == 'string' ? JSON.parse(productSchema.body) : productSchema.body

      const related: string = parsedBody?.schema?.components?.filter(i => i.related).map((i) => i.productId).join()
      if (related?.length) {
        productSchema.related_ids = related
      }
    } catch (error) {
      console.error(error)
    }
  }
}
