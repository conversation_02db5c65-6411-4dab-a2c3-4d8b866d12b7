import { DateTime } from 'luxon'
import { BaseModel, afterFetch, afterFind, column } from '@ioc:Adonis/Lucid/Orm'

export default class ProductSnapshot extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column()
  public prod_id: number

  @column()
  public body: string

  @column()
  public user: string

  @column()
  public user_id: number

  static async getSnapshots(prod_id: number) {
    const res = await this.query()
      .where({
        prod_id
      })
      .orderBy('id', 'desc')
      .limit(300)

      return res//.map(i => i.toJSON())
  }
  @afterFetch()
  public static async afterFetch(snaphots: ProductSnapshot[]) {
    try {
      snaphots.map((productSnapshot) => {
        productSnapshot.body = JSON.parse(productSnapshot.body)
      })
    } catch (error) {
      console.log(error)
    }
  }

  @afterFind()
  public static async afterFind(productSnapshot: ProductSnapshot) {
    try {
      productSnapshot.body = JSON.parse(productSnapshot.body)
    } catch (error) {
      console.log(error)
    }
  }
}
