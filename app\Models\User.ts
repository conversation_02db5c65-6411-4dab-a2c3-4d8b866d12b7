// import { DateTime } from 'luxon'
import { BaseModel, beforeSave, column } from '@ioc:Adonis/Lucid/Orm'
import Hash from '@ioc:Adonis/Core/Hash'

export default class User extends BaseModel {
  @column({ isPrimary: true })
  public user_id: number

/*   @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime */

  @column()
  public user_name: string

  @column()
  public user_mail: string

  @column()
  public user_phone: string

  @column()
  public user_role: string

  @column({ serializeAs: null, columnName: 'user_password' })
  public password: string

  @beforeSave()
  public static async beforeSaveHook (user: User) {
    if (user.$dirty.password) {
      console.log('user.password before:', user.password);
      user.password = await Hash.make(String(user.password).replace(/\s*/gm, ''))
      console.log('user.password after: ', user.password);
    }
  }
}
