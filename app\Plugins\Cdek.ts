import loadSettings from 'App/Helpers/loadSettings'
import axios from 'axios'

export interface TariffResponse {
  delivery_sum: number
  period_min: number
  period_max: number
  calendar_min: number
  calendar_max: number
  weight_calc: number
  services: Service[]
  total_sum: number
  currency: string
}

export interface Service {
  code: string
  sum: number
  total_sum: number
  discount_percent: number
  discount_sum: number
}

export interface TariffRequest {
  tariff_code: number
  from_location: Location
  to_location: Location
  packages: Package[]
}

export interface Calculate extends Omit<Location, 'code'> {
  weight: number
  address: string
}
export interface Location {
  country_code: string
  city: string
  postal_code: number
  code: number
  address: string
}

export interface Package {
  length: number
  width: number
  height: number
  weight: number
}

export interface LocationsResponse {
  code: number
  city_uuid: string
  city: string
  fias_guid: string
  country_code: string
  country: string
  region: string
  region_code: number
  fias_region_guid: string
  sub_region: string
  longitude: number
  latitude: number
  time_zone: string
  payment_limit: number
}

export interface AuthResponse {
  access_token: string
  token_type: string
  expires_in: number
  scope: string
  jti: string
}

// https://api.cdek.ru/v2/location/cities?postal_code=184250
// https://api.cdek.ru/v2/calculator/tariff
// grant_type=client_credentials&client_id=BMLsThFszXx8JbpudmOoWpB7gVJqWjvv&client_secret=72qMmYKDlmS0oSevUMa7urnyV0Sq6Xr4

export class Cdek {
  authToken = null

  CDEK_LOGIN = 'xftFU1AnxOtVXZ4gJXUkUhdIoc0xZbj0'
  CDEK_PASSWORD = 'H1dwywEIE4Azs0OgisMSJrCIgritQHYh'
  BASE_URL = 'https://api.cdek.ru/v2'
  JSON_HEADERS = {
    'Accept': 'application/json',
    // 'X-App-Name': 'widget_pvz',
    'Authorization': `Bearer ${this.authToken}`
  }

  constructor() {
    // const instance = axios.create({
    //     baseURL: this.BASE_URL,
    // })
  }

  async getLocationsByPostCode(postalCode: string) {
    // https://api.cdek.ru/v2/location/cities?postal_code=184250

    await this.getAuthToken()

    const res = await axios(`${this.BASE_URL}/location/cities?postal_code=${postalCode}`, {
      headers: this.JSON_HEADERS
    })

    let result: LocationsResponse | undefined = undefined

    if (res.status == 200) {
      result = res.data?.[0]
    }

    return result
  }
  async getAuthToken() {
    let bodyContent = `grant_type=client_credentials&client_id=${this.CDEK_LOGIN}&client_secret=${this.CDEK_PASSWORD}`

    try {
      const response = await axios.post(
        `${this.BASE_URL}/oauth/token`,
        // {
        //   grant_type: 'client_credentials',
        //   client_id: CDEK_LOGIN,
        //   client_secret: CDEK_PASSWORD
        // },
        bodyContent,
        {
          headers: {
            'Accept': '*/*',
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      )

      this.authToken = response.data.access_token

      this.JSON_HEADERS.Authorization = `Bearer ${this.authToken}`

      if (!this.authToken) {
        throw new Error('Server not authorized to CDEK API')
      }
    } catch (error) {
      console.error('Error getting auth token:', error)
      throw new Error('Server not authorized to CDEK API')
    }
  }

  async calculate(toLocation: Calculate) {
    console.log('cdel calc:', toLocation)

    const settings = await loadSettings(['cdek.from_location', 'cdek.tariff', 'cdek.addnl%'])

    settings['cdek.addnl%']
    settings['cdek.tariff']
    settings['cdek.from_location']

    const reqlocations = await this.getLocationsByPostCode(String(toLocation.postal_code))

    if (!reqlocations?.code) {
      console.error(`ошибка getLocationsByPostCode по запросу: ${toLocation.postal_code}`)
    }

    const payload: TariffRequest = {
      'tariff_code': settings['cdek.tariff'] || 139, //139,
      //   'from_location': {
      //     'country_code': 'RU',
      //     'city': 'Калининград',
      //     'postal_code': 236000,
      //     'code': 152,
      //     'address': 'ул. Юрия Гагарина, д. 55А'
      //   },
      from_location: settings['cdek.from_location'],
      'to_location': {
        'country_code': toLocation.country_code || 'RU',
        'city': toLocation.city, //'Кировск',
        'postal_code': toLocation.postal_code, //236000,
        'code': reqlocations?.code || 44, //2804,
        'address': toLocation.address //'ул. Олимпийская д.10'
      },
      'packages': [
        {
          //   'length': 40,
          //   'width': 30,
          //   'height': 30,
          'weight': toLocation.weight || 1200
        }
      ]
    }

    // await this.getAuthToken()

    const res = await axios.post(this.BASE_URL + '/calculator/tariff', payload, {
      headers: this.JSON_HEADERS
    })

    let result: TariffResponse | undefined = undefined
    if (res.status == 200) {
      result = res.data

      if (result?.total_sum) {
        // total_sum + 5%
        result.total_sum = Math.ceil(result.total_sum + result.total_sum * (Number(settings['cdek.addnl%']) / 100))
      }

      return result
    }
  }
}

// const cdekApi = new Cdek()

// async function test() {
//   //   const res = await cdekApi.getLocationsByPostCode('184250')

//   const res = await cdekApi.calculate({
//     address: 'ул.Олимпийская д.10',
//     city: 'г.Кировск',
//     country_code: 'RU',
//     postal_code: 183250,
//     weight: 300
//   })
//   console.log('res', res)
// }

// test()
