import Product from 'App/Models/Product'
const XLSX = require('xlsx')

export class Emex {

    catalog: Array<Product> = []

    constructor() {

    }

    async loadProducts(limit?) {
        console.log('loadProducts limit', limit)
        this.catalog = await Product
                                    .query()
                                    .select('*')
                                    //.preload('category')
                                    .where(query => {
                                        query.where('prod_count', '>', 0)
                                        query.orWhere('prod_group_count', '>', 0)
                                    })
                                    .andWhere('prod_price', '>', 0)
                                    .if(limit, query => query.limit(limit))
    }

    async make(limit?) {

        await this.loadProducts(limit)

        let pricelist = this.catalog.map((item: Product) => {

            let _price = item.prod_price - (item.prod_price / 100 * item.prod_discount)
            let emex_price = _price + (_price * 0.2)

            return {
                'Наименование': `${item.prod_purpose} | ${item.prod_size} | (${item.prod_sku} ${item.prod_type}) | ${item.prod_analogsku} | ${item.prod_manuf}`,
                'Артикул': item.prod_analogsku,
                'Orpav(OEM)': item.prod_sku,
                'Размер': item.prod_size,
                //'Категория': item.category.cat_title,
                'Наличие': item.prod_group ? item.prod_group_count : item.prod_count,
                'Цена(Розн)': item.prod_price,
                'Цена Emex': emex_price,
                'Скидка(%)': item.prod_discount,
                'Бренд': item.prod_manuf,
                'Аналоги': item.prod_analogs,
                "Изобр.": item.prod_img,
            }
        })

        let wb = XLSX.utils.book_new()
        let ws = XLSX.utils.json_to_sheet(pricelist)
        XLSX.utils.book_append_sheet(wb, ws, 'sheet0')

        return XLSX.write(wb, { bookType: "xlsx", type: 'base64' })
        //XLSX.writeFile(wb, 'crosses3.xlsx')
    }

}