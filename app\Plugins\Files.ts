import Application from '@ioc:Adonis/Core/Application'

import * as fs from 'fs'
import * as util from 'util'
import * as _path from 'path'
import * as dyWatermark from 'imagemagick-dynamic-watermark'
//import * as gm from 'gm'
const gm = require('gm')

import Env from '@ioc:Adonis/Core/Env'
import { MultipartFileContract } from '@ioc:Adonis/Core/BodyParser'

class Files {
  UPLOAD_PATH: string

  constructor() {
    this.UPLOAD_PATH = _path.resolve(Env.get('UPLOAD_PATH')) //process.env.NODE_ENV === 'production' ? _path.resolve(Env.get('UPLOAD_PATH')) : Application.publicPath('data')
  }

  normalizeFileExtension(value: string): string {
    console.log('🚀 ~ normalizeFileExtension ~ value:', value)
    if (!value) return ''

    const filePath = String(value)
    // Находим последнюю точку в пути
    const lastDotIndex = filePath.lastIndexOf('.')

    // Если точка не найдена, возвращаем исходную строку
    if (lastDotIndex === -1) {
      return filePath
    }

    // Разделяем путь на часть до расширения и само расширение
    const pathWithoutExtension = filePath.substring(0, lastDotIndex)
    const extension = filePath.substring(lastDotIndex)

    // Возвращаем путь с расширением в нижнем регистре
    return pathWithoutExtension + extension.toLowerCase()
  }

  async deleteByPath(path) {
    console.log('🚀 ~ file: Files.ts:22 ~ deleteByPath ~ path:', path)
    try {
      fs.unlink(_path.join(this.UPLOAD_PATH, path), (err) => {
        console.log('🚀 ~ file: Files.ts:25 ~ fs.unlink ~ _path.join(this.UPLOAD_PATH, path):', _path.join(this.UPLOAD_PATH, path))
        // fs.unlink(this.UPLOAD_PATH  + path, (err) => {
        if (err) {
          //throw err
          return new Error('deleteByPath error')
        }
      })
    } catch (error) {
      return new Error(error)
    }
  }

  async applyWatermark({ filepath, type }: { filepath: string; type: 'rti' | 'rumi' }) {
    console.log('start applyWatermark', filepath)

    const optionsWatermark = {
      source: filepath,
      destination: filepath,
      type: 'watermark',
      watermark: {
        logo: `${this.UPLOAD_PATH}/watermark/${type}.png`,
        gravity: 'Center',
        logoWidthPercent: 1,
        logoHeightPercent: 1
      }
    }

    gm(filepath)
      .resize(900)
      .write(filepath, (err) => {
        if (!err) {
          dyWatermark.apply(optionsWatermark, (err, isOk) => {
            //console.log('dyWatermark isOk', isOk)
            // if (err) console.log('WATERMARK ERROR: ' + err)
            //else console.log('done')
          })
        } else {
          console.log('WATERMARK CRITICAL ERROR: ' + err)
        }
      })
  }

  async save({ file, type, watermark = true }: { file: MultipartFileContract; type: 'rti' | 'rumi'; watermark?: boolean }) {
    const uploadPath = `${this.UPLOAD_PATH}/${type}/`
    const uploadPath_original = `${this.UPLOAD_PATH}/original/`

    // Создаем папку если её нет
    if (!fs.existsSync(uploadPath_original)) {
      fs.mkdirSync(uploadPath_original, { recursive: true })
    }

    const fileName = file.clientName ? this.normalizeFileExtension(file.clientName) : file.fileName

    if (file.clientName) {
      await file.move(uploadPath, { name: fileName, overwrite: true })
    } else {
      await file.move(uploadPath, { overwrite: true })
    }

    const nameWithoutExt = fileName?.slice(0, fileName?.lastIndexOf('.'))

    fs.copyFileSync(`${uploadPath}${fileName}`, `${uploadPath_original}${nameWithoutExt}.jpg`)

    if (watermark) {
      await this.applyWatermark({ filepath: file.filePath, type })
    }

    fs.renameSync(`${uploadPath}${fileName}`, `${uploadPath}${nameWithoutExt}.jpg`)

    return `${uploadPath}${nameWithoutExt}.jpg`
  }
}

export const FilePlugin = new Files()
