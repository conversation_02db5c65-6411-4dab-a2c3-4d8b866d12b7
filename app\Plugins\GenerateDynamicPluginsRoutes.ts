import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import Application from '@ioc:Adonis/Core/Application'

const fs = require('fs')
const path = require('path')

class Plugins {

    list: string[]
    directory: string
    plugins

    constructor(directory = Application.makePath('app/Plugins/dynamic')) {
        this.directory = directory
    }

    async getFiles() {
        let _files: string[] = []

        const process = async (dir = this.directory) => {
            await Promise.all(fs.readdirSync(dir).map(
                async (_item) => {
                    if (fs.lstatSync(path.resolve(dir, _item)).isDirectory()) {
                        //console.log('Directory: ' + _item)
                        process(dir + '/' + _item)
                    } else {
                        //console.log('File: ' + _item)
                        _files.push(dir + '/' + _item)
                    }
                }
            ))
        }

        await process()
        return _files
    }

    async load(): Promise<dynPluginInterface[]>  {

        const files = await this.getFiles()
        let plugins = []

        await Promise.all(
            files.map(async (file) => {
                let plugin = await import(file)

                let n = file.split('/')
                let name = n[n.length - 1].split('.')[0]

                //plugins[name] = plugin[Object.keys(plugin)[0]]

                plugins.push(plugin.default)
            })
        )

        //console.log('plugins: ', plugins)
        return this.plugins = plugins
    }
}


//export const pluginsLoader = new Plugins()
//export const plugins = pluginsLoader.plugins


export default async function (): Promise<dynPluginInterface[]> {
    const pluginsLoader = new Plugins()
    const plugins = await pluginsLoader.load()

    return plugins.filter(i => i)
}