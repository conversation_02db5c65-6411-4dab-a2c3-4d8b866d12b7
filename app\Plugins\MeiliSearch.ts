import Database from '@ioc:Adonis/Lucid/Database'
import { Prisma, products } from '@prisma/client'
import { isSize } from 'App/Helpers/isSize'
import Product from 'App/Models/Product'
// import { productProvider } from 'App/Providers/ProductProvider'
import { $prisma } from 'App/Services/Prisma'
import { MeiliSearch } from 'meilisearch'

export class MeiliSearchPlugin {
  client: MeiliSearch
  index: 'products' | string

  constructor({ host = 'http://localhost:7700', apiKey = 'aSampleMasterKey', index = 'products' }) {
    this.client = new MeiliSearch({
      host,
      apiKey
    })
    this.index = index || 'products'
  }

  // async transformToAdonisProducts(hits) {
  //   return productProvider.initAndTransformToAdonisModel(hits)
  // }

  async checkIndexExists(indexUid: string) {
    try {
      const index = await this.client.getIndex(indexUid)
      return !!index
    } catch (error) {
      if (error.response.status === 404) {
        return false
      }
      throw error // если другая ошибка — пробрасываем дальше
    }
  }

  /**
   * Генерирует уникальное имя для временного индекса
   * Использует timestamp для гарантии уникальности
   */
  private generateTempIndexName(baseName: string = 'products'): string {
    const timestamp = Date.now()
    const randomSuffix = Math.random().toString(36).substring(2, 8)
    return `${baseName}_temp_${timestamp}_${randomSuffix}`
  }

  /**
   * Копирует все настройки из исходного индекса в целевой индекс
   * Необходимо для корректной работы swapIndexes
   */
  private async copyIndexSettings(sourceIndexName: string, targetIndexName: string, timeoutMs: number = 15000): Promise<void> {
    try {
      console.log(`📋 Копируем настройки из ${sourceIndexName} в ${targetIndexName}`)

      // Получаем настройки исходного индекса
      const settings = await this.client.index(sourceIndexName).getSettings()

      // Применяем настройки к целевому индексу
      const updateTask = await this.client.index(targetIndexName).updateSettings(settings)

      // Ждем завершения применения настроек
      await this.client.tasks.waitForTask(updateTask.taskUid, { timeout: timeoutMs })

      console.log(`✅ Настройки скопированы успешно`)
    } catch (error) {
      console.error(`❌ Ошибка копирования настроек:`, error)
      throw error
    }
  }

  /**
   * Выполняет swap индексов для атомарного обновления данных без простоев
   * @param tempIndexName Имя временного индекса
   * @param mainIndexName Имя основного индекса (по умолчанию 'products')
   * @param timeoutMs Таймаут ожидания в миллисекундах
   */
  private async performIndexSwap(
    tempIndexName: string,
    mainIndexName: string = 'products',
    timeoutMs: number = 30000
  ): Promise<void> {
    try {
      console.log(`🔄 Выполняем swap индексов: ${tempIndexName} ↔ ${mainIndexName}`)

      // Выполняем swap
      const swapTask = await this.client.swapIndexes([
        { indexes: [tempIndexName, mainIndexName] }
      ])

      console.log(`✅ Swap задача создана: ${swapTask.taskUid}`)

      // Ждем завершения swap операции
      await this.client.tasks.waitForTask(swapTask.taskUid, { timeout: timeoutMs })
      console.log(`✅ Swap индексов завершен успешно`)

      // После успешного swap временный индекс становится основным,
      // поэтому удаляем старый индекс (который теперь имеет имя tempIndexName)
      await this.client.deleteIndex(tempIndexName)
      console.log(`🗑️ Временный индекс ${tempIndexName} удален`)

    } catch (error) {
      console.error(`❌ Ошибка при swap индексов:`, error)

      // В случае ошибки пытаемся очистить временный индекс
      try {
        await this.client.deleteIndex(tempIndexName)
        console.log(`🧹 Временный индекс ${tempIndexName} очищен после ошибки`)
      } catch (cleanupError) {
        console.warn(`⚠️ Не удалось очистить временный индекс ${tempIndexName}:`, cleanupError)
      }

      throw error
    }
  }


  productsDB() {
    return this.client.index(this.index)
  }

  generateSkuTokens(articles: string | string[], minSuffixLength = 4, splitPattern = /[^a-z0-9]+/): string[] {
    const MIN_TOKEN_LENGTH = 3
    const tokens = new Set<string>()
    const list = Array.isArray(articles) ? articles : [articles]

    for (const raw of list) {
      if (!raw) continue
      const art = raw.trim().toLowerCase()
      // tokens.add(art)

      const segments = art.split(splitPattern).filter(Boolean)
      const base = segments.join('')
      tokens.add(base)

      // Все суффиксы от конкатенации сегментов
      for (let i = 0; i < base.length; i++) {
        const suf = base.substring(i)
        if (suf.length >= minSuffixLength) tokens.add(suf)
      }

      // Группы «буквы+цифры»
      const groups = art.match(/[a-z]+\d+|\d+/g)
      if (groups) groups.forEach((g) => tokens.add(g))
    }

    return [...tokens].filter((t) => t.length > MIN_TOKEN_LENGTH)
  }

  async _search(value) {
    const res = await this.client.index(this.index).search(value, {
      attributesToRetrieve: [
        'prod_id'
        // 'prod_sku'
      ],
      limit: 200
      // attributesToHighlight: ['prod_sku']
    })

    return res
  }
  async syncCategoriesByPrisma() {
    //console.time('syncCategoriesByPrisma')
    const rawCategories = await $prisma.cats.findMany({
      where: {
        cat_active: true
      }
    })

    const categories = rawCategories //.map(({ cat_id: id, ...rest }) => ({ id, ...rest }))

    const indexName = 'categories'

    // Проверяем существование индекса
    const indexExists = await this.checkIndexExists(indexName)

    if (indexExists) {
      // Используем updateDocuments для обновления существующего индекса
      console.log('🔄 Индекс categories существует, обновляем данные')

      // Очищаем индекс
      const deleteTask = await this.client.index(indexName).deleteAllDocuments()
      await this.client.tasks.waitForTask(deleteTask.taskUid, { timeout: 10000 })

      // Загружаем новые данные
      const result = await this.client.index(indexName).addDocuments(categories)
      await this.client.tasks.waitForTask(result.taskUid, { timeout: 30000 })

      console.log('✅ Категории обновлены')
    } else {
      // Создаем новый индекс
      console.log('🆕 Создаем индекс categories')

      await this.client.createIndex(indexName, { primaryKey: 'cat_id' })

      await this.client.index(indexName).updateSettings({
        filterableAttributes: [
          'cat_id',
          'cat_url',
          'cat_rootcat',
          'cat_active',
          'cat_url_ru',
          'cat_url_de',
          'cat_url_en',
          'cat_url_es',
          'cat_url_it',
          'cat_url_de',
          'cat_url_fr',
          'cat_url_pl'
        ],
        sortableAttributes: ['cat_sort', 'cat_search_sort']
      })

      await this.updateRankingRulesSortFirst(indexName)

      const result = await this.client.index(indexName).addDocuments(categories)
      await this.client.tasks.waitForTask(result.taskUid, { timeout: 30000 })

      console.log('✅ Индекс categories создан')
    }

    //console.timeEnd('syncCategoriesByPrisma')
    return { success: true, count: categories.length }
  }

  async syncColumnsByPrisma() {
    //console.time('syncColumnsByPrisma')
    const rawColumns = await $prisma.cat_columns.findMany({
      orderBy: {
        ID: 'asc'
      }
    })

    const columns = rawColumns

    const indexName = 'columns'

    // Проверяем существование индекса
    const indexExists = await this.checkIndexExists(indexName)

    if (indexExists) {
      // Используем updateDocuments для обновления существующего индекса
      console.log('🔄 Индекс columns существует, обновляем данные')

      // Очищаем индекс
      const deleteTask = await this.client.index(indexName).deleteAllDocuments()
      await this.client.tasks.waitForTask(deleteTask.taskUid, { timeout: 10000 })

      // Загружаем новые данные
      const result = await this.client.index(indexName).addDocuments(columns)
      await this.client.tasks.waitForTask(result.taskUid, { timeout: 30000 })

      console.log('✅ Колонки обновлены')
    } else {
      // Создаем новый индекс
      console.log('🆕 Создаем индекс columns')

      await this.client.createIndex(indexName, { primaryKey: 'ID' })
      await this.client.index(indexName).updateSettings({
        filterableAttributes: ['cat_id', 'keyname'],
        sortableAttributes: ['sort', 'ID']
      })

      await this.updateRankingRulesSortFirst(indexName)

      const result = await this.client.index(indexName).addDocuments(columns)
      await this.client.tasks.waitForTask(result.taskUid, { timeout: 30000 })

      console.log('✅ Индекс columns создан')
    }

    //console.timeEnd('syncColumnsByPrisma')
    return { success: true, count: columns.length }
  }

  async syncFiltersByPrisma() {
    //console.time('syncFiltersByPrisma')
    const filters = await $prisma.filters.findMany()

    const indexName = 'filters'

    // Проверяем существование индекса
    const indexExists = await this.checkIndexExists(indexName)

    if (indexExists) {
      // Используем updateDocuments для обновления существующего индекса
      console.log('🔄 Индекс filters существует, обновляем данные')

      // Очищаем индекс
      const deleteTask = await this.client.index(indexName).deleteAllDocuments()
      await this.client.tasks.waitForTask(deleteTask.taskUid, { timeout: 10000 })

      // Загружаем новые данные
      const result = await this.client.index(indexName).addDocuments(filters)
      await this.client.tasks.waitForTask(result.taskUid, { timeout: 30000 })

      console.log('✅ Фильтры обновлены')
    } else {
      // Создаем новый индекс
      console.log('🆕 Создаем индекс filters')

      await this.client.createIndex(indexName, { primaryKey: 'id' })

      await this.client.index(indexName).updateSettings({
        filterableAttributes: ['category_id', 'field']
      })

      await this.updateRankingRulesSortFirst(indexName)

      const result = await this.client.index(indexName).addDocuments(filters)
      await this.client.tasks.waitForTask(result.taskUid, { timeout: 30000 })

      console.log('✅ Индекс filters создан')
    }

    //console.timeEnd('syncFiltersByPrisma')
    return { success: true, count: filters.length }
  }

  async updateRankingRulesSortFirst(documentName = 'products', timeoutMs: number = 10000) {
    try {
      const newRankingRules = ['sort', 'words', 'typo', 'proximity', 'attribute', 'exactness']

      const task = await this.client.index(documentName).updateRankingRules(newRankingRules)
      console.log('Update ranking rules task:', task)
      // Можно дождаться завершения задачи
      await this.client.tasks?.waitForTask(task.taskUid, { timeout: timeoutMs })
      console.log('Ranking rules updated successfully.')
    } catch (error) {
      console.error('Error updating ranking rules:', error)
    }
  }

  // 17.04.25 - old ['exactness', 'sort', 'words', 'typo', 'proximity', 'attribute']
  // 16.05.25 - old ['exactness', 'attribute', 'sort', 'words', 'typo', 'proximity']
  async updateRankingRules(
    documentName = 'products',
    newRankingRules = [
      //format
      'sort',
      'exactness',
      'typo',
      'attribute',
      'words',
      'proximity'
    ],
    timeoutMs: number = 10000
  ) {
    try {
      const task = await this.client.index(documentName).updateRankingRules(newRankingRules)
      console.log('Update ranking rules task:', task)
      // Можно дождаться завершения задачи
      await this.client.tasks?.waitForTask(task.taskUid, { timeout: timeoutMs })
      console.log('Ranking rules updated successfully.')
    } catch (error) {
      console.error('Error updating ranking rules:', error)
    }
  }

  // Вызвать эту функцию один раз для обновления настроек индекса
  // updateRankingRulesSortFirst();

  async syncProductsByPrisma(uploadProducts: products[] | undefined = undefined) {
    //console.time('syncProductsByPrisma')

    const { clientProductDBfields } = Product.getProductColumns()
    const sizeFields = ['size_in', 'size_in_2', 'size_out', 'size_out_2', 'size_h', 'size_h_2']

    const sortableAndFilterableFields = clientProductDBfields.map((i) => i.split('.')[1]).filter((i) => i)

    sortableAndFilterableFields.push(...sizeFields)

    sortableAndFilterableFields.push('prod_model')
    sortableAndFilterableFields.push('inStock')
    sortableAndFilterableFields.push('cat_search_sort')

    // sortableAndFilterableFields.push('skuTokens')

    // console.log("🚀 ~ MeiliSearchPlugin ~ syncProductsByPrisma ~ sortableAndFilterableFields:", sortableAndFilterableFields)

    const categories = await $prisma.cats.findMany({
      select: {
        cat_id: true,
        cat_title: true,
        cat_note: true,
        cat_search_sort: true,
        cat_sort: true
      },
      where: {
        cat_active: true,
        duplicate: false
      }
    })

    const products =
      uploadProducts ||
      (await $prisma.products.findMany({
        where: {
          prod_cat: {
            in: (
              await $prisma.cats.findMany({
                where: { cat_active: true },
                select: { cat_id: true }
              })
            ).map((cat) => String(cat.cat_id))
          }
        },
        orderBy: {
          prod_id: 'desc'
        }
      }))

    // Создаем карту категорий для быстрого доступа
    const categoriesMap = new Map(categories.map((cat) => [String(cat.cat_id), cat]))

    // Массив для хранения всех товаров (основных + виртуальных)
    const allMeiliProducts: Record<string, any>[] = []

    const meiliProducts = products.map((product) => {
      const meiliProduct: Record<string, any> = { ...product }
      for (const field of sizeFields) {
        if (meiliProduct[field]) {
          meiliProduct[field] = Number(meiliProduct[field])
        }
      }

      meiliProduct.inStock = meiliProduct.prod_count > 0
      meiliProduct.id = String(meiliProduct.prod_id) // Убеждаемся, что ID - строка

      // Добавляем флаг виртуальности для основного товара
      meiliProduct.isVirtual = false

      // Создаем массив артикулов для генерации токенов
      const skuArray = [meiliProduct.prod_sku, meiliProduct.prod_analogsku, ...(meiliProduct.prod_analogs ? meiliProduct.prod_analogs.split(',') : [])].filter(Boolean) // Удаляем пустые значения

      meiliProduct.skuTokens = this.generateSkuTokens(skuArray as string[])

      // Добавляем информацию о категории к товару
      const category = categoriesMap.get(String(meiliProduct.prod_cat))
      if (category) {
        // Принудительно преобразуем cat_search_sort в число
        const cat_search_sort = Number(category.cat_search_sort) || 0

        meiliProduct.category = {
          cat_id: category.cat_id,
          cat_title: category.cat_title,
          cat_search_sort: cat_search_sort
        }

        // Добавляем поле напрямую в корень товара для удобства сортировки
        meiliProduct.cat_search_sort = cat_search_sort
      }

      // Создаем виртуальные товары на основе аналогов
      if (meiliProduct.prod_analogs) {
        const virtualProducts = this.splitAnalogsToProducts(meiliProduct)

        // Обрабатываем каждый виртуальный товар
        virtualProducts.forEach((virtualProduct: Record<string, any>) => {
          // Копируем все поля размеров
          for (const field of sizeFields) {
            if (virtualProduct[field]) {
              virtualProduct[field] = Number(virtualProduct[field])
            }
          }

          // Устанавливаем флаги
          virtualProduct.inStock = virtualProduct.prod_count > 0
          virtualProduct.isVirtual = true

          // Генерируем токены для виртуального товара
          const virtualSkuArray = [virtualProduct.prod_sku, virtualProduct.prod_analogsku].filter(Boolean)
          virtualProduct.skuTokens = this.generateSkuTokens(virtualSkuArray as string[])

          // Добавляем информацию о категории
          if (category) {
            const cat_search_sort = Number(category.cat_search_sort) || 0
            virtualProduct.category = {
              cat_id: category.cat_id,
              cat_title: category.cat_title,
              cat_search_sort: cat_search_sort
            }
            virtualProduct.cat_search_sort = cat_search_sort
          }

          // Добавляем виртуальный товар в общий массив
          allMeiliProducts.push(virtualProduct)
        })
      }

      return meiliProduct
    })

    // Объединяем основные товары с виртуальными
    allMeiliProducts.unshift(...meiliProducts)

    const virtualCount = allMeiliProducts.length - meiliProducts.length
    console.log(`🚀 ~ MeiliSearchPlugin ~ syncProductsByPrisma ~ Всего товаров: ${allMeiliProducts.length} (основных: ${meiliProducts.length}, виртуальных: ${virtualCount})`)

    // Проверяем на дублирующиеся ID
    const allIds = allMeiliProducts.map(p => p.id)
    const uniqueIds = new Set(allIds)
    if (allIds.length !== uniqueIds.size) {
      console.error('⚠️ Обнаружены дублирующиеся ID!', {
        total: allIds.length,
        unique: uniqueIds.size,
        duplicates: allIds.length - uniqueIds.size
      })

      // Находим и выводим дублирующиеся ID
      const idCounts = new Map()
      allIds.forEach(id => {
        idCounts.set(id, (idCounts.get(id) || 0) + 1)
      })

      const duplicateIds = Array.from(idCounts.entries())
        .filter(([id, count]) => count > 1)
        .slice(0, 10) // Показываем первые 10 дубликатов

      console.error('🔍 Примеры дублирующихся ID:')
      duplicateIds.forEach(([id, count]) => {
        const products = allMeiliProducts.filter(p => p.id === id)
        console.error(`ID "${id}" встречается ${count} раз:`)
        products.forEach((p, index) => {
          console.error(`  ${index + 1}. prod_id: ${p.prod_id}, isVirtual: ${p.isVirtual}, prod_analogsku: ${p.prod_analogsku}`)
        })
      })
    }

    // Проверяем структуру первых товаров
    // console.log('🔍 Первый основной товар:', {
    //   id: allMeiliProducts[0]?.id,
    //   prod_id: allMeiliProducts[0]?.prod_id,
    //   isVirtual: allMeiliProducts[0]?.isVirtual,
    //   prod_sku: allMeiliProducts[0]?.prod_sku
    // })

    if (virtualCount > 0) {
      const firstVirtual = allMeiliProducts.find(p => p.isVirtual)
      console.log('🔍 Первый виртуальный товар:', {
        id: firstVirtual?.id,
        prod_id: firstVirtual?.prod_id,
        isVirtual: firstVirtual?.isVirtual,
        prod_analogsku: firstVirtual?.prod_analogsku
      })
    }

    // Добавляем поля для сортировки по категории
    sortableAndFilterableFields.push('id')
    sortableAndFilterableFields.push('category.cat_search_sort')
    sortableAndFilterableFields.push('cat_search_sort')
    sortableAndFilterableFields.push('isVirtual')

    /// ------------- UPLOAD TO MEILISEARCH -------------

    // Определяем настройки индекса, которые будут применены
    const indexSettings = {
      filterableAttributes: sortableAndFilterableFields,
      sortableAttributes: sortableAndFilterableFields,
      distinctAttribute: 'prod_id',
      searchableAttributes: [
        'skuTokens',
        'prod_sku',
        'prod_analogsku',
        'prod_analogs',
        'prod_purpose',
        'prod_type',
        'prod_material',
        'prod_uses',
        'prod_note',
        'prod_manuf',
        'prod_year',
        'prod_size',
        'prod_model',
        'category.cat_title',
        'isVirtual',
        'id'
      ],
      pagination: {
        maxTotalHits: 200000
      },
      typoTolerance: {
        disableOnAttributes: ['skuTokens'],
        enabled: true,
        minWordSizeForTypos: {
          oneTypo: 6,
          twoTypos: 10
        },
        disableOnNumbers: true
      }
    }

    const mainIndexName = 'products'
    let res

    // Проверяем существование основного индекса
    const mainIndexExists = await this.checkIndexExists(mainIndexName)

    if (mainIndexExists) {
      // Используем swapIndexes для атомарного обновления без простоев
      console.log('🔄 Основной индекс существует, используем swapIndexes для обновления без простоев')

      const tempIndexName = this.generateTempIndexName(mainIndexName)
      console.log(`📝 Создаем временный индекс: ${tempIndexName}`)

      try {
        // 1. Создаем временный индекс
        await this.client.createIndex(tempIndexName, { primaryKey: 'id' })

        // 2. Копируем настройки из основного индекса во временный
        await this.copyIndexSettings(mainIndexName, tempIndexName)

        // 3. Обновляем ranking rules для временного индекса
        await this.updateRankingRules(tempIndexName, undefined, 15000)

        // 4. Загружаем данные во временный индекс
        console.log('📤 Загружаем товары во временный индекс:', allMeiliProducts.length)
        res = await this.client.index(tempIndexName).addDocuments(allMeiliProducts)

        // 5. Ждем завершения загрузки документов во временный индекс
        console.log(`⏳ Ждем завершения загрузки во временный индекс (задача ${res.taskUid})...`)
        await this.client.tasks.waitForTask(res.taskUid, { timeout: 300000 }) // 5 минут для загрузки 265k документов
        console.log(`✅ Загрузка во временный индекс завершена`)

        // 6. Выполняем swap индексов (теперь все задачи завершены)
        await this.performIndexSwap(tempIndexName, mainIndexName, 60000) // 1 минута для swap

        console.log('✅ Синхронизация с swapIndexes завершена успешно')

      } catch (error) {
        console.error('❌ Ошибка при синхронизации с swapIndexes:', error)

        // В случае ошибки пытаемся удалить временный индекс
        try {
          if (await this.checkIndexExists(tempIndexName)) {
            await this.client.deleteIndex(tempIndexName)
            console.log(`🧹 Временный индекс ${tempIndexName} удален после ошибки`)
          }
        } catch (cleanupError) {
          console.warn(`⚠️ Не удалось удалить временный индекс ${tempIndexName}:`, cleanupError)
        }

        throw error
      }

    } else {
      // Основной индекс не существует, создаем его напрямую
      console.log('🆕 Основной индекс не существует, создаем новый индекс products')

      await this.client.createIndex(mainIndexName, { primaryKey: 'id' })

      // Применяем настройки
      const settingsTask = await this.client.index(mainIndexName).updateSettings(indexSettings)
      await this.client.tasks.waitForTask(settingsTask.taskUid, { timeout: 15000 })

      await this.updateRankingRules(mainIndexName, undefined, 15000)

      // Загружаем данные
      console.log('📤 Загружаем товары в новый индекс:', allMeiliProducts.length)
      res = await this.client.index(mainIndexName).addDocuments(allMeiliProducts)

      // Ждем завершения загрузки
      console.log(`⏳ Ждем завершения загрузки в новый индекс (задача ${res.taskUid})...`)
      await this.client.tasks.waitForTask(res.taskUid, { timeout: 120000 }) // 2 минуты для загрузки
      console.log('✅ Данные загружены в новый индекс')

      console.log('✅ Новый индекс products создан и заполнен')
    }

    /// ------------- UPLOAD TO MEILISEARCH -------------

    // Проверяем статус загрузки документов через некоторое время
    setTimeout(async () => {
      try {
        console.log(`📋 Проверяем статус загрузки документов (задача ${res.taskUid})...`)

        // Получаем информацию о задаче
        const taskInfo = await this.client.tasks.getTask(res.taskUid)
        console.log('📊 Статус задачи загрузки:', {
          uid: taskInfo.uid,
          status: taskInfo.status,
          type: taskInfo.type,
          duration: taskInfo.duration,
          enqueuedAt: taskInfo.enqueuedAt,
          startedAt: taskInfo.startedAt,
          finishedAt: taskInfo.finishedAt
        })

        // Проверяем статистику индекса
        const stats = await this.client.index('products').getStats()
        console.log('📈 Статистика индекса:', {
          numberOfDocuments: stats.numberOfDocuments,
          isIndexing: stats.isIndexing,
          fieldDistribution: Object.keys(stats.fieldDistribution || {}).length
        })

        if (taskInfo.status === 'succeeded') {
          console.log('✅ Загрузка документов завершена успешно!')
        } else if (taskInfo.status === 'processing' || taskInfo.status === 'enqueued') {
          console.log('⏳ Загрузка документов продолжается...')
        } else if (taskInfo.status === 'failed') {
          console.log('❌ Загрузка документов завершилась с ошибкой')
        }

      } catch (error) {
        console.error('❌ Ошибка получения статуса загрузки:', error)
      }
    }, 3000) // Проверяем через 3 секунды

    //console.timeEnd('syncProductsByPrisma')

    // Возвращаем расширенную информацию о результате
    return {
      res,
      product0: allMeiliProducts[0],
      totalProducts: allMeiliProducts.length,
      mainProducts: meiliProducts.length,
      virtualProducts: virtualCount,
      method: mainIndexExists ? 'swapIndexes' : 'createIndex'
    }
  }

  async syncAllByPrisma() {
    console.log('🚀 Начинаем полную синхронизацию MeiliSearch...')

    const results = {
      products: null as any,
      categories: null as any,
      columns: null as any,
      filters: null as any,
      errors: [] as string[]
    }

    try {
      results.products = await this.syncProductsByPrisma()
      console.log('✅ Синхронизация товаров завершена')
    } catch (error) {
      console.error('❌ Ошибка синхронизации товаров:', error)
      results.errors.push(`products: ${error.message}`)
    }

    try {
      results.categories = await this.syncCategoriesByPrisma()
      console.log('✅ Синхронизация категорий завершена')
    } catch (error) {
      console.error('❌ Ошибка синхронизации категорий:', error)
      results.errors.push(`categories: ${error.message}`)
    }

    try {
      results.columns = await this.syncColumnsByPrisma()
      console.log('✅ Синхронизация колонок завершена')
    } catch (error) {
      console.error('❌ Ошибка синхронизации колонок:', error)
      results.errors.push(`columns: ${error.message}`)
    }

    try {
      results.filters = await this.syncFiltersByPrisma()
      console.log('✅ Синхронизация фильтров завершена')
    } catch (error) {
      console.error('❌ Ошибка синхронизации фильтров:', error)
      results.errors.push(`filters: ${error.message}`)
    }

    if (results.errors.length > 0) {
      console.warn(`⚠️ Синхронизация завершена с ${results.errors.length} ошибками:`, results.errors)
    } else {
      console.log('🎉 Полная синхронизация MeiliSearch завершена успешно!')
    }

    return results
  }

  async updateProduct({ product, isAdonisModel = false }: { product: Product | Record<string, unknown>; isAdonisModel?: boolean }) {
    try {
      const productJson: Record<string, any> = isAdonisModel ? (product as Product).toJSON() : { ...product }
      const sizeFields = ['size_in', 'size_in_2', 'size_out', 'size_out_2', 'size_h', 'size_h_2']

      // Добавляем флаг виртуальности, для основного товара
      productJson.isVirtual = false

      // Обработка размеров
      for (const field of sizeFields) {
        if (productJson[field]) {
          productJson[field] = Number(productJson[field])
        }
      }

      // Добавляем флаг наличия
      productJson.inStock = productJson.prod_count > 0
      productJson.id = productJson.prod_id

      // Создаем массив артикулов для генерации токенов
      const skuArray = [productJson.prod_sku, productJson.prod_analogsku, ...(productJson.prod_analogs ? productJson.prod_analogs.split(',') : [])].filter(Boolean) // Удаляем пустые значения

      productJson.skuTokens = this.generateSkuTokens(skuArray as string[])

      // Получаем информацию о категории для добавления cat_search_sort
      if (productJson.prod_cat) {
        const category = await $prisma.cats.findFirst({
          where: { cat_id: Number(productJson.prod_cat) },
          select: { cat_id: true, cat_title: true, cat_search_sort: true }
        })

        if (category) {
          // Принудительно преобразуем cat_search_sort в число
          const cat_search_sort = Number(category.cat_search_sort) || 0

          productJson.category = {
            cat_id: category.cat_id,
            cat_title: category.cat_title,
            cat_search_sort: cat_search_sort
          }

          // Добавляем поле напрямую в корень товара для удобства сортировки
          productJson.cat_search_sort = cat_search_sort
        }
      }

      // Проверяем, есть ли у товара группа
      if (productJson.prod_group) {
        // Получаем все товары с такой же группой
        const groupProducts = await $prisma.products.findMany({
          where: { prod_group: String(productJson.prod_group) }
        })

        // Получаем уникальные ID категорий для всех товаров в группе
        const categoryIds = [...new Set(groupProducts.map((p) => Number(p.prod_cat)).filter(Boolean))]

        // Получаем данные категорий одним запросом
        const categoriesData = await $prisma.cats.findMany({
          where: { cat_id: { in: categoryIds } },
          select: { cat_id: true, cat_title: true, cat_search_sort: true }
        })
        const categoriesMap = new Map(categoriesData.map((c) => [c.cat_id, c]))

        // Синхронизируем поля prod_count и prod_group_count для всех товаров в группе
        const documentsToUpdate = groupProducts.map((groupProduct) => {
          const enrichedProduct: any = { ...groupProduct }
          // Копируем значения полей из обновляемого товара
          enrichedProduct.prod_count = Number(productJson.prod_count)
          enrichedProduct.prod_group_count = Number(productJson.prod_count)

          // Устанавливаем флаг наличия
          enrichedProduct.inStock = enrichedProduct.prod_count > 0

          // Генерируем токены
          const skuArray = [enrichedProduct.prod_sku, enrichedProduct.prod_analogsku, ...(enrichedProduct.prod_analogs ? enrichedProduct.prod_analogs.split(',') : [])].filter(
            Boolean
          )
          enrichedProduct.skuTokens = this.generateSkuTokens(skuArray as string[])

          // Добавляем данные категории
          const category = categoriesMap.get(Number(enrichedProduct.prod_cat))
          if (category) {
            const cat_search_sort = Number(category.cat_search_sort) || 0
            enrichedProduct.category = {
              cat_id: category.cat_id,
              cat_title: category.cat_title,
              cat_search_sort
            }
            enrichedProduct.cat_search_sort = cat_search_sort
          }

          for (const field of sizeFields) {
            if (enrichedProduct[field]) {
              enrichedProduct[field] = Number(enrichedProduct[field])
            }
          }

          return enrichedProduct
        })

        // Обновляем все товары группы в MeiliSearch
        const updateTask = await this.client.index('products').updateDocuments(documentsToUpdate)
        await this.client.tasks.waitForTask(updateTask.taskUid, { timeout: 30000 })
        console.log(`Обновлено ${documentsToUpdate.length} товаров в группе ${productJson.prod_group}`)
      } else {
        // Если товар не входит в группу, обновляем только его
        const updateTask = await this.client.index('products').updateDocuments([productJson])
        await this.client.tasks.waitForTask(updateTask.taskUid, { timeout: 10000 })
      }

      // Добавляем виртуальные товары / разворачиваем строку prod_analogs в виртуальные товары
      // Только для основных товаров (не виртуальных)
      if (productJson.prod_analogs && !productJson.isVirtual) {
        await this.updateVirtualProducts(productJson)
      }

      return true
    } catch (error) {
      console.error('MeiliSearchPlugin ~ updateProduct error:', error)
      return false
    }
  }

  /**
   * Обновляет виртуальные товары для основного товара
   * Удаляет старые и создает новые виртуальные товары
   */
  private async updateVirtualProducts(productJson: Record<string, any>): Promise<void> {
    try {
      console.log(`🔄 Обновление виртуальных товаров для ${productJson.prod_id}`)

      // Сначала удаляем все старые виртуальные товары этого продукта
      await this.deleteVirtualProducts(productJson.prod_id)

      // Создаем новые виртуальные товары
      const virtualProducts = this.splitAnalogsToProducts(productJson)
      if (virtualProducts.length > 0) {
        console.log(`📦 Создание ${virtualProducts.length} виртуальных товаров для ${productJson.prod_id}`)

        // Добавляем виртуальные товары пакетно
        const addTask = await this.client.index('products').addDocuments(virtualProducts)
        await this.client.tasks.waitForTask(addTask.taskUid, { timeout: 15000 })
      }
    } catch (error) {
      console.error(`❌ Ошибка обновления виртуальных товаров для ${productJson.prod_id}:`, error)
    }
  }

  /**
   * Удаляет только виртуальные товары для указанного prod_id
   */
  private async deleteVirtualProducts(prodId: string | number): Promise<void> {
    try {
      const searchResults = await this.client.index('products').search('', {
        filter: `prod_id = ${prodId} AND isVirtual = true`,
        limit: 1000
      })

      if (searchResults.hits.length > 0) {
        const virtualIds = searchResults.hits.map(hit => hit.id)
        console.log(`🗑️ Удаление ${virtualIds.length} старых виртуальных товаров для ${prodId}`)
        const deleteTask = await this.client.index('products').deleteDocuments(virtualIds)
        await this.client.tasks.waitForTask(deleteTask.taskUid, { timeout: 10000 })
      }
    } catch (error) {
      console.warn(`⚠️ Не удалось удалить виртуальные товары для ${prodId}:`, error)
    }
  }

  splitAnalogsToProducts(product: Record<string, any>): Record<string, any>[] {
    if (!product.prod_analogs || typeof product.prod_analogs !== 'string') {
      return []
    }

    const analogsArray =
      product.prod_analogs
        ?.split(',')
        .map((i) => i.trim())
        .filter(Boolean)
        .filter((analog) => !isSize(analog)) // Фильтруем аналоги, которые НЕ являются размерами
        .filter((analog) => analog !== product.prod_analogsku) // Исключаем аналоги, совпадающие с prod_analogsku самого товара
        .filter((analog) => analog !== product.prod_sku) // Исключаем аналоги, совпадающие с prod_sku самого товара
        || []

    // Удаляем дубликаты аналогов
    const uniqueAnalogs = [...new Set(analogsArray)]

    return uniqueAnalogs.map((analog, index) => {
      return {
        ...product,
        id: `${product.prod_id}-${index}`,
        isVirtual: true,
        prod_analogsku: analog,
        prod_analogs: ''
      }
    })
  }

  /**
   * Удаляет товар из индекса MeiliSearch
   * Удаляет как основной товар, так и все его виртуальные копии
   * @param productId ID товара для удаления
   * @returns true в случае успеха, false в случае ошибки
   */
  async deleteProduct(productId: string | number) {
    try {
      console.log(`🗑️ Удаление товара с ID ${productId} из MeiliSearch`)

      // Удаляем основной товар
      const deleteMainTask = await this.client.index('products').deleteDocument(String(productId))
      await this.client.tasks.waitForTask(deleteMainTask.taskUid, { timeout: 5000 })
      console.log(`✅ Основной товар ${productId} удален`)

      // Удаляем все виртуальные товары этого продукта
      // Виртуальные товары имеют ID вида: {productId}-{index}
      // Ищем виртуальные товары по префиксу
      try {
        const searchResults = await this.client.index('products').search('', {
          filter: `prod_id = ${productId} AND isVirtual = true`,
          limit: 1000 // Максимум виртуальных товаров на один основной
        })

        if (searchResults.hits.length > 0) {
          const virtualIds = searchResults.hits.map(hit => hit.id)
          console.log(`🔍 Найдено ${virtualIds.length} виртуальных товаров для удаления:`, virtualIds)

          // Удаляем виртуальные товары
          const deleteVirtualTask = await this.client.index('products').deleteDocuments(virtualIds)
          await this.client.tasks.waitForTask(deleteVirtualTask.taskUid, { timeout: 10000 })
          console.log(`✅ Удалено ${virtualIds.length} виртуальных товаров`)
        }
      } catch (searchError) {
        console.warn(`⚠️ Не удалось найти виртуальные товары для ${productId}:`, searchError)
      }

      return true
    } catch (error) {
      console.error(`❌ MeiliSearchPlugin ~ deleteProduct error для ID ${productId}:`, error)
      return false
    }
  }
}

export const meiliDB = new MeiliSearchPlugin({})
