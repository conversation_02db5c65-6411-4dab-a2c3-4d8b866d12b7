import loadSettings from 'App/Helpers/loadSettings'

const got = require('got')
//import got from 'node-got';

export class PochtaRU {
  pochtaApiUrl: string

  constructor() {
    this.pochtaApiUrl = 'https://tariff.pochta.ru/tariff/v1/calculate?jsontext'
  }

  manualCalc({ orderprice, type = 'express', countryID = '643' }) {
    let ru_settings = {
      afterGRdelimeter: 10000,
      afterGRfactorValue: 100,
      GRvalues: {
        1000: 800,
        3000: 900,
        6000: 1000,
        9000: 1100,
        12000: 1200,
        20000: 1300,
        30000: 1400,
        40000: 1600
      }
    }

    let sng_settings = {
      afterGRdelimeter: 10000,
      afterGRfactorValue: 20,
      GRvalues: {
        1000: 1400,
        10000: 1600,
        20000: 1800,
        30000: 2000,
        40000: 2200,
        50000: 2400,
        60000: 2600,
        70000: 2800
      }
    }

    let settings = countryID == '643' ? ru_settings : sng_settings

    let calcValue = 0

    let sortedPR = Object.fromEntries(Object.entries(settings.GRvalues).sort())
    let maxGR = Object.keys(sortedPR).pop()

    if (orderprice > maxGR) {
      calcValue = Math.ceil(orderprice / settings.afterGRdelimeter) * settings.afterGRfactorValue

      return calcValue + settings.GRvalues[maxGR]
    } else {
      Object.keys(settings.GRvalues).map((key) => {
        if (key < orderprice) {
          calcValue = settings.GRvalues[key]
        }
      })

      return calcValue
    }
  }

  getPrice(data, adnl: number, percent?): number {
    let price = 0

    if (data.body) {
      try {
        let parsedBody = JSON.parse(data.body)
        price = Number(parsedBody.paynds || parsedBody.pay) / 100
        // console.log("🚀 ~ PochtaRU ~ getPrice ~ default price:", price)
      } catch (error) {
        // console.log("🚀 ~ PochtaRU ~ getPrice ~ error:", error)
      }
    } else {
      price = Number(data.paynds || data.pay) / 100
      // console.log('🚀 ~ PochtaRU ~ getPrice ~ 2default price:', price)
    }

    if (percent) {
      return price + (price / 100) * adnl
    } else {
      return price + adnl
    }
  }

  async calculate(country: any, destinationIndex: number, orderprice: number) {
    let settingsColumns = [
      'pochta.destination.from',
      'pochta.object.standart.ru',
      'pochta.object.standart.all',
      'pochta.object.express.ru',
      'pochta.object.express.all',
      'pochta.standart.addnl.ru',
      'pochta.standart.addnl.all',
      'pochta.express.addnl%.ru',
      'pochta.express.addnl%.all',
      'kce.addnl%'
    ]
    let settings = await loadSettings(settingsColumns)
    let shippingprice: number = 0

    return {
      standard: async (weight: number) => {
        if (!weight) weight = 700

        let pochttaApiResponse = undefined

        try {
          weight = Math.ceil(weight + 30)
        } catch (error) {}

        if (country == '643') {
          const data = await got(
            `${this.pochtaApiUrl}&object=${settings['pochta.object.standart.ru']}&from=${settings['pochta.destination.from']}&to=${destinationIndex}&weight=${weight}`
          ).json()
          pochttaApiResponse = data
          shippingprice = this.getPrice(data, settings['pochta.standart.addnl.ru'])
          console.log('🚀 ~ PochtaRU ~ standard: ~ shippingprice:', shippingprice)
        } else {
          const data = await got(`${this.pochtaApiUrl}&object=${settings['pochta.object.standart.all']}&country=${country}&weight=${weight}`).json()
          pochttaApiResponse = data

          shippingprice = this.getPrice(data, settings['pochta.standart.addnl.all'])
        }

        if (isNaN(shippingprice)) {
          console.log('calc standard shipping res isNan:', pochttaApiResponse)

          if (country == '643') shippingprice = 551
          else shippingprice = 991
        }

        return Math.ceil(shippingprice)
      },
      kce: async (weight: number) => {
        if (!weight) weight = 900

        weight = Math.ceil(weight)

        if (country == '643') {
          const data = await got(
            `${this.pochtaApiUrl}&object=${settings['pochta.object.express.ru']}&from=${settings['pochta.destination.from']}&to=${destinationIndex}&weight=${weight}`
          ).json()
          // const data = await response.json()
          shippingprice = this.getPrice(data, settings['kce.addnl%'], 'percent')
          console.log('🚀 ~ PochtaRU ~ kce: ~ shippingprice:', shippingprice)
        } else {
          const data = await got(`${this.pochtaApiUrl}&object=${settings['pochta.object.express.all']}&country=${country}&weight=${weight}`).json()
          // const data = await response.json()
          shippingprice = this.getPrice(data, settings['kce.addnl%'], 'percent')
        }

        let manualCalcResult = this.manualCalc({ orderprice, type: 'express', countryID: country })

        if (shippingprice < 800) {
          shippingprice = 821
        }

        if (isNaN(shippingprice)) {
          shippingprice = 0
        }

        let resPrice = Math.ceil(Math.max(shippingprice, manualCalcResult))

        if (resPrice == 0) {
          resPrice = 1400
        }

        return resPrice
      },
      express: async (weight: number) => {
        if (!weight) weight = 900

        weight = Math.ceil(weight)

        if (country == '643') {
          const data = await got(
            `${this.pochtaApiUrl}&object=${settings['pochta.object.express.ru']}&from=${settings['pochta.destination.from']}&to=${destinationIndex}&weight=${weight}`
          ).json()
          // const data = await response.json()
          shippingprice = this.getPrice(data, settings['pochta.express.addnl%.ru'], 'percent')
          console.log('🚀 ~ PochtaRU ~ express: ~ shippingprice:', shippingprice)
        } else {
          const data = await got(`${this.pochtaApiUrl}&object=${settings['pochta.object.express.all']}&country=${country}&weight=${weight}`).json()
          // const data = await response.json()
          shippingprice = this.getPrice(data, settings['pochta.express.addnl%.all'], 'percent')
        }

        let manualCalcResult = this.manualCalc({ orderprice, type: 'express', countryID: country })

        if (shippingprice < 800) {
          shippingprice = 821
        }

        if (isNaN(shippingprice)) {
          shippingprice = 0
        }

        let resPrice = Math.ceil(Math.max(shippingprice, manualCalcResult))

        if (resPrice == 0) {
          resPrice = 1400
        }

        if (country && country != '643') {
          console.log('Доставка вне РФ: ', resPrice / 2)

          return resPrice / 2
        }

        return resPrice
      }
    }
  }
}
