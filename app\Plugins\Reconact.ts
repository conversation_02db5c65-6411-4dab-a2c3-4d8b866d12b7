import Order from 'App/Models/Order'

export class Reconact {
  clientId: number
  constructor(clientId: number) {
    this.clientId = clientId
  }

  async getOrdersDataByClient(dates: string[]) {
    console.log('🚀 ~ Reconact ~ getOrdersDataByClient ~ dates:', dates)
    const data = await Order.query()
      .select('order_id', 'order_datetime', 'order_price', 'order_shippingprice', 'order_status', 'order_client')
      .where({
        order_client: this.clientId
      })
      .andWhereBetween('order_datetime', dates)
      // .debug(true)

    await Promise.all(
      data.map(async (order) => {
        await order.load('client')
        await order.client.load('org')
      })
    )

    return data.map( order => order.toJSON())
  }
}
