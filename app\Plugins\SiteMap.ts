import { getCategoryHrefByLocale, getProductHrefByLocale } from "App/Helpers/getProductHrefByLocale"
import Category     from "App/Models/Category"
import Product      from "App/Models/Product"
import { $prisma } from "App/Services/Prisma"

const convert = require('xml-js')

// sitemapGenerator.ts


const prisma = $prisma
const BASE_URL = 'https://mirsalnikov.ru'; // URL вашего сайта

// Максимальное количество URL в одном sitemap файле по протоколу
const SITEMAP_PAGE_SIZE = 50_000;

export class SiteMap {

    catalog
    categories: Array<Category>
    siteurl: string
    limit: number
    page: number

    constructor() {
        this.siteurl = 'https://mirsalnikov.ru/'
        this.categories = []
    }

    async loadProducts(page, limit) {

        let _catalog =  await Product
                                .query().select('*')
                                .orderBy('prod_count', 'desc')
                                .orderBy('prod_id', 'desc')
                                .paginate(page, limit)
                                //.limit(10)

        this.catalog = _catalog.toJSON().data
 
        console.log('this.catalog.lastPage: ', _catalog.lastPage);
    }

    async loadCategories(page, limit) {
        if (page == 1) {
            this.categories = await Category.query().select('*').where('cat_active', 1)
        }
    }

    async make(page = 1, limit = 10000) {
        const today = new Date()
        const fulldate = today.getFullYear() + '-' + today.getMonth() + '-' + today.getDate()

        await this.loadProducts(page, limit)
        await this.loadCategories(page, limit)

        let categoriesData = this.categories.map(category => {
            return {
                loc: this.siteurl + 'catalog/' + category.cat_url,
                lastmod: fulldate,
                priority: 0.5,
                changefreq: 'weekly'
            }
        })
        
        let productsData = this.catalog.map(product => {
            return {
                loc: this.siteurl + 'catalog/product/' + product.prod_id,
                lastmod: fulldate,
                priority: 0.3,
                changefreq: 'daily'
            }
        })
        

        const json = {
            _declaration: { 
                _attributes: { 
                    "version": "1.0", "encoding": "utf-8" 
                } 
            },
            'urlset': {
                _attributes: {
                    xmlns: 'http://www.sitemaps.org/schemas/sitemap/0.9'
                },
                url: [...categoriesData, ...productsData]
            }
        }

        const options = { compact: true, ignoreComment: true, spaces: 4 }

        let result = convert.json2xml(json, options)

        return result
    }

    async makeGoogle(page = 1, limit = 45000, locale = 'en') {

        const today = new Date()
        const fulldate = today.getFullYear() + '-' + today.getMonth() + '-' + today.getDate()
  
        await this.loadProducts(page, limit)
        await this.loadCategories(page, limit)

        let categoriesData = this.categories.map(category => {
            return {
                loc: getCategoryHrefByLocale(locale) + (locale == 'ru' ? (category.cat_url) : (category.$extras['cat_url_' + locale])),
                'xhtml:link': {
                    _attributes: {
                        rel: 'alternate',
                        hreflang: locale, // 'en' || 'de' ..etc
                        href: getCategoryHrefByLocale(locale) + (locale == 'ru' ? (category.cat_url) : (category.$extras['cat_url_' + locale]))
                    }
                },
                lastmod: today.toJSON().split('T')[0],
                changefreq: 'weekly',
                priority: 0.3,
            }
        })

        let productsData = this.catalog.map(product => {
            return {
                loc: getProductHrefByLocale(locale) + product.prod_id,
                'xhtml:link': {
                    _attributes: {
                        rel: 'alternate',
                        hreflang: locale, // 'en' || 'de' ..etc
                        href: getProductHrefByLocale(locale) + product.prod_id
                    }
                },
                lastmod: today.toJSON().split('T')[0],
                priority: 0.5,
                changefreq: 'daily'
            }
        })

        const json = {
            _declaration: {
                _attributes: {
                    "version": "1.0", "encoding": "utf-8"
                }
            },
            'urlset': {
                _attributes: {
                    xmlns: 'http://www.sitemaps.org/schemas/sitemap/0.9',
                    'xmlns:xhtml': "http://www.w3.org/1999/xhtml"
                },
                url: [...categoriesData, ...productsData]
            }
        }

        const options = { compact: true, ignoreComment: true, spaces: 4 }

        let result = convert.json2xml(json, options)

        return result

        
    }
}

