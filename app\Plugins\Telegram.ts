import Env from '@ioc:Adonis/Core/Env'
// import Application from '@ioc:Adonis/Core/Application'
import Database from '@ioc:Adonis/Lucid/Database'
import loadSettings from 'App/Helpers/loadSettings'
import { Bot } from 'grammy'

interface TelegramDBToken {
  id: number
  user_id: number
  token?: string
}

export default async function () {
  const _token = Env.get('TELEGRAM_BOT', '**********************************************')
  const { telegram_secret_phrase } = await loadSettings(['telegram_secret_phrase'])

  // Create an instance of the `Bot` class and pass your bot token to it.
  const bot = new Bot(_token) // <-- put your bot token between the ""

  // You can now register listeners on your bot object `bot`.
  // grammY will call the listeners when users send messages to your bot.

  console.log('@@TELEGRAM>TS')

  bot.command('stop', async (ctx) => {
    console.log('🚀 ~ file: Telegram.ts:62 ~ bot.command ~ stop:')

    await Database.from('telegram_sessions')
      .where('user_id', (await ctx.getAuthor()).user.id)
      .del()
    ctx.reply('Оповещения отключены. Повторный ввод секретной фразы включит оповещения.')
  })

  // Handle the /start command.
  bot.command('start', async (ctx) => {
    console.log('ctx start: ', ctx)

    ctx.reply('Ввод секретной фразы включит оповещения.')
  })
  // Handle other messages.
  bot.on('message', async (ctx) => {
    if (ctx.message.text == telegram_secret_phrase) {
      const existUser = await Database.from('telegram_sessions').where('user_id', ctx.message.from.id).first()

      if (existUser?.id) {
        ctx.reply('Оповещения уже включены. Команда /stop отключит оповещения.')
        return false
      }

      const nUser = await Database.table('telegram_sessions').insert({
        user_id: ctx.message.from.id,
        token: 'empty' //crypto.randomUUID()
      })


      ctx.reply(`Оповещения "id: ${nUser?.[0] || ''}" включены. Команда /stop отключит оповещения.`)
    }

    // console.log('🚀 ~ file: Telegram.ts:18 ~ bot.on ~ ctx:', (await ctx.getAuthor())?.user?.id)
    // ctx.reply('Got another message!')
  })

  // Now that you specified how to handle messages, you can start your bot.
  // This will connect to the Telegram servers and wait for messages.

  // Start the bot.
  bot.start()
}
