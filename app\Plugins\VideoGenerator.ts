import { exec } from 'child_process'
// import { checkFileExists } from './PicGenerator'

// checkFileExists
interface Image {
  path: string
  duration: number
}

interface ImageParams {
  path: string
  duration: number
}

interface ImageParams {
  path: string
  duration: number
}

// function old_generateFFmpegCommand(images: string[], output: string): string {
//   console.log("🚀 ~ generateFFmpegCommand ~ images:", images)
//   let command = ''

//   if (images.length == 2) {
//     command = `ffmpeg -loop 1 -t 2 -i ${images[0]} -loop 1 -t 3 -i ${images[1]} -filter_complex "[0:v]scale=980:600,setsar=1,zoompan=z='zoom+0.004':x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':d=3*25:s=980x600:fps=25[im1]; [1:v]scale=980:600,setsar=1,zoompan=z='zoom+0.004':x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':d=3*25:s=980x600:fps=25[im2]; [im1][im2]xfade=transition=circleopen:duration=1:offset=2[out]" -t 5 -c:v libx264 -pix_fmt yuv420p -map "[out]" ${output} -y`
//   } else if (images.length == 3) {
//     command = `ffmpeg -loop 1 -t 3 -framerate 60 -i ${images[0]} -loop 1 -t 3 -framerate 60 -i ${images[1]} -loop 1 -t 3 -framerate 60 -i ${images[3]} -filter_complex "[0]scale=980:600,zoompan=z='zoom+0.004':x=iw/2-(iw/zoom/2):y=ih/2-(ih/zoom/2):d=3*60:s=980x600:fps=25[s0];[1]scale=980:600,zoompan=z='zoom+0.004':x=iw/2-(iw/zoom/2):y=ih/2-(ih/zoom/2):d=3*60:s=980x600:fps=25[s1];[2]scale=980:600,zoompan=z='zoom+0.004':x=iw/2-(iw/zoom/2):y=ih/2-(ih/zoom/2):d=3*60:s=980x600:fps=25[s2];[s0][s1]xfade=transition=circleopen:duration=1:offset=2[f0]; [f0][s2]xfade=transition=circleopen:duration=1:offset=4" -t 7 -c:v libx264 -pix_fmt yuv420p ${output} -y`
//   } else {
//     throw new Error('images.length must be 2 or 3')
//   }

//   return command
// }

// function generateFFmpegCommand(images: string[], output: string): string {
//   // images.reverse()
//   console.log('🚀 ~ generateFFmpegCommand ~ images:', images)
//   let command = ''

//   if (images.length == 2) {
//     command = `ffmpeg -loop 1 -t 2 -i ${images[0]} -loop 1 -t 3 -i ${images[1]} -filter_complex "[0:v]scale=980:600,setsar=1,zoompan=z='zoom+0.004':x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':d=2*25:s=980x600:fps=25[im1]; [1:v]scale=980:600,setsar=1,zoompan=z='zoom+0.004':x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':d=3*25:s=980x600:fps=25[im2]; [im1][im2]overlay=x=0:y=0:enable='between(t,2,5)'[out]" -t 5 -c:v libx264 -pix_fmt yuv420p -map "[out]" ${output} -y`
//   } else if (images.length == 3) {
//     command = `ffmpeg -loop 1 -t 3 -i ${images[0]} -loop 1 -t 3 -i ${images[1]} -loop 1 -t 3 -i ${images[2]} -filter_complex "[0:v]scale=980:600,setsar=1,zoompan=z='zoom+0.004':x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':d=3*25:s=980x600:fps=25[im1]; [1:v]scale=980:600,setsar=1,zoompan=z='zoom+0.004':x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':d=3*25:s=980x600:fps=25[im2]; [2:v]scale=980:600,setsar=1,zoompan=z='zoom+0.004':x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':d=3*25:s=980x600:fps=25[im3]; [im1][im2]overlay=x=0:y=0:enable='between(t,0,3)'[temp]; [temp][im3]overlay=x=0:y=0:enable='between(t,3,6)'[out]" -t 9 -c:v libx264 -pix_fmt yuv420p -map "[out]" ${output} -y`
//   } else {
//     throw new Error('images.length must be 2 or 3')
//   }

//   return command
// }

function generateFFmpegCommand(images: string[], output: string): string {
  console.log('🚀 ~ generateFFmpegCommand ~ images:', images)
  let command = ''

  if (images.length == 2) {
    command = `ffmpeg -loop 1 -t 1.5 -i ${images[0]} -loop 1 -t 1.5 -i ${images[1]} -filter_complex "[0:v]scale=980:600,setsar=1,zoompan=z='zoom+0.004':x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':d=1.5*25:s=980x600:fps=25[im1]; [1:v]scale=980:600,setsar=1,zoompan=z='zoom+0.004':x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':d=1.5*25:s=980x600:fps=25[im2]; [im1][im2]overlay=x=0:y=0:enable='between(t,1.5,3)'[out]" -t 3 -c:v libx264 -pix_fmt yuv420p -map "[out]" ${output} -y`
  } else if (images.length == 3) {
    command = `ffmpeg -loop 1 -t 1.5 -i ${images[0]} -loop 1 -t 1.5 -i ${images[1]} -loop 1 -t 1.5 -i ${images[2]} -filter_complex "[0:v]scale=980:600,setsar=1,zoompan=z='zoom+0.004':x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':d=1.5*25:s=980x600:fps=25[im1]; [1:v]scale=980:600,setsar=1,zoompan=z='zoom+0.004':x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':d=1.5*25:s=980x600:fps=25[im2]; [2:v]scale=980:600,setsar=1,zoompan=z='zoom+0.004':x='iw/2-(iw/zoom/2)':y='ih/2-(ih/zoom/2)':d=1.5*25:s=980x600:fps=25[im3]; [im1][im2]overlay=x=0:y=0:enable='between(t,0,1.5)'[temp]; [temp][im3]overlay=x=0:y=0:enable='between(t,1.5,3)'[out]" -t 4.5 -c:v libx264 -pix_fmt yuv420p -map "[out]" ${output} -y`
  } else {
    throw new Error('images.length must be 2 or 3')
  }

  return command
}

export async function generateVideo(images: string[], outputPath: string) {
  const command = generateFFmpegCommand(images, outputPath)

  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        reject(error)
        return
      }

      resolve(stdout)
    })
  })
}

// const images = [
//   //f
//   '/home/<USER>/testdir/rti/H-05024.jpg',
//   '/home/<USER>/testdir/rti/g_a_C883.jpg',
//   '/home/<USER>/testdir/rti/g_a_H-05024.jpg'
// ]

// generateVideo(images, 'output.mp4')
//   .then((output) => {
//     console.log('Video generated', output)
//   })
//   .catch((err) => {
//     console.error('Error generating video', err)
//   })
