import Category from "App/Models/Category"
import Product from "App/Models/Product"

const convert = require('xml-js')



export class YandexTurbo {

    catalog: Array<Product>
    categories: Array<Category>
    siteurl: string
    limit: number
    page: number

    constructor() {
        this.siteurl = 'https://mirsalnikov.ru'
        this.categories = []
    }


    rfc3339(d) {

        function pad(n) {
            return n < 10 ? "0" + n : n;
        }

        function timezoneOffset(offset) {
            var sign;
            if (offset === 0) {
                return "Z";
            }
            sign = (offset > 0) ? "-" : "+";
            offset = Math.abs(offset);
            return sign + pad(Math.floor(offset / 60)) + ":" + pad(offset % 60);
        }

        return d.getFullYear() + "-" +
            pad(d.getMonth() + 1) + "-" +
            pad(d.getDate()) + "T" +
            pad(d.getHours()) + ":" +
            pad(d.getMinutes()) + ":" +
            pad(d.getSeconds()) +
            timezoneOffset(d.getTimezoneOffset());
    }

    async loadProducts(page, limit) {

        const activeCategories = await Category.query().select('cat_id').where('cat_active', 1)
        
        let _catalog = await Product
                                    .query()
                                    .select('*')
                                    .where(query => {
                                        query.where('prod_count', '>', 0)
                                        query.orWhere('prod_group_count', '>', 0)
                                    })
                                    .andWhere('prod_price', '>', 0)
                                    .andWhereIn('prod_cat', activeCategories.map(cat => cat.cat_id))
                                    .paginate(page, limit)

        this.catalog = _catalog.toJSON().data
    }

    async loadCategories(page, limit) {
        if (page == 1) {
            this.categories = await Category.query().select('*').where('cat_active', 1)
        }
    }

    async makeYml(page = 1, limit = 30000) {
        const today = new Date()
        //const fulldate = today.getFullYear() + '-' + today.getMonth() + '-' + today.getDate() + ' ' + today.getHours() + ':' + today.getMinutes()

        await this.loadProducts(page, limit)
        await this.loadCategories(page, limit)

        let category = this.categories.map(item => {
            return {
                    '_text': item.cat_title,
                    '_attributes': {
                        id: item.cat_id
                    }
            }
        })

        //console.log('_categories: ', categories);
        

        let offer = this.catalog.map(item => {
            return {
                //offer: {
                    '_attributes': {
                        id: item.prod_id,
                        type: 'vendor.model',
                        available: 'true'
                    },
                    name: item.prod_purpose,
                    url: `${this.siteurl}/catalog/product/${item.prod_id}`,
                    price: item.prod_price,
                    currencyId: 'RUR',
                    categoryId: {
                        '_text': item.prod_cat,
                        '_attributes': {
                            type: 'Own'
                        }
                    },
                    picture: 'https://mirsalnikov.ru/data/rti/' + item.prod_img + 'jpg',
                    delivery: true,
                    local_delivery_cost: '200',
                    typePrefix: item.prod_purpose,
                    vendor: item.prod_manuf,
                    vendorCode: item.prod_analogsku,
                    model: item.prod_model || item.prod_uses,
                    description: `${item.prod_purpose} ${item.prod_uses} - ${item.prod_sku} (${item.prod_analogsku}), ${item.prod_manuf}, ${item.prod_size}`,
                    manufacturer_warranty: true,
                    country_of_origin: 'Тайвань'
                //}
            }
        })

        const json = {
            _declaration: {
                _attributes: {
                    "version": "1.0", "encoding": "utf-8"
                }
            },
            'yml_catalog': {
                _attributes: {
                    date: this.rfc3339(today)
                },
                shop: {
                    name: 'Мир Сальников',
                    company: 'ООО «РТИ-Балтика»',
                    url: this.siteurl,
                    currencies: {
                        currency: {
                            '_attributes': {
                                id: 'RUR',
                                rate: '1',
                                plus: '0'
                            }
                        }
                    },
                    categories: {
                        category
                    },
                    offers: {
                        offer
                    }
                }
            }
        }

        const options = { compact: true, ignoreComment: true, spaces: 4 }

        let result = convert.json2xml(json, options)

        return result
    }
}