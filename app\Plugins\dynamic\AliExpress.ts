import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext';
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'

import { YandexTurbo } from 'App/Plugins/YandexTurbo'
const convert = require('xml-js')

class AliexpressProductYml extends YandexTurbo {
    constructor() { super() }

    async makeYml(page = 1, limit = 30000, shippingprice = 200) {

        const nameRegex = /(?=\w*)_\d{1}/gm

        await this.loadProducts(page, limit)
        await this.loadCategories(page, limit)


        let category = this.categories.map(item => {
            return {
                    '_text': item.cat_title,
                    '_attributes': {
                        id: item.cat_id,
                        parentId: item.cat_rootcat
                    }
            }
        })

        let offer = this.catalog.map(item => {
            let prodSizes = String(item.prod_size).split('*')
            return {
                    '_attributes': {
                        id: item.prod_id,
                    },
                    
                    price: Number(item.prod_price) + shippingprice,
                    // discount_price: item.prod_price,

                    picture_0: 'https://mirsalnikov.ru/data/rti/' + item.prod_img + '.jpg',
                    // picture_1: 'https://mirsalnikov.ru/data/rti/' + item.prod_type + '.jpg',

                    name: `${item.prod_purpose}, ${item.prod_uses} - ${item.prod_sku}, ${item.prod_size}`,
/*                     description: `<![CDATA[ 
                        <ul>
                            <li>Назначение: <b>${item.prod_purpose || '--'}</b></li>
                            <li>Артикул: <b>${item.prod_sku || '--'}</b></li>
                            <li>Код: <b>${item.prod_analogsku || '--'}</b></li>
                            <li>Аналоги: <b>${item.prod_analogs || '--'}</b></li>
                            <li>Размер: <b>${item.prod_size || '--'}</b></li>
                            <li>Применение: <b>${item.prod_uses || '--'}</b></li>
                            <li>Год: <b>${item.prod_year || '--'}</b></li>                       
                            <li>Модель: <b>${item.prod_model || '--'}</b></li>
                        </ul>

                        <ul>
                            <li>d вн.: ${prodSizes[0]}</li>
                            <li>d нар.: ${prodSizes[1]}</li>
                            <li>высота.: ${prodSizes[2]}</li>
                        </ul>
                        <p>${item.prod_note}</p>
                    ]]>`, */

                    description: `Назначение: ${item.prod_purpose || '--'} 
                    | Артикул: ${item.prod_sku || '--'} 
                    | Код: ${item.prod_analogsku || '--'} 
                    | Аналоги: ${item.prod_analogs || '--'} 
                    | Размер: ${item.prod_size || '--'} 
                    | Применение: ${item.prod_uses || '--'} 
                    | Год: ${item.prod_year || '--'} 
                    | Модель: ${item.prod_model || '--'} 
                    | d вн.: ${prodSizes[0] || ''} / d нар.: ${prodSizes[1] || ''} / высота.: ${prodSizes[2] || ''} 
                    | Примечание: ${item.prod_note || '--'}

                    ${!item.prod_rk ? '!!!НЕ КОМПЛЕКТ. ЦЕНА ЗА 1ШТ!!!' : ''} 
                    На фото товар с двух сторон.
                    `,

                    length: item.size_in || 1,
                    width: item.size_out || 1,
                    height: item.size_h || 1,
                    weight: item.prod_weight || 20,
                    
                    categoryId: item.prod_cat,
                    quantity: item.prod_group_count || item.prod_count,

                    param_0: {
                        '_text': prodSizes[0] || 0,
                        '_attributes': {
                            name: 'd вн.',
                            unit: 'мм'
                        }
                    },
                    param_1: {
                        '_text': prodSizes[1] || 0,
                        '_attributes': {
                            name: 'd нар.',
                            unit: 'мм'
                        }
                    },
                    param_2: {
                        '_text': prodSizes[2] || 0,
                        '_attributes': {
                            name: 'высота',
                            unit: 'мм'
                        }
                    },
                    param_3: {
                        '_text': item.prod_sku,
                        '_attributes': {
                            name: 'Артикул'
                        }
                    },
                    param_4: {
                        '_text': item.prod_analogsku,
                        '_attributes': {
                            name: 'Код'
                        }
                    },
                    param_5: {
                        '_text': item.prod_analogs,
                        '_attributes': {
                            name: 'Аналоги'
                        }
                    },
                    param_6: {
                        '_text': item.prod_manuf,
                        '_attributes': {
                            name: 'Бренд'
                        }
                    },
                    param_7: {
                        '_text': 8487909000,
                        '_attributes': {
                            name: 'ТН_ВЭД'
                        }
                    },
                    param_8: {
                        '_text': '8487909000',
                        '_attributes': {
                            name: 'ТН_ВЭД'
                        }
                    },
                    param_9: {
                        '_text': item.prod_size,
                        '_attributes': {
                            name: 'Размер'
                        }
                    }
                //}
            }
        })

        const json = {
            _declaration: {
                _attributes: {
                    "version": "1.0", "encoding": "utf-8"
                }
            },
            'yml_catalog': {
                categories: {
                    category
                },
                offers: {
                    offer
                }
            }
        }

        const options = { compact: true, ignoreComment: true, spaces: 4 }
        let result = convert.json2xml(json, options).replace(nameRegex, '')
        
        // console.log("🚀 ~ file: AliExpress.ts ~ line 132 ~ AliexpressProductYml ~ result", result)

        return result
    }
}


const AlCB = async ({ request, params, response, auth, session }: HttpContextContract) => {
    
    const { page = 1, limit = 10, shippingprice = 200 } = request.qs()

    const ali = new AliexpressProductYml()
    const productsYml = ali.makeYml(page, limit, shippingprice)

    response.header('Content-type', 'application/xml')
    response.type('application/xml')

    return productsYml
    // return response.send(productsYml)
}

const plugin: dynPluginInterface = {
    httpmethods: ['GET'],
    cb: async (httpCtx: HttpContextContract) => {
        return await AlCB(httpCtx)
    },
    route: '/aliexpress/yml/'
}

export default plugin