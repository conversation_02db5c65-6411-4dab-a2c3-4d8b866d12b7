import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'

import axios from 'axios'

// id:  BMLsThFszXx8JbpudmOoWpB7gVJqWjvv
// pw: 72qMmYKDlmS0oSevUMa7urnyV0Sq6Xr4

const CDEK_LOGIN = 'BMLsThFszXx8JbpudmOoWpB7gVJqWjvv' //'EMscd6r9JnFiQ3bLoyjJY6eM78JrJceI' // Replace with your actual login
const CDEK_PASSWORD = '72qMmYKDlmS0oSevUMa7urnyV0Sq6Xr4' //'PjLZkKBHEiLK3YsjtNrt3TGNG0ahs3kG' // Replace with your actual password
const BASE_URL = 'https://api.cdek.ru/v2' //'https://api.cdek.ru/v2'

let authToken = null

const AlCB = async ({ request, response }: HttpContextContract) => {
  const { action } = request.all()

  const sendValidationError = (res, message) => {
    res.status(400).json({ message })
  }

  const sendResponse = (res, data) => {
    res.status(200).json(data)
  }

  const getAuthToken = async () => {
    // if (authToken) {
    //   return
    // }

    let bodyContent = `grant_type=client_credentials&client_id=${CDEK_LOGIN}&client_secret=${CDEK_PASSWORD}`

    try {
      const response = await axios.post(
        `${BASE_URL}/oauth/token`,
        // {
        //   grant_type: 'client_credentials',
        //   client_id: CDEK_LOGIN,
        //   client_secret: CDEK_PASSWORD
        // },
        bodyContent,
        {
          headers: {
            'Accept': '*/*',
            'Content-Type': 'application/x-www-form-urlencoded'
          }
        }
      )


      authToken = response.data.access_token

      if (!authToken) {
        throw new Error('Server not authorized to CDEK API')
      }
    } catch (error) {
      console.error('Error getting auth token:', error)
      throw new Error('Server not authorized to CDEK API')
    }
  }

  const getOffices = async (data) => {
    return await httpRequest('deliverypoints', data)
  }

  const calculate = async (data) => {
    return await httpRequest('calculator/tarifflist', data, true)
  }

  const httpRequest = async (method, data, useJson = false) => {
    const headers = {
      'Accept': 'application/json',
      'X-App-Name': 'widget_pvz',
      'Authorization': `Bearer ${authToken}`
    }

    let options = {
      method: 'GET',
      url: `${BASE_URL}/${method}`,
      headers: headers
    }

    if (useJson) {
      options.method = 'POST'
      options.data = data
      headers['Content-Type'] = 'application/json'
    } else {
      options.params = data
    }

    try {
      const response = await axios(options)
      return response.data
    } catch (error) {
      console.error('Error in HTTP request:', error)
      throw error
    }
  }

  if (!action) {
    return sendValidationError(response, 'Action is required')
  }

  try {
    await getAuthToken()

    switch (action) {
      case 'offices':
        const officesData = await getOffices(request.all())
        return sendResponse(response, officesData)
      case 'calculate':
        const calculateData = await calculate(request.all())
        return sendResponse(response, calculateData)
      default:
        return sendValidationError(response, 'Unknown action')
    }
  } catch (error) {
    console.error('Error processing request:', error)
    return response.status(500).json({ message: 'Internal Server Error' })
  }
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET','POST'],
  cb: async (httpCtx: HttpContextContract) => {
    return await AlCB(httpCtx)
  },
  route: '/cdek/widget'
}

export default plugin
