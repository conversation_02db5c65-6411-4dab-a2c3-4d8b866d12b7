import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import loadSettings from 'App/Helpers/loadSettings'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import Cache from 'App/Models/Cache'
import Client from 'App/Models/Client'

const { api } = require('zadarma')

const AlCB = async ({ request, params, response, auth, session }: HttpContextContract) => {
  const ip = request.ip()

  const { phones } = request.body()

  if (!phones?.length) {
    return []
  }

  const fCache = await Cache.query()
    .where({
      ckey: 'clientsbyphone',
      hash: JSON.stringify({ phones })
    })
    .andWhere('updated_at', '>=', new Date(Date.now() - 1000 * 60 * 60 * 1))
    .first()

  if (fCache) {
    return fCache.body
  }

  try {
    const data = await Client.query().whereRaw(`REGEXP_REPLACE(client_phone, '[^0-9]+', '') IN (${phones.join()})`).limit(150)

    const cacheRes = await Cache.updateOrCreate(
      {
        ckey: 'clientsbyphone',
        hash: JSON.stringify({ phones })
      },
      {
        body: data,
        ckey: 'clientsbyphone',
        hash: JSON.stringify({ phones })
      }
    )

    return data
  } catch (error) {
    return []
  }
}

const plugin: dynPluginInterface = {
  httpmethods: ['POST'],
  cb: async (httpCtx: HttpContextContract) => {
    return await AlCB(httpCtx)
  },
  route: '/clients/byphones'
}

export default plugin
