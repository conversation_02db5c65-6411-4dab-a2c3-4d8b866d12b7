import { HttpContextContract } from "@ioc:Adonis/Core/HttpContext"
// import loadSettings from "App/Helpers/loadSettings"
import dynPluginInterface from "App/Interfaces/plugins/dynPluginInterface"
import Product from "App/Models/Product"
import User from "App/Models/User"
import { FilePlugin } from "../Files"


const AlCB = async ({ request, params, response, auth, session, }: HttpContextContract) => {
    const ip = request.ip()    
    // const user: User = await auth.use('api').authenticate()
    
    const {target: targetProductID, name, path} = request.qs()

    if (!targetProductID) throw new Error("targetProduct undefined")

    const product = await Product.findOrFail(targetProductID)
    
    //product.prod_images = [...new Set(images.map(image => encodeURIComponent(image.clientName)), ...String(product.prod_images).split(','))].filter(i => i).join()
    
    await FilePlugin.deleteByPath(path)

    product.prod_images = product.prod_images.split(',').filter(i => i != name).join()
    await product.save()

    return product.prod_images.split(',')
}

const plugin: dynPluginInterface = {
    httpmethods: ['GET'],
    cb: async (httpCtx: HttpContextContract) => {
        return await AlCB(httpCtx)
    },
    route: '/files/delete'
}

export default plugin