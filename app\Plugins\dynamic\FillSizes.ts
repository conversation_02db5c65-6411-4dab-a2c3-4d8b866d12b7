import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'

import { fillSizes } from 'App/Helpers/updateProductSizes'

const AlCB = async (httpCtx: HttpContextContract) => {
  const { limit = 200 } = httpCtx.request.qs()

  return await fillSizes(limit)
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET'],
  cb: async (httpCtx: HttpContextContract) => {
    return await AlCB(httpCtx)
  },
  route: '/fillsizes/'
}

export default plugin
