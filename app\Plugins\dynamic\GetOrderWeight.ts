import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'

import Database from '@ioc:Adonis/Lucid/Database'
import Order from 'App/Models/Order'

const AlCB = async (httpCtx: HttpContextContract) => {
  const { id } = httpCtx.request.all()

  const order = await Order.findOrFail(id)

  return await order.orderWeight()
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET'],
  cb: async (httpCtx: HttpContextContract) => {
    return await AlCB(httpCtx)
  },
  route: '/orderweight/'
}

export default plugin
