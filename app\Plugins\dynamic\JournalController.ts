import { HttpContextContract } from "@ioc:Adonis/Core/HttpContext"
import dynPluginInterface from "App/Interfaces/plugins/dynPluginInterface"
import Journal from "App/Models/Journal"

import Lock from "App/Models/Lock"

const CB = async (httpCtx: HttpContextContract) => {
    let { value } = httpCtx.request.params()

    if (value) {
        value = decodeURIComponent(value)
    }

    return await Journal.query()
                            .if(value, query => {
                                query
                                    .orWhere('msg', 'like', `%${value}%`)
                                    .orWhere('entity_id', value)
                                    .orWhere('user_name', 'like', `%${value}`)
                                    .orderBy('id', 'desc')
                            })
                            //.debug(true)
                            .limit(30)
}


const plugin: dynPluginInterface = {
    httpmethods: ['GET'],
    cb: async (httpCtx: HttpContextContract) => {
        try {
            return await CB(httpCtx)
        } catch (error) {
            console.error('dynamic plugin LockController: ', error)
            return httpCtx.response.status(500).send(String(error))
        }
    },
    route: '/journal/search/:value?'
}

export default plugin