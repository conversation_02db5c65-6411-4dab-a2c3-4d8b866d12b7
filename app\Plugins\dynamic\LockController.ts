import { HttpContextContract } from "@ioc:Adonis/Core/HttpContext"
import dynPluginInterface from "App/Interfaces/plugins/dynPluginInterface"
import Lock from "App/Models/Lock"

const CB = async (httpCtx: HttpContextContract) => {
    const { action, entity, entity_id } = httpCtx.request.qs()
    const user = await httpCtx?.auth.use('api').authenticate()
       
    
    return await Lock[action]({
        entity,
        entity_id,
        user_id: user.user_id,
        user_name: user.user_name
    })
}


const plugin: dynPluginInterface = {
    httpmethods: ['GET'],
    cb: async (httpCtx: HttpContextContract) => {
        try {
            return await CB(httpCtx)
        } catch (error) {
            console.error('dynamic plugin LockController: ', error)
            return httpCtx.response.status(500).send(String(error))
        }
    },
    route: '/lock/'
}

export default plugin