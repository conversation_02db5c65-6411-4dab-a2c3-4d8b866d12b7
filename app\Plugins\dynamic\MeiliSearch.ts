import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import { MeiliSearchPlugin } from '../MeiliSearch'

async function testProductIntegrity(meiliSearch: MeiliSearchPlugin) {
  console.log('🧪 Тестирование целостности данных товаров...')

  // Тестовый товар с аналогами
  const testProduct = {
    prod_id: 999999,
    prod_sku: 'TEST123',
    prod_analogsku: 'TEST123',
    prod_analogs: 'ANALOG1, ANALOG2, ANALOG3',
    prod_cat: '1',
    prod_count: 5,
    prod_price: '1000',
    prod_manuf: 'TEST',
    prod_purpose: 'Тестовый товар',
    isVirtual: false
  }

  try {
    // 1. Обновляем товар
    console.log('1️⃣ Обновление товара...')
    const result = await meiliSearch.updateProduct({ product: testProduct })
    console.log('Результат обновления:', result)

    // 2. Ждем немного для индексации
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 3. Проверяем, что товар и его виртуальные копии созданы
    console.log('2️⃣ Проверка созданных документов...')
    const searchResults = await meiliSearch.client.index('products').search('', {
      filter: 'prod_id = 999999',
      limit: 10
    })

    console.log('Найдено документов:', searchResults.hits.length)
    searchResults.hits.forEach(hit => {
      console.log('- ID:', hit.id, 'isVirtual:', hit.isVirtual, 'analogsku:', hit.prod_analogsku)
    })

    // 4. Обновляем товар с другими аналогами
    console.log('3️⃣ Обновление с новыми аналогами...')
    testProduct.prod_analogs = 'NEW_ANALOG1, NEW_ANALOG2'
    await meiliSearch.updateProduct({ product: testProduct })

    // 5. Ждем и проверяем снова
    await new Promise(resolve => setTimeout(resolve, 2000))
    const searchResults2 = await meiliSearch.client.index('products').search('', {
      filter: 'prod_id = 999999',
      limit: 10
    })

    console.log('После обновления найдено документов:', searchResults2.hits.length)
    searchResults2.hits.forEach(hit => {
      console.log('- ID:', hit.id, 'isVirtual:', hit.isVirtual, 'analogsku:', hit.prod_analogsku)
    })

    // 6. Удаляем тестовый товар
    console.log('4️⃣ Удаление товара...')
    await meiliSearch.deleteProduct(999999)

    // 7. Проверяем, что все удалено
    await new Promise(resolve => setTimeout(resolve, 2000))
    const searchResults3 = await meiliSearch.client.index('products').search('', {
      filter: 'prod_id = 999999',
      limit: 10
    })

    console.log('После удаления найдено документов:', searchResults3.hits.length)

    console.log('✅ Тест завершен')

    return {
      success: true,
      steps: [
        { step: 1, result: result, documentsFound: searchResults.hits.length },
        { step: 2, documentsAfterUpdate: searchResults2.hits.length },
        { step: 3, documentsAfterDelete: searchResults3.hits.length }
      ]
    }

  } catch (error) {
    console.error('❌ Ошибка теста:', error)
    return { success: false, error: error.message }
  }
}

const AlCB = async (httpCtx: HttpContextContract) => {
  const { action } = httpCtx.request.qs()
  const meiliSearch = new MeiliSearchPlugin({})


  if (action === 'syncProductsByPrisma') {
    const { products } = httpCtx.request.body()

    return await meiliSearch.syncProductsByPrisma(products)
  }

  if (action === 'syncCategoriesByPrisma') {
    return await meiliSearch.syncCategoriesByPrisma()
  }


  if (action === 'syncColumnsByPrisma') {
    return await meiliSearch.syncColumnsByPrisma()
  }


  if (action === 'syncFiltersByPrisma') {
    return await meiliSearch.syncFiltersByPrisma()
  }

  if (action === 'search') {
    const { query } = httpCtx.request.qs()
    return meiliSearch.productsDB().search(query)
  }


    if (action === 'syncAllByPrisma') {
      return await meiliSearch.syncAllByPrisma()
    }

  if (action === 'testProductIntegrity') {
    return await testProductIntegrity(meiliSearch)
  }
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET'],
  cb: async (httpCtx: HttpContextContract) => {
    return await AlCB(httpCtx)
  },
  route: '/meilisearch/'
}

export default plugin
