import { HttpContextContract } from "@ioc:Adonis/Core/HttpContext"
import dynPluginInterface from "App/Interfaces/plugins/dynPluginInterface"

import { ozonService } from 'App/Services/OzonService'

const AlCB = async (httpCtx: HttpContextContract) => {
   
    const res = await ozonService.syncProducts([532555], 1, true)
    console.log("🚀 ~ AlCB ~ res:", res)
   
    return res
}

const plugin: dynPluginInterface = {
    httpmethods: ['GET'],
    cb: async (httpCtx: HttpContextContract) => {
        return await AlCB(httpCtx)
    },
    route: '/ozon/test'
}

export default plugin