import loadSettings from 'App/Helpers/loadSettings'
import Cart  from 'App/Models/Cart'



export class RumisotaShipping {
    
    //destIndex: number | string

    constructor() {
        
    }

    async calcByPrice({ countryID, idField, cartId }) {
        console.log('countryID', countryID)

    
        const cart = new Cart()
        const settingsColumns = [
            'currency',
            'eu.shipping.basePrice',
            'eu.shipping.step.maxStartOrderSum',
            'eu.shipping.step.shippingPrice',
            'eu.shipping.whosale.start',
            'eu.shipping.whosale.basePrice',
            'eu.shipping.whosale.step.orderSum',
            'eu.shipping.whosale.step.shippingPrice',
            'eu.shipping.step.orderSum',
            'pl.shipping.step.maxStartOrderSum',
            'pl.shipping.step.orderSum',
            'pl.shipping.basePrice',
            'pl.shipping.step.shippingPrice'
        ]

        let cartSum = await cart.getCartSum(idField, cartId)
        let settings = await loadSettings(settingsColumns)
        //console.log('settings', settings)

        //check

        const isLoadedAllColumns = settingsColumns.every(columnKey => Object.keys(settings).includes(columnKey))

        if (!isLoadedAllColumns) {
            console.error('Error settingsColumns::some fields are missing: ', settingsColumns.toString())
            return 911
        }

        let isPL = String(countryID) == '616'
        let crnc = settings.currency[isPL ? 'zl' : 'eur']
        let cartSumEur = (cartSum.defsum / crnc)

        if (isPL) {
            return 1070
            
            // let overSteps = Math.ceil((cartSumEur - settings['pl.shipping.step.maxStartOrderSum']) / settings['pl.shipping.step.orderSum'])
            // return (settings['pl.shipping.basePrice'] + (overSteps * settings['pl.shipping.step.shippingPrice'])) * crnc
        }

        //console.log('cartSumEur', cartSumEur)

        // розница
        if (cartSumEur < settings['eu.shipping.whosale.start']) {
            if (cartSumEur <= settings['eu.shipping.step.maxStartOrderSum']) {
                return (settings['eu.shipping.basePrice'] * crnc)
            } else {
                //console.log('base price: ', settings['eu.shipping.basePrice'])
                //console.log('over base tariff: ', cartSumEur - settings['eu.shipping.step.maxStartOrderSum'])

                let overSteps = Math.ceil((cartSumEur - settings['eu.shipping.step.maxStartOrderSum']) / settings['eu.shipping.step.orderSum'])

                //console.log('overSteps: ', overSteps)
                
                return (settings['eu.shipping.basePrice'] + ( overSteps * settings['eu.shipping.step.shippingPrice'] )) * crnc
            }
        } 
        // опт
        else {
            let overSteps = Math.ceil((cartSumEur - settings['eu.shipping.whosale.start']) / settings['eu.shipping.whosale.step.orderSum'])
            return (settings['eu.shipping.whosale.basePrice'] + (overSteps * settings['eu.shipping.whosale.step.shippingPrice'])) * crnc
        }

    }
    
}