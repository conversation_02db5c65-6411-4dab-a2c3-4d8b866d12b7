import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'

// TODO: move create order method to Order model
import OrdersController from 'App/Controllers/Http/OrdersController'
import Database from '@ioc:Adonis/Lucid/Database'

const AlCB = async (httpCtx: HttpContextContract) => {
  // SELECT sum(qty) FROM `spec_list` LEFT JOIN specs on specs.id = spec_list.spec WHERE spec_list.prod = 'P02121' and specs.active = 1;
  const { oem } = httpCtx.request.all()

  if (!oem) {
    return 0
  }

  const result = await Database.from('spec_list')
    .leftJoin('specs', 'specs.id', 'spec_list.spec')
    .where('spec_list.prod', oem)
    .andWhere('specs.active', 1)
    .sum('qty as total')
    .first()

  return result?.total || 0
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET'],
  cb: async (httpCtx: HttpContextContract) => {
    return await AlCB(httpCtx)
  },
  route: '/specsum/'
}

export default plugin
