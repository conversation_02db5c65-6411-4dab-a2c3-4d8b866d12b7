import { HttpContextContract } from "@ioc:Adonis/Core/HttpContext"
import loadSettings from "App/Helpers/loadSettings"
import dynPluginInterface from "App/Interfaces/plugins/dynPluginInterface"
import { AliexpressOrders } from "App/Plugins/AliexpressOrders"



const cb = async (httpCtx: HttpContextContract) => {
   

    let orderSum = '21000'

    const { 'free_shipping.isactive': freeShippingIsActive, 'free_shipping.startsum': freeShippingStartSum } = await loadSettings(['free_shipping.isactive', 'free_shipping.startsum'])

    const _isFreeShipping = !!Number(freeShippingIsActive) && Number(orderSum) > Number(freeShippingStartSum)

       console.log("🚀 ~ cb ~ _isFreeShipping:", _isFreeShipping)
       
}

const plugin: dynPluginInterface = {
    httpmethods: ['GET'],
    cb: async (httpCtx: HttpContextContract) => {
        return await cb(httpCtx)
    },
    route: '/tt/'
}

export default plugin