import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import loadSettings from 'App/Helpers/loadSettings'
import { SnapshotBodyInterface } from 'App/Interfaces/orders/snapshotBody'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import Order from 'App/Models/Order'
import OrderSnapshot from 'App/Models/OrderSnapshot'

const cb = async (httpCtx: HttpContextContract) => {
  const { action, orderId } = httpCtx.request.qs()

  if (!orderId) {
    throw new Error('orderId is required')
  }

  const lastSnapshot = (await Order.lastSnapshot(orderId)) as OrderSnapshot

  if (!lastSnapshot) {
    throw new Error('Snapshot not found')
  }

  const body = lastSnapshot.body as SnapshotBodyInterface

    if (action == 'follow') {
    const { review_url } = await loadSettings(['review_url'])
    httpCtx.response.redirect(review_url)

    body.qrIsRead = true

    lastSnapshot.body = body
    await Database.from('orders_snapshots')
      .where('ID', lastSnapshot.ID)
      .update({ body: JSON.stringify(body) })

  } else if (action == 'get') {
    return body.qrIsRead || false
  }
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET'],
  cb: async (httpCtx: HttpContextContract) => {
    return await cb(httpCtx)
  },
  route: '/qrcodereview/'
}

export default plugin
