import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import { generateYMLFeed } from '../YandexMarket'

const AlCB = async (httpCtx: HttpContextContract) => {
  const { page = 1, limit } = httpCtx.request.qs()

  httpCtx.response.header('Content-type', 'application/xml')
  httpCtx.response.type('application/xml')

  return await generateYMLFeed({ page: page ? Number(page) : 1, limit: limit ? Number(limit) : undefined })
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET'],
  cb: async (httpCtx: HttpContextContract) => {
    return await AlCB(httpCtx)
  },
  route: '/yandex-market-feed/'
}

export default plugin
