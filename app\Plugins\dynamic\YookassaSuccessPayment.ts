import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import type dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import { Yookassa } from '../Yookassa'

const AlCB = async (httpCtx: HttpContextContract) => {
  // SELECT sum(qty) FROM `spec_list` LEFT JOIN specs on specs.id = spec_list.spec WHERE spec_list.prod = 'P02121' and specs.active = 1;
  const payload = httpCtx.request.all()

  const yookassa = new Yookassa()
  const result = await yookassa.afterSuccess(payload)

  return result
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET'],
  cb: async (httpCtx: HttpContextContract) => {
    return await AlCB(httpCtx)
  },
  route: '/yookassa/successpayment/'
}

export default plugin
