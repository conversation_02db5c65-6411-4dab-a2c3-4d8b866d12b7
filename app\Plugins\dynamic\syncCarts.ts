import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import { $prisma } from 'App/Services/Prisma'

const cbf = async (httpCtx: HttpContextContract) => {
  // const { _cart: cart, 'rti-session': session } = httpCtx.request.cookiesList()

  return 'ok'
}

const plugin: dynPluginInterface = {
  httpmethods: ['POST'],
  cb: async (httpCtx: HttpContextContract) => {
    return await cbf(httpCtx)
  },
  route: '/syncdomains/'
}

export default plugin
