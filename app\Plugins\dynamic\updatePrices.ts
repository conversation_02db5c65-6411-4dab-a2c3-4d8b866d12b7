import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext';
import Database from '@ioc:Adonis/Lucid/Database';
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'

const convert = require('xml-js')


const pluginCB = async ({ request, params, response, auth, session }: HttpContextContract) => {
    let { currency } = request.qs()

    if (!currency || isNaN(currency)) {
        return response.status(500).send({error: '!currency || currency isNaN'})
    }
    

    currency = Number(currency)
    console.log('start updateprices: ', currency)

    // const trx = await Database.transaction()
/*     const res = await Database.knexRawQuery(`update products 
                                    set prod_price = ROUND(CAST(prod_purchasing as double) * CAST(prod_coeff as double) * ?)  
                                    WHERE prod_purchasing != '' and prod_purchasing != 0 and prod_count > 0 and prod_coeff != 0`, [currency])
                            
 */
    
                                    
    // return { success: true, query: res }
}

const plugin: dynPluginInterface = {
    httpmethods: ['GET'],
    cb: async (httpCtx: HttpContextContract) => {
        return await pluginCB(httpCtx)
    },
    route: '/updateprices/'
}

export default plugin