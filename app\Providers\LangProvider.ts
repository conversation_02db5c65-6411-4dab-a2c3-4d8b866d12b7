import { $prisma } from 'App/Services/Prisma'
import { productProvider } from './ProductProvider'
import LangDict, { translatableFields } from 'App/Models/LangDict'
import { products } from '@prisma/client'
import i18next from 'i18next'
import { lang_dict } from '@prisma/client'

type Row = Omit<lang_dict, 'id'> & { id?: number }
function sortObjectByValueLength(obj) {
  if (!obj) {
    return {}
  }
  const sortedObj = {}
  Object.keys(obj)
    .sort((a, b) => obj[b]?.length - obj[a]?.length)
    .forEach((key) => {
      sortedObj[key] = obj[key]
    })
  return sortedObj
}

function sortObjectByKeyLength(obj) {
  if (!obj) {
    return {}
  }
  const sortedKeys = Object.keys(obj).sort((a, b) => b?.length - a?.length)
  const sortedObj = {}
  sortedKeys.forEach((key) => {
    sortedObj[key] = obj[key]
  })
  return sortedObj
}

function haveRUchars(str) {
  const russianRegex = /[\u0400-\u04FF]/
  return russianRegex.test(str)
}
class LangProvider {
  db = $prisma.lang_dict
  constructor() {}

  async getUntranslatedProductsValues() {
    const activeLangs = [
      'en',
      'pl',
      'es',
      'fr',
      'ar'
      // 'it',
      // 'de',
    ]

    // const selectO = {}

    // translatableFields
    //   //format
    //   .filter((i) => i.startsWith('prod_'))
    //   .filter((i) => i !== 'prod_note')
    //   .forEach((i) => (selectO[i] = true))

    // // console.log('🚀 ~ LangProvider ~ getUntranslatedProductsValues ~ selectO:', selectO)

    const products: products[] = await productProvider.db.findMany({
      select: {
        // ...selectO,
        prod_purpose: true,
        prod_uses: true
      }, //,
      distinct: ['prod_purpose', 'prod_uses'],
      orderBy: {
        prod_id: 'desc'
      }
      // take: 10000
    })

    //translate products

    // await LangDict.prodsTranslate(products, 'en)

    const values = new Set<string[]>([])

    products.forEach((product) => {
      translatableFields.forEach((f) => values.add(product[f]))
    })

    const res = [...values].filter((i) => i)

    const vw = new Set<string[]>([])

    const dicts = new Map<
      string,
      | {
          [key: string]: string
        }
      | undefined
    >()

    activeLangs.forEach((langKey) => dicts.set(langKey, sortObjectByKeyLength(i18next.getDataByLanguage(langKey)?.translation)))

    dicts.forEach((dict, langKey) => {
      res.forEach((value) => {
        let _value = value

        dict &&
          Object.keys(dict).forEach((key) => {
            const regex = new RegExp(key, 'gim')
            _value = _value.replace(regex, dict[key])
          })

        if (haveRUchars(_value)) {
          vw.add(value)
        }
      })
    })

    const untransltedRes = [...vw].flat()

    // return untransltedRes

    const lang_dict = await this.db.findMany({
      select: {
        tkey: true
      },
      orderBy: {
        id: 'desc'
      }
    })

    const uniqtkeys = untransltedRes.filter((i) => !lang_dict.find((x) => x.tkey == i))

    const createNewRows = await this.db.createMany({
      data: uniqtkeys.map((i) => ({ tkey: i, backend: true })),
      skipDuplicates: true
    })

    return { uniqtkeys, createNewRows }
    return untransltedRes
  }

  async getFullDict() {
    const res = await this.db.findMany({
      orderBy: {
        id: 'desc'
      }
    })

    return res
  }

  async updateRow(row: Row) {
    const { id, ...data } = row

    const res = await this.db.upsert({
      where: {
        id
      },
      create: data,
      update: data
    })
    // const res = await this.db.updateMany({
    //   where: {
    //     tkey
    //   },
    //   data
    // })

    return res
  }

  async addRow(row: Row) {
    const res = await this.db.create({
      data: row
    })

    return res
  }

  async deleteRow(tkey: string) {
    return await this.db.deleteMany({
      where: {
        tkey
      }
    })
  }
}

export const langProvider = new LangProvider()
