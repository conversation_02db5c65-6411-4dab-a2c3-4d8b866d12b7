import { <PERSON><PERSON> } from 'App/Plugins/Cdek'
import { PochtaRU } from 'App/Plugins/PochtaRU'
import { $prisma } from 'App/Services/Prisma'
import { cartProvider } from './CartProvider'
import Cart from 'App/Models/Cart'
import loadSettings from 'App/Helpers/loadSettings'
import { emailService } from 'App/Services/EmailService'
import { EmailSenderData } from 'App/Interfaces/email/EmailInterfaces'

interface ShippingResult {
  type: string
  price: number
  error?: string
}

interface OrderListParams {
  searchValue?: string
  limit?: number
  page?: number
}

class ServiceProvider {
  ordersDb = $prisma.orders
  orderSnapshotsDb = $prisma.orders_snapshots
  htmlChunksDb = $prisma.html_chunks
  pageDb = $prisma.pages

  constructor() {}

  async getSettings({ params }: { params: string[] }) {
    return await loadSettings(params)
  }

  // МЕТОДЫ ДЛЯ EMAIL РАССЫЛКИ (делегируем к EmailService)

  async getEmailSenderData() {
    return await emailService.getEmailSenderData()
  }

  async updateEmailSenderData(payload: EmailSenderData) {
    return await emailService.updateEmailSenderData(payload)
  }

  async sendBulkEmail(config?: { batchSize?: number; delayBetweenBatches?: number }) {
    return await emailService.sendBulkEmail({
      batchSize: config?.batchSize,
      delayBetweenBatches: config?.delayBetweenBatches
    })
  }

  async retryFailedEmails(campaignId?: string) {
    return await emailService.retryFailedEmails(campaignId)
  }

  async sendScheduledEmail(config?: { batchSize?: number }) {
    return await emailService.sendScheduledEmail({
      batchSize: config?.batchSize
    })
  }

  async getNextScheduledRun(emailData?: EmailSenderData) {
    return await emailService.getNextScheduledRun(emailData)
  }

  getCampaignProgress(campaignId: string) {
    return emailService.getCampaignProgress(campaignId)
  }

  getActiveCampaigns() {
    return emailService.getActiveCampaigns()
  }

  async getCampaignStats(campaignId: string) {
    return await emailService.getCampaignStats(campaignId)
  }

  async calculateShipping({ cartId, type = ['standard'], country = 643, destinationIndex = 101000, city, address }) {
    const pochtaRU = new PochtaRU()
    const cdek = new Cdek()

    const [cartSum, cartWeight] = await Promise.all([cartProvider.getCartSum({ cartId }), cartProvider.getCartWeight({ cartId })])

    const shippingTypes = Array.isArray(type) ? type : [type]

    const results = await Promise.all(
      shippingTypes.map(async (shipType): Promise<ShippingResult> => {
        try {
          if (shipType === 'cdek') {
            const cdekres = await cdek.calculate({
              address,
              city,
              country_code: 'RU',
              postal_code: destinationIndex,
              weight: cartWeight
            })

            return {
              type: shipType,
              price: cdekres?.total_sum || 901
            }
          }

          const price = await (await pochtaRU.calculate(country, destinationIndex, cartSum.sum))[shipType](cartWeight)
          return {
            type: shipType,
            price
          }
        } catch (error) {
          const fallbackPrice = await pochtaRU.manualCalc({
            orderprice: cartSum.sum,
            type: shipType,
            countryID: String(country)
          })

          return {
            type: shipType,
            price: fallbackPrice,
            error: 'Использован резервный метод расчета'
          }
        }
      })
    )

    return results
  }

  async ordersList({ searchValue, limit = 50, page = 1 }: OrderListParams) {
    if (searchValue?.length > 2) {
      limit = 5
    }

    const snaps = await this.orderSnapshotsDb.findMany({
      where: {
        body: searchValue?.length
          ? {
              contains: String(searchValue)
            }
          : {}
      },
      take: limit,
      skip: (page - 1) * limit,
      orderBy: {
        ID: 'desc'
      }
    })

    const ordersData: any[] = []

    const uniqueSnaps = Array.from(new Map(snaps.sort((a, b) => b.ID - a.ID).map((snap) => [snap.orderid, snap])).values())

    uniqueSnaps.forEach((snapshot) => {
      try {
        let body = JSON.parse(snapshot.body)
        ordersData.push(body)
      } catch (error) {}
    })

    return ordersData
  }

  async getPage({ id, locale = 'ru' }) {
    return await this.pageDb.findFirst({
      where: {
        OR: [
          !isNaN(id)
            ? {
                page_id: id
              }
            : {},
          {
            page_key: id
          }
        ],
        page_locale: locale
      }
    })
  }

  async getHtmlChunks() {
    return await this.htmlChunksDb.findMany({
      orderBy: {
        id: 'asc'
      }
    })
  }

  async getHtmlChunk({ id, key }: { id?: number, key?: string }) {
    if (key) {
      return await this.htmlChunksDb.findFirst({
        where: {
          keyname: key
        }
      })
    }
    return await this.htmlChunksDb.findUnique({
      where: {
        id
      }
    })
  }

  async updateHtmlChunk({ id, key, body }: { id?: number, key?: string, body?: string }) {
    if (key) {
      return await this.htmlChunksDb.updateMany({
        where: {
          keyname: key
        },
        data: {
          body
        }
      })
    }
    return await this.htmlChunksDb.update({
      where: {
        id
      },
      data: {
        body
      }
    })
  }



}

export const serviceProvider = new ServiceProvider()
