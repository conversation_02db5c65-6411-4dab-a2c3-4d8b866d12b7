import Imap from 'imap'
import { simpleParser } from 'mailparser'
import axios from 'axios'
import loadSettings from 'App/Helpers/loadSettings'
import ProcessedEmailService from 'App/Plugins/ProcessedEmail'
import EmailAttachmentService from 'App/Services/EmailAttachmentService'
import { IEmailMonitorService, EmailData, EmailAnalysis, KeywordConfig, DEFAULT_KEYWORDS } from 'App/Interfaces/email/EmailProcessingInterfaces'
import Env from '@ioc:Adonis/Core/Env'

/**
 * Простой анализатор ключевых слов
 */
class KeywordAnalyzer {
  static analyze(subject: string, body: string, keywords: KeywordConfig): EmailAnalysis {
    const text = `${subject} ${body}`.toLowerCase()

    console.log(`🔍 Анализируем текст: "${text.substring(0, 200)}${text.length > 200 ? '...' : ''}"`)

    // Проверяем исключающие слова
    if (keywords.exclude) {
      const excludedWords = keywords.exclude.filter((word) => text.includes(word.toLowerCase()))
      if (excludedWords.length > 0) {
        console.log(`❌ Найдены исключающие слова: ${excludedWords.join(', ')}`)
        return { isMatch: false, score: 0, matchedKeywords: [] }
      }
      console.log(`✅ Исключающие слова не найдены`)
    }

    // Ищем обязательные слова
    const matchedRequired = keywords.required.filter((word) => text.includes(word.toLowerCase()))
    console.log(`📋 Найдено обязательных слов: ${matchedRequired.length} из ${keywords.required.length} (${matchedRequired.join(', ')})`)

    // Если нет обязательных слов, письмо не подходит
    if (matchedRequired.length === 0) {
      console.log(`❌ Не найдено ни одного обязательного слова`)
      return { isMatch: false, score: 0, matchedKeywords: [] }
    }

    // Ищем дополнительные слова
    const matchedOptional = keywords.optional.filter((word) => text.includes(word.toLowerCase()))
    console.log(`📋 Найдено дополнительных слов: ${matchedOptional.length} из ${keywords.optional.length} (${matchedOptional.join(', ')})`)

    // Простой расчет оценки
    const score = Math.min(100, matchedRequired.length * 50 + matchedOptional.length * 10)
    console.log(`📊 Итоговая оценка: ${score}`)

    return {
      isMatch: true,
      score,
      matchedKeywords: [...matchedRequired, ...matchedOptional]
    }
  }
}

/**
 * Простой Email Monitor Service
 * Мониторит входящие письма и отправляет подходящие в Mastra Agent
 */
export class EmailMonitorService {
  private isActive = false
  private settings: any = {}

  constructor() {
    this.loadSettings()
  }

  /**
   * Загрузка настроек из БД
   */
  private async loadSettings() {
    try {
      this.settings = await loadSettings([
        'email_monitor_enabled',
        'email_monitor_processed_folder',
        'email_monitor_folder',
        'email_monitor_max_emails_per_check',
        'mastra_base_url',
        'mastra_agent_id',
        'mastra_resource_id'
      ])
    } catch (error) {
      console.error('Ошибка загрузки настроек:', error)
    }
  }

  /**
   * Запуск мониторинга
   */
  async start(): Promise<void> {
    if (this.isActive) {
      throw new Error('Email Monitor уже запущен')
    }

    await this.loadSettings()

    if (!this.settings.email_monitor_enabled) {
      throw new Error('Email Monitor отключен в настройках')
    }

    if (!this.settings.mastra_agent_id) {
      throw new Error('Не настроен ID агента Mastra')
    }

    console.log('🚀 Запуск Email Monitor...')

    this.isActive = true

    // Запускаем периодическую проверку
    await this.processEmails()

    this.isActive = false

  }

  /**
   * Проверка статуса работы
   */
  isRunning(): boolean {
    return this.isActive
  }



  /**
   * Получение списка доступных папок
   */
  async listFolders(): Promise<void> {
    return new Promise((resolve, reject) => {
      const imapConfig: Imap.Config = {
        user: Env.get('IMAP_USERNAME'),
        password: Env.get('IMAP_PASSWORD'),
        host: Env.get('IMAP_HOST'),
        port: Env.get('IMAP_PORT'),
        tls: true,
        authTimeout: 3000
      }

      const imap = new Imap(imapConfig)

      imap.once('ready', () => {
        console.log('📂 Получаем список папок...')

        imap.getBoxes((err, boxes) => {
          if (err) {
            console.error('Ошибка получения списка папок:', err)
            imap.end()
            return reject(err)
          }

          console.log('📁 Доступные папки:')
          this.printBoxes(boxes, '')

          imap.end()
          resolve()
        })
      })

      imap.once('error', (err) => {
        console.error('Ошибка IMAP соединения:', err)
        reject(err)
      })

      imap.connect()
    })
  }

  /**
   * Рекурсивный вывод папок
   */
  private printBoxes(boxes: any, indent: string) {
    for (const [name, box] of Object.entries(boxes)) {
      console.log(`${indent}📁 ${name}`)
      if ((box as any).children) {
        this.printBoxes((box as any).children, indent + '  ')
      }
    }
  }

  /**
   * Основной метод обработки писем
   */
  async processEmails(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        console.log('📧 Проверяем новые письма...')

        // Создаем конфигурацию IMAP
        const imapConfig: Imap.Config = {
          user: Env.get('IMAP_USERNAME'),
          password: Env.get('IMAP_PASSWORD'),
          host: Env.get('IMAP_HOST'),
          port: Env.get('IMAP_PORT'),
          tls: true,
          authTimeout: 3000
        }

        const imap = new Imap(imapConfig)

        imap.once('ready', () => {
          // Используем папку из настроек или INBOX по умолчанию
          const folderToCheck = this.settings.email_monitor_folder || 'INBOX'
          console.log(`📂 Открываем папку: ${folderToCheck}`)

          imap.openBox(folderToCheck, false, (err) => {
            if (err) {
              console.error(`Ошибка открытия папки ${folderToCheck}:`, err)
              imap.end()
              return reject(err)
            }

            // Ищем непрочитанные письма (ограничиваем поиск последними днями для производительности)
            const daysBack = 3 // Ищем письма за последние 30 дней
            const searchDate = new Date()
            searchDate.setDate(searchDate.getDate() - daysBack)
            const searchDateStr = searchDate.toISOString().split('T')[0].replace(/-/g, '-')

            // Используем комбинацию UNSEEN + SINCE для ограничения результатов на сервере
            imap.search(['UNSEEN', ['SINCE', searchDateStr]], (err, results) => {
              if (err) {
                console.error('Ошибка поиска писем:', err)
                imap.end()
                return reject(err)
              }

              if (!results || results.length === 0) {
                console.log('📬 Новых писем не найдено')
                imap.end()
                return resolve()
              }

              console.log(`📬 Найдено ${results.length} новых писем`)

              // Ограничиваем количество писем для обработки (берем последние)
              const maxEmails = this.settings.email_monitor_max_emails_per_check || 30
              const limitedResults = results.slice(-maxEmails)

              if (limitedResults.length < results.length) {
                console.log(`⚠️ Ограничено до ${limitedResults.length} писем из ${results.length} найденных`)
              }

              const f = imap.fetch(limitedResults, { bodies: '' })
              const emailsToMove: { uid: number; folder: string }[] = []
              const parsedEmails: { parsed: any; uid?: number }[] = []
              let messagesProcessed = 0
              const totalMessages = limitedResults.length

              f.on('message', (msg) => {
                let messageUID: number | undefined

                msg.once('attributes', (attrs) => {
                  messageUID = attrs.uid
                })

                msg.on('body', (stream) => {
                  simpleParser(stream, async (err, parsed) => {
                    if (err) {
                      console.error('Ошибка парсинга письма:', err)
                    } else {
                      // Сохраняем распарсенное письмо для последующей обработки
                      parsedEmails.push({ parsed, uid: messageUID })
                    }

                    messagesProcessed++

                    // Если все письма обработаны, начинаем их обработку
                    if (messagesProcessed === totalMessages) {
                      console.log('✅ Получение писем завершено')
                      console.log(`📝 Начинаем последовательную обработку ${parsedEmails.length} писем...`)

                      // Обрабатываем письма ПОСЛЕДОВАТЕЛЬНО, одно за другим
                      await processAllEmails()
                    }
                  })
                })
              })

              // Выносим обработку писем в отдельную функцию
              const processAllEmails = async () => {
                try {
                  for (let i = 0; i < parsedEmails.length; i++) {
                    const { parsed, uid } = parsedEmails[i]
                    console.log(`📧 Обрабатываем письмо ${i + 1}/${parsedEmails.length}...`)

                    try {
                      const result = await this.processMessage(parsed, uid)
                      if (result.shouldMove && uid && result.targetFolder) {
                        emailsToMove.push({ uid, folder: result.targetFolder })
                      }

                      // Небольшая пауза между письмами, чтобы не перегружать Mastra
                      if (i < parsedEmails.length - 1) {
                        await new Promise((resolve) => setTimeout(resolve, 1000)) // 1 секунда пауза
                      }
                    } catch (error) {
                      console.error(`❌ Ошибка обработки письма ${i + 1}:`, error)
                    }
                  }

                  console.log('✅ Обработка всех писем завершена')

                  // Перемещаем обработанные письма
                  if (emailsToMove.length > 0) {
                    try {
                      console.log('пропускаем перенос сообщений...')
                      // await this.moveProcessedEmails(imap, emailsToMove)
                    } catch (error) {
                      console.error('Ошибка перемещения писем:', error)
                    }
                  }

                  imap.end()
                  resolve()
                } catch (error) {
                  console.error('Ошибка при обработке писем:', error)
                  imap.end()
                  reject(error)
                }
              }

              f.once('error', (ex) => {
                console.error('Ошибка получения писем:', ex)
                imap.end()
                reject(ex)
              })

              f.once('end', async () => {
                // Если все письма уже обработаны (синхронный случай), запускаем обработку
                if (messagesProcessed === totalMessages) {
                  await processAllEmails()
                }
              })
            })
          })
        })

        imap.once('error', (err) => {
          console.error('Ошибка IMAP соединения:', err)
          reject(err)
        })

        imap.once('end', () => {
          console.log('IMAP соединение закрыто')
        })

        imap.connect()
      } catch (ex) {
        console.error('Ошибка при проверке писем:', ex)
        reject(ex)
      }
    })
  }

  /**
   * Перемещение обработанных писем в соответствующие папки
   */
  private async moveProcessedEmails(imap: Imap, emailsToMove: { uid: number; folder: string }[]): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Группируем письма по папкам назначения
        const folderGroups = emailsToMove.reduce((groups, email) => {
          if (!groups[email.folder]) {
            groups[email.folder] = []
          }
          groups[email.folder].push(email.uid)
          return groups
        }, {} as Record<string, number[]>)

        // Перемещаем письма по группам
        const movePromises = Object.entries(folderGroups).map(([folder, uids]) => {
          return new Promise<void>((resolveMove, rejectMove) => {
            imap.move(uids, folder, (err) => {
              if (err) {
                console.error(`Ошибка перемещения писем в ${folder}:`, err)
                rejectMove(err)
              } else {
                console.log(`📁 Перемещено ${uids.length} писем в папку ${folder}`)
                resolveMove()
              }
            })
          })
        })

        Promise.all(movePromises)
          .then(() => resolve())
          .catch((error) => reject(error))
      } catch (error) {
        console.error('Ошибка при перемещении писем:', error)
        reject(error)
      }
    })
  }

  /**
   * Генерация уникального идентификатора письма
   */
  private generateUniqueMessageId(parsedEmail: any, uid?: number): string {
    // Создаем хеш на основе содержимого письма для уникальности
    const crypto = require('crypto')

    // Извлекаем ключевые данные письма
    const fromEmail = this.extractFromEmail(parsedEmail)
    const subject = parsedEmail.subject || ''
    const dateReceived = parsedEmail.date ? new Date(parsedEmail.date).getTime() : Date.now()
    const bodyPreview = (parsedEmail.text || parsedEmail.html || '').substring(0, 500)

    // Создаем строку для хеширования
    const contentString = `${fromEmail}|${subject}|${dateReceived}|${bodyPreview}`
    const contentHash = crypto.createHash('sha256').update(contentString).digest('hex').substring(0, 16)

    // Если есть IMAP UID - используем его как основу (UID уникален в рамках папки)
    if (uid) {
      return `uid-${uid}-${contentHash}`
    }

    // Если есть оригинальный Message-ID - используем его с хешем для дополнительной уникальности
    if (parsedEmail.messageId) {
      const cleanMessageId = parsedEmail.messageId.replace(/[<>]/g, '').substring(0, 50)
      return `msg-${cleanMessageId}-${contentHash}`
    }

    // Fallback - timestamp + хеш содержимого
    return `fallback-${Date.now()}-${contentHash}`
  }

  /**
   * Извлечение email адреса отправителя
   */
  private extractFromEmail(parsedEmail: any): string {
    if (parsedEmail.from?.value && Array.isArray(parsedEmail.from.value) && parsedEmail.from.value.length > 0) {
      return parsedEmail.from.value[0]?.address || ''
    } else if (Array.isArray(parsedEmail.from) && parsedEmail.from.length > 0) {
      return parsedEmail.from[0]?.address || parsedEmail.from[0]?.value || ''
    } else if (parsedEmail.from?.address) {
      return parsedEmail.from.address
    } else if (parsedEmail.from?.value) {
      return parsedEmail.from.value
    } else if (typeof parsedEmail.from === 'string') {
      return parsedEmail.from
    }
    return ''
  }

  /**
   * Обработка одного письма
   */
  private async processMessage(parsedEmail: any, uid?: number): Promise<{ shouldMove: boolean; targetFolder?: string }> {
    let tempDirToCleanup = '' // Объявляем в начале для доступа в finally

    try {
      // Генерируем надежный уникальный идентификатор
      const messageId = this.generateUniqueMessageId(parsedEmail, uid)
      // Используем уже созданный метод для извлечения email
      const fromEmail = this.extractFromEmail(parsedEmail)

      const subject = parsedEmail.subject || ''
      let body = parsedEmail.text || parsedEmail.html || ''

      console.log(`📧 Обрабатываем письмо: ${subject} от ${fromEmail}`)

      // Обрабатываем вложения, если они есть
      let attachmentInfo = ''
      if (parsedEmail.attachments && parsedEmail.attachments.length > 0) {
        console.log(`📎 Найдено вложений: ${parsedEmail.attachments.length}`)
        try {
          const attachmentResult = await EmailAttachmentService.processAttachments(parsedEmail)
          tempDirToCleanup = attachmentResult.tempDir

          if (attachmentResult.totalTextContent) {
            body += attachmentResult.totalTextContent
            console.log(`📄 Извлечен текст из вложений (${attachmentResult.totalTextContent.length} символов)`)
          }

          // Формируем информацию о вложениях для логов
          attachmentInfo = attachmentResult.attachments.map(att =>
            `${att.filename}${att.error ? ` (ошибка: ${att.error})` : ' ✓'}`
          ).join(', ')

        } catch (error) {
          console.error('❌ Ошибка обработки вложений:', error)
          attachmentInfo = 'Ошибка обработки вложений'
        }
      }

      // Проверяем, не обрабатывали ли мы уже это письмо
      if (await ProcessedEmailService.isProcessed(messageId)) {
        console.log(`⏭️ Письмо уже обработано: ${messageId}`)
        return { shouldMove: false }
      }

      // ВРЕМЕННО ОТКЛЮЧЕНЫ критерии поиска по ключевым словам
      console.log(`📤 Отправляем ВСЕ письма в Mastra (критерии отключены): ${subject}`)
      if (attachmentInfo) {
        console.log(`📎 Вложения: ${attachmentInfo}`)
      }

      // Отправляем в Mastra Agent с messageId как threadId для консистентности
      const mastra_messageId = fromEmail ? `${fromEmail}-${subject}` : messageId
      const sent = await this.sendToMastraAgent(fromEmail, subject, body, mastra_messageId)

      if (sent) {
        // Отмечаем как обработанное ТОЛЬКО после успешной отправки в Mastra
        await ProcessedEmailService.markAsProcessed(messageId, fromEmail, subject, true)
        console.log(`✅ Письмо успешно отправлено в Mastra и отмечено как обработанное: ${subject}`)

        // Перемещаем в папку обработанных
        return {
          shouldMove: !!this.settings.email_monitor_processed_folder,
          targetFolder: this.settings.email_monitor_processed_folder
        }
      } else {
        console.log(`❌ Ошибка отправки в Mastra: ${subject}. Письмо НЕ отмечено как обработанное`)
        // НЕ отмечаем как обработанное при ошибке - письмо будет обработано повторно
        return { shouldMove: false }
      }
    } catch (error) {
      console.error('Ошибка обработки письма:', error)
      return { shouldMove: false }
    } finally {
      // Всегда очищаем временные файлы вложений
      if (tempDirToCleanup) {
        await EmailAttachmentService.cleanupTempDir(tempDirToCleanup)
      }
    }
  }

  /**
   * Отправка письма в Mastra Agent с retry логикой
   */
  private async sendToMastraAgent(from: string, subject: string, body: string, messageId: string): Promise<boolean> {
    const maxRetries = 3
    let lastError: any

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`📤 Попытка ${attempt}/${maxRetries} отправки в Mastra...`)

        const message = `Получено новое коммерческое письмо:

От: ${from}
Тема: ${subject}

Содержание письма:
${body.substring(0, 1000)}${body.length > 1000 ? '...' : ''}

Пожалуйста, проанализируй это письмо и подготовь соответствующий ответ.`

        // Отправляем запрос в Mastra Agent через STREAM endpoint для лучшей обработки multi-step операций
        const response = await axios.post(
          `${this.settings.mastra_base_url}/api/agents/${this.settings.mastra_agent_id}/stream`,
          {
            messages: [
              {
                role: 'user',
                content: message
              }
            ],
            runId: `email-${messageId}`, // Используем messageId для консистентности
            threadId: messageId, // Используем messageId как threadId для связи с БД
            maxRetries: 2,
            maxSteps: 10, // Увеличиваем количество шагов для сложных операций с инструментами
            temperature: 0.7,
            resourceId: this.settings.mastra_resource_id // ОБЯЗАТЕЛЬНО при использовании threadId!
          },
          {
            headers: {
              'Content-Type': 'application/json',
              'Accept': '*/*'
            },
            timeout: 180000 // Увеличиваем timeout до 3 минут для сложных операций
          }
        )

        // Для stream endpoint нужно дождаться полного завершения потока
        if (response.status === 200) {
          console.log(`✅ Попытка ${attempt}: Stream успешно инициирован`)

          // Для stream API считаем успехом инициацию потока
          // Mastra будет обрабатывать письмо асинхронно
          return true
        } else {
          throw new Error(`Неожиданный статус ответа: ${response.status}`)
        }
      } catch (error) {
        lastError = error
        console.error(`❌ Попытка ${attempt}/${maxRetries} неудачна:`, error.message)

        // Если это не последняя попытка, ждем перед повтором
        if (attempt < maxRetries) {
          const delay = attempt * 2000 // Увеличиваем задержку с каждой попыткой
          console.log(`⏳ Ожидание ${delay}ms перед следующей попыткой...`)
          await new Promise((resolve) => setTimeout(resolve, delay))
        }
      }
    }

    // Все попытки неудачны
    console.error('❌ Все попытки отправки в Mastra неудачны. Последняя ошибка:', lastError)
    return false
  }
}

// Экспортируем singleton instance
// export const emailMonitorService = new EmailMonitorService()


