export declare type CreateRouterParams = {
    router: typeof router;
    publicProcedure: typeof publicProcedure;
    authedProcedure: typeof authedProcedure;
};
export declare const createContext: (ctx: HttpContextContract) => Promise<HttpContextContract>;
export declare type Context = Awaited<ReturnType<typeof createContext>>;
export declare const t: {
    _config: import("@trpc/server/dist/unstable-core-do-not-import").RootConfig<{
        ctx: any;
        meta: object;
        errorShape: never;
        transformer: import("@trpc/server/dist/unstable-core-do-not-import").DataTransformerOptions;
    }>;
    procedure: import("@trpc/server/dist/unstable-core-do-not-import").ProcedureBuilder<any, object, object, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker>;
    middleware: <$ContextOverrides>(fn: import("@trpc/server/dist/unstable-core-do-not-import").MiddlewareFunction<any, object, object, $ContextOverrides, unknown>) => import("@trpc/server/dist/unstable-core-do-not-import").MiddlewareBuilder<any, object, $ContextOverrides, unknown>;
    router: <TProcRouterRecord extends import("@trpc/server").TRPCProcedureRouterRecord>(procedures: TProcRouterRecord) => import("@trpc/server/dist/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/dist/unstable-core-do-not-import").RootConfig<{
        ctx: any;
        meta: object;
        errorShape: never;
        transformer: import("@trpc/server/dist/unstable-core-do-not-import").DataTransformerOptions;
    }>, TProcRouterRecord>;
    mergeRouters: typeof import("@trpc/server/dist/unstable-core-do-not-import").mergeRouters;
    createCallerFactory: <TRouter extends import("@trpc/server/dist/unstable-core-do-not-import").Router<import("@trpc/server/dist/unstable-core-do-not-import").AnyRouterDef<import("@trpc/server/dist/unstable-core-do-not-import").RootConfig<{
        ctx: any;
        meta: object;
        errorShape: never;
        transformer: import("@trpc/server/dist/unstable-core-do-not-import").DataTransformerOptions;
    }>>>>(router: TRouter) => import("@trpc/server/dist/unstable-core-do-not-import").RouterCaller<TRouter["_def"]>;
};
export declare const middleware: <$ContextOverrides>(fn: import("@trpc/server/dist/unstable-core-do-not-import").MiddlewareFunction<any, object, object, $ContextOverrides, unknown>) => import("@trpc/server/dist/unstable-core-do-not-import").MiddlewareBuilder<any, object, $ContextOverrides, unknown>;
export declare const mergeRouters: typeof import("@trpc/server/dist/unstable-core-do-not-import").mergeRouters;
export declare const router: <TProcRouterRecord extends import("@trpc/server").TRPCProcedureRouterRecord>(procedures: TProcRouterRecord) => import("@trpc/server/dist/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/dist/unstable-core-do-not-import").RootConfig<{
    ctx: any;
    meta: object;
    errorShape: never;
    transformer: import("@trpc/server/dist/unstable-core-do-not-import").DataTransformerOptions;
}>, TProcRouterRecord>;
export declare const publicProcedure: import("@trpc/server/dist/unstable-core-do-not-import").ProcedureBuilder<any, object, object, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker>;
export declare const authedProcedure: import("@trpc/server/dist/unstable-core-do-not-import").ProcedureBuilder<any, object, {
    auth: AuthContract;
    sessionUser: any;
}, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker, typeof import("@trpc/server/dist/unstable-core-do-not-import").unsetMarker>;
export declare const handleHttpRequest: (ctx: HttpContextContract) => Promise<void>;
export declare function createRouter(): import("@trpc/server/dist/unstable-core-do-not-import").CreateRouterInner<import("@trpc/server/dist/unstable-core-do-not-import").RootConfig<{
    ctx: any;
    meta: object;
    errorShape: never;
    transformer: import("@trpc/server/dist/unstable-core-do-not-import").DataTransformerOptions;
}>, {
    products: any;
    langs: any;
}>;
export declare type AppRouter = ReturnType<typeof createRouter>;
