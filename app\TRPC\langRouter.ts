import { langProvider } from 'App/Providers/LangProvider'
import { CreateRouterParams } from 'App/Services/tRPC'

import { z } from 'zod'

export const langRouter = ({ router, publicProcedure, authedProcedure }: CreateRouterParams) =>
  router({
    getFullDict: authedProcedure.query(async ({ ctx, input }) => {
      return await langProvider.getFullDict()
    }),
    getUntranslatedValues: publicProcedure.query(async ({ ctx, input }) => {
      return await langProvider.getUntranslatedProductsValues()
    }),
    deleteRow: authedProcedure
      .input(
        z.object({
          tkey: z.string()
        })
      )
      .mutation(async ({ ctx, input }) => {
        return await langProvider.deleteRow(input.tkey)
      }),
    updateRow: authedProcedure
      .input(
        z.object({
          id: z.number(),
          tkey: z.string().min(1),
          en: z.string().optional(),
          pl: z.string().optional(),
          es: z.string().optional(),
          fr: z.string().optional(),
          ar: z.string().optional()
        })
      )
      .mutation(async ({ ctx, input }) => {
        return await langProvider.updateRow(input)
      }),
    addRow: authedProcedure
      .input(
        z.object({
          tkey: z.string().min(1),
          en: z.string(),
          pl: z.string(),
          es: z.string(),
          fr: z.string(),
          ar: z.string()
        })
      )
      .mutation(async ({ ctx, input }) => {
        return await langProvider.addRow(input)
      })
  })


  // export type LangRouter = Awaited<ReturnType<typeof langRouter>>
