import { orderProvider } from 'App/Providers/OrderProvider'
import type { CreateRouterParams } from 'App/Services/tRPC'
import { z } from 'zod'

export const orderRouter = ({ router, publicProcedure, authedProcedure }: CreateRouterParams) =>
  router({
    getUserOrders: authedProcedure.input(z.object({ page: z.number().optional() })).query(async ({ ctx, input }) => {
      if (!ctx.sessionUser?.client_id) {
        throw new Error('Client not found')
      }
      return await orderProvider.getUserOrders({ clientId: ctx.sessionUser.client_id, page: input.page })
    })
  })
