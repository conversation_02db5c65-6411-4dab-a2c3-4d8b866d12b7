/**
 * Утилиты для работы с Ozon API
 * 
 * Вспомогательные функции для обработки данных,
 * валидации и форматирования при работе с Ozon.
 */

import { OZON_PRODUCT_STATES, getProductStateDescription } from 'Config/ozon'

/**
 * Утилиты для работы с товарами
 */
export class OzonProductUtils {
  /**
   * Генерация offer_id из данных товара
   */
  static generateOfferId(product: any): string {
    if (product.prod_sku) {
      return product.prod_sku
    }
    return `product_${product.prod_id}`
  }

  /**
   * Подготовка названия товара для Ozon
   */
  static prepareProductName(product: any): string {
    const parts = []

    if (product.prod_purpose) parts.push(product.prod_purpose)
    if (product.prod_sku) parts.push(product.prod_sku)
    if (product.prod_size) parts.push(`размер: ${product.prod_size}`)
    if (product.prod_type) parts.push(`тип: ${product.prod_type}`)

    let name = parts.join(' ')
    
    // Обрезаем до максимальной длины
    if (name.length > 500) {
      name = name.substring(0, 497) + '...'
    }

    return name || `Товар ${product.prod_id}`
  }

  /**
   * Подготовка описания товара для Ozon
   */
  static prepareProductDescription(product: any): string {
    const parts = []

    if (product.prod_note) parts.push(product.prod_note)
    if (product.prod_uses) parts.push(`Применение: ${product.prod_uses}`)
    if (product.prod_composition) parts.push(`Состав: ${product.prod_composition}`)
    if (product.prod_features) parts.push(`Особенности: ${product.prod_features}`)

    let description = parts.join('\n\n')

    // Обрезаем до максимальной длины
    if (description.length > 4000) {
      description = description.substring(0, 3997) + '...'
    }

    return description || 'Описание товара'
  }

  /**
   * Подготовка цены с наценкой
   */
  static preparePrice(basePrice: number, additionalPrice = 500): string {
    const finalPrice = Math.max(0, basePrice + additionalPrice)
    return finalPrice.toString()
  }

  /**
   * Подготовка старой цены (для скидок)
   */
  static prepareOldPrice(currentPrice: number, discountPercent: number): string | undefined {
    if (discountPercent <= 0) return undefined
    
    const oldPrice = Math.round(currentPrice / (1 - discountPercent / 100))
    return oldPrice > currentPrice ? oldPrice.toString() : undefined
  }

  /**
   * Конвертация веса в граммы
   */
  static convertWeightToGrams(weight: string | number): number {
    if (typeof weight === 'number') return weight

    if (typeof weight === 'string') {
      // Убираем все кроме цифр, точек и запятых
      const cleanWeight = weight.replace(/[^\d.,]/g, '').replace(',', '.')
      const parsed = parseFloat(cleanWeight)
      
      if (isNaN(parsed)) return 0

      // Если вес меньше 10, считаем что это килограммы
      if (parsed < 10) {
        return Math.round(parsed * 1000)
      }
      
      // Иначе считаем что это уже граммы
      return Math.round(parsed)
    }

    return 0
  }

  /**
   * Валидация данных товара для Ozon
   */
  static validateProduct(product: any): {
    isValid: boolean
    errors: string[]
    warnings: string[]
  } {
    const errors: string[] = []
    const warnings: string[] = []

    // Обязательные поля
    if (!product.offer_id) {
      errors.push('Отсутствует артикул товара (offer_id)')
    }

    if (!product.name || product.name.trim().length === 0) {
      errors.push('Отсутствует название товара')
    }

    if (!product.category_id || product.category_id <= 0) {
      errors.push('Не указана категория товара')
    }

    if (!product.price || parseFloat(product.price) <= 0) {
      errors.push('Не указана цена товара или цена равна нулю')
    }

    // Проверка длины полей
    if (product.name && product.name.length > 500) {
      errors.push('Название товара слишком длинное (максимум 500 символов)')
    }

    if (product.description && product.description.length > 4000) {
      errors.push('Описание товара слишком длинное (максимум 4000 символов)')
    }

    // Предупреждения
    if (product.name && product.name.length < 10) {
      warnings.push('Название товара очень короткое (рекомендуется минимум 10 символов)')
    }

    if (!product.description || product.description.length < 50) {
      warnings.push('Описание товара слишком короткое (рекомендуется минимум 50 символов)')
    }

    if (!product.images || product.images.length === 0) {
      warnings.push('Отсутствуют изображения товара')
    }

    if (product.images && product.images.length > 15) {
      warnings.push('Слишком много изображений (максимум 15)')
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    }
  }
}

/**
 * Утилиты для работы с атрибутами товаров
 */
export class OzonAttributeUtils {
  /**
   * Стандартные атрибуты для товаров
   */
  static readonly STANDARD_ATTRIBUTES = {
    BRAND: 85,           // Бренд
    MODEL: 9048,         // Модель
    MATERIAL: 10096,     // Материал
    DIMENSIONS: 9461,    // Размеры
    COLOR: 10097,        // Цвет
    WEIGHT: 4180,        // Вес
    COUNTRY: 9024,       // Страна производства
    WARRANTY: 9048       // Гарантия
  }

  /**
   * Создание атрибута бренда
   */
  static createBrandAttribute(brand: string = 'SNF'): any {
    return {
      id: this.STANDARD_ATTRIBUTES.BRAND,
      values: [{ value: brand }]
    }
  }

  /**
   * Создание атрибута модели
   */
  static createModelAttribute(model: string): any {
    return {
      id: this.STANDARD_ATTRIBUTES.MODEL,
      values: [{ value: model }]
    }
  }

  /**
   * Создание атрибута материала
   */
  static createMaterialAttribute(material: string): any {
    return {
      id: this.STANDARD_ATTRIBUTES.MATERIAL,
      values: [{ value: material }]
    }
  }

  /**
   * Создание атрибута размеров
   */
  static createDimensionsAttribute(product: any): any | null {
    const dimensions = []
    
    if (product.size_in) dimensions.push(`Внутренний: ${product.size_in}`)
    if (product.size_out) dimensions.push(`Внешний: ${product.size_out}`)
    if (product.size_h) dimensions.push(`Высота: ${product.size_h}`)

    if (dimensions.length === 0) return null

    return {
      id: this.STANDARD_ATTRIBUTES.DIMENSIONS,
      values: [{ value: dimensions.join(', ') }]
    }
  }

  /**
   * Создание всех стандартных атрибутов для товара
   */
  static createStandardAttributes(product: any): any[] {
    const attributes = []

    // Бренд (всегда добавляем)
    attributes.push(this.createBrandAttribute())

    // Модель
    if (product.prod_model) {
      attributes.push(this.createModelAttribute(product.prod_model))
    }

    // Материал
    if (product.prod_material) {
      attributes.push(this.createMaterialAttribute(product.prod_material))
    }

    // Размеры
    const dimensionsAttr = this.createDimensionsAttribute(product)
    if (dimensionsAttr) {
      attributes.push(dimensionsAttr)
    }

    return attributes
  }
}

/**
 * Утилиты для работы с ошибками Ozon API
 */
export class OzonErrorUtils {
  /**
   * Проверка, является ли ошибка временной (можно повторить запрос)
   */
  static isRetryableError(error: any): boolean {
    const status = error.response?.status
    const code = error.code

    // HTTP статусы, при которых можно повторить запрос
    const retryableStatuses = [429, 500, 502, 503, 504]
    
    // Коды ошибок, при которых можно повторить запрос
    const retryableCodes = ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND']

    return retryableStatuses.includes(status) || retryableCodes.includes(code)
  }

  /**
   * Получение времени ожидания перед повтором запроса
   */
  static getRetryDelay(error: any, attempt: number): number {
    const status = error.response?.status

    if (status === 429) {
      // Rate limit - ждем 1 минуту
      return 60000
    }

    if (status >= 500) {
      // Серверные ошибки - экспоненциальная задержка
      return Math.min(1000 * Math.pow(2, attempt), 30000)
    }

    // Сетевые ошибки - фиксированная задержка
    return 3000
  }

  /**
   * Извлечение понятного сообщения об ошибке
   */
  static extractErrorMessage(error: any): string {
    // Ошибка от Ozon API
    if (error.response?.data?.error?.message) {
      return error.response.data.error.message
    }

    // Общая ошибка от API
    if (error.response?.data?.message) {
      return error.response.data.message
    }

    // HTTP ошибка
    if (error.response?.status) {
      return `HTTP ${error.response.status}: ${error.response.statusText || 'Unknown error'}`
    }

    // Сетевая ошибка
    if (error.code) {
      const networkErrors = {
        'ECONNRESET': 'Соединение сброшено',
        'ETIMEDOUT': 'Превышено время ожидания',
        'ENOTFOUND': 'Сервер не найден',
        'ECONNREFUSED': 'Соединение отклонено'
      }
      
      return networkErrors[error.code] || `Сетевая ошибка: ${error.code}`
    }

    // Обычная ошибка
    return error.message || 'Неизвестная ошибка'
  }
}

/**
 * Утилиты для работы со статусами товаров
 */
export class OzonStatusUtils {
  /**
   * Проверка, активен ли товар
   */
  static isProductActive(productInfo: any): boolean {
    if (!productInfo) return false
    
    const activeStates = [
      'processed',
      'ready_to_supply'
    ]

    return productInfo.visible && 
           activeStates.includes(productInfo.state) &&
           !productInfo.errors?.length
  }

  /**
   * Получение человекочитаемого статуса товара
   */
  static getProductStatusText(productInfo: any): string {
    if (!productInfo) return 'Товар не найден'

    const state = productInfo.state || 'unknown'
    const description = getProductStateDescription(state)

    if (productInfo.errors?.length > 0) {
      return `${description} (есть ошибки)`
    }

    if (!productInfo.visible) {
      return `${description} (скрыт)`
    }

    return description
  }

  /**
   * Получение списка проблем с товаром
   */
  static getProductIssues(productInfo: any): string[] {
    const issues: string[] = []

    if (!productInfo) {
      issues.push('Товар не найден в Ozon')
      return issues
    }

    if (!productInfo.visible) {
      issues.push('Товар скрыт')
    }

    if (productInfo.errors?.length > 0) {
      productInfo.errors.forEach(error => {
        issues.push(error.message || 'Неизвестная ошибка')
      })
    }

    const problemStates = ['declined', 'failed_moderation', 'failed_validation', 'state_failed']
    if (problemStates.includes(productInfo.state)) {
      issues.push(`Проблемный статус: ${getProductStateDescription(productInfo.state)}`)
    }

    return issues
  }
}

/**
 * Утилиты для форматирования данных
 */
export class OzonFormatUtils {
  /**
   * Форматирование цены для отображения
   */
  static formatPrice(price: string | number): string {
    const numPrice = typeof price === 'string' ? parseFloat(price) : price
    return new Intl.NumberFormat('ru-RU', {
      style: 'currency',
      currency: 'RUB',
      minimumFractionDigits: 0
    }).format(numPrice)
  }

  /**
   * Форматирование веса для отображения
   */
  static formatWeight(weightInGrams: number): string {
    if (weightInGrams >= 1000) {
      return `${(weightInGrams / 1000).toFixed(1)} кг`
    }
    return `${weightInGrams} г`
  }

  /**
   * Форматирование размеров для отображения
   */
  static formatDimensions(height?: number, width?: number, depth?: number): string {
    const dimensions = []
    
    if (width) dimensions.push(`Ш: ${width}`)
    if (depth) dimensions.push(`Г: ${depth}`)
    if (height) dimensions.push(`В: ${height}`)

    return dimensions.length > 0 ? dimensions.join(' × ') + ' мм' : 'Не указано'
  }

  /**
   * Форматирование даты для отображения
   */
  static formatDate(date: Date | string): string {
    const dateObj = typeof date === 'string' ? new Date(date) : date
    return new Intl.DateTimeFormat('ru-RU', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(dateObj)
  }
}
