import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class ClientsSchema extends BaseSchema {
  protected tableName = 'clients'

  public async up () {
    this.schema.alterTable(this.tableName, (table) => {
      //table.increments('id').primary()
      //table.string('email', 255).notNullable()
      table.string('client_password', 180).notNullable()
      table.string('remember_me_token').nullable()
      table.timestamps(true)
    })
  }

  public async down () {
    this.schema.dropTable(this.tableName)
  }
}
