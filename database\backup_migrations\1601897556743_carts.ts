import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Carts extends BaseSchema {
  protected tableName = 'cart'

  public async up () {
/*     this.schema.createTable(this.tableName, (table) => {
      table.increments('cart_id')
      table.timestamps(true)
    }) */

    this.schema.alterTable(this.tableName, (table) => {
      table.timestamps(true)
      table.dropColumn('cart_timestamp')
    })
  }

  public async down () {
    this.schema.dropTable(this.tableName)
  }
}
