import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Filters extends BaseSchema {
  protected tableName = 'filters'

  public async up () {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.integer('category_id')
      table.string('field')
      table.string('title')
      //table.boolean('defaults').defaultTo(false)
      //table.timestamps(true)
    })

  }

  public async down () {
    this.schema.dropTable(this.tableName)
  }
}
