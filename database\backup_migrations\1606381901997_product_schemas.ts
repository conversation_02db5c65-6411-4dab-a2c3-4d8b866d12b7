import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class ProductSchemas extends BaseSchema {
  protected tableName = 'product_schemas'

  public async up () {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.timestamps(true)
      table.integer('product_id')
      table.text('body', 'mediumtext')
    })
  }

  public async down () {
    this.schema.dropTable(this.tableName)
  }
}
