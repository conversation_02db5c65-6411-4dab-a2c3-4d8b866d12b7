import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Products extends BaseSchema {
  protected tableName = 'products'

  public async up () {
    this.schema.alterTable(this.tableName, (table) => {
      table.decimal('size_in').defaultTo('')
      table.decimal('size_in_2').defaultTo('')

      table.decimal('size_out').defaultTo('')
      table.decimal('size_out_2').defaultTo('')

      table.decimal('size_h').defaultTo('')
      table.decimal('size_h_2').defaultTo('')

    })
  }

  public async down () {
    this.schema.alterTable(this.tableName, (table) => {
    })
  }
}
