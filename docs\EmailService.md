# EmailService - Документация

## Обзор

EmailService - это отказоустойчивый сервис для массовой рассылки email с поддержкой checkpoint'ов, детального логирования и мониторинга.

## Основные возможности

### ✅ Реализовано

1. **Отказоустойчивость**
   - Checkpoint система для возобновления рассылки с места остановки
   - Retry логика с экспоненциальной задержкой
   - Graceful shutdown
   - Валидация email адресов

2. **Логирование**
   - Детальные логи всех отправок в таблице `email_logs`
   - Статистика по кампаниям
   - Отслеживание ошибок и повторных попыток

3. **Мониторинг**
   - Прогресс активных кампаний в реальном времени
   - API для получения статистики
   - Журналирование в системный журнал

4. **Конфигурация**
   - Настраиваемые размеры батчей
   - Задержки между отправками
   - Максимальное количество попыток
   - Временные и постоянные ошибки

## Структура файлов

```
app/
├── Services/
│   └── EmailService.ts          # Основной сервис
├── Models/
│   └── EmailLog.ts             # Модель для логирования
├── Interfaces/email/
│   └── EmailInterfaces.ts      # Типы и интерфейсы
├── Controllers/Http/
│   └── EmailTestController.ts  # Контроллер для тестирования
└── Providers/
    └── ServiceProvider.ts      # Обновленный провайдер (делегирует к EmailService)

database/migrations/
└── 1749571344630_email_logs.ts # Миграция для таблицы логов

app/Routes/
└── api.ts                      # API маршруты для тестирования
```

## API Endpoints для тестирования

### POST `/api/email/test-email`
Тестовая отправка одного письма
```json
{
  "email": "<EMAIL>",
  "subject": "Тест",
  "message": "<h1>Тестовое письмо</h1>"
}
```

### GET `/api/email/campaigns/active`
Получить список активных кампаний

### GET `/api/email/campaigns/:campaignId/stats`
Статистика по конкретной кампании

### GET `/api/email/logs?campaignId=xxx&status=failed&limit=50&page=1`
Получить логи рассылки с фильтрацией

### POST `/api/email/retry-failed?campaignId=xxx`
Повторить неудачные отправки

### GET `/api/email/next-scheduled`
Информация о следующем запланированном запуске

### POST `/api/email/trigger-scheduled?batchSize=10`
Запустить рассылку по расписанию

### DELETE `/api/email/campaigns/:campaignId`
Отменить активную кампанию

### GET `/api/email/settings`
Получить настройки рассылки

### PUT `/api/email/settings`
Обновить настройки рассылки

### DELETE `/api/email/cleanup-logs?daysOld=90`
Очистить старые логи

## Использование в коде

### Базовое использование
```typescript
import { emailService } from 'App/Services/EmailService'

// Массовая рассылка
const result = await emailService.sendBulkEmail({
  batchSize: 20,
  delayBetweenBatches: 15000,
  delayBetweenEmails: 2000,
  maxRetries: 3,
  validateEmails: true
})

// Рассылка по расписанию
const scheduledResult = await emailService.sendScheduledEmail({
  batchSize: 50
})

// Повторная отправка неудачных
const retryResult = await emailService.retryFailedEmails('campaign-id')
```

### Мониторинг
```typescript
// Получить прогресс кампании
const progress = emailService.getCampaignProgress('campaign-id')

// Получить все активные кампании
const activeCampaigns = emailService.getActiveCampaigns()

// Статистика по кампании
const stats = await emailService.getCampaignStats('campaign-id')
```

### Управление
```typescript
// Отменить кампанию
const cancelled = emailService.cancelCampaign('campaign-id')

// Graceful shutdown
emailService.requestShutdown()
```

## Конфигурация

### EmailCampaignConfig
```typescript
{
  batchSize: 20,              // Размер батча
  delayBetweenBatches: 15000, // Задержка между батчами (мс)
  delayBetweenEmails: 2000,   // Задержка между письмами (мс)
  maxRetries: 3,              // Максимум попыток
  retryDelay: 5000,           // Задержка перед повтором (мс)
  enableCheckpoints: true,    // Включить checkpoint'ы
  validateEmails: true        // Валидировать email'ы
}
```

### RetryConfig
```typescript
{
  maxAttempts: 5,
  baseDelay: 2000,
  maxDelay: 60000,
  exponentialBackoff: true,
  temporaryErrorCodes: [451, 421, 'EENVELOPE', 'ETIMEDOUT', 'ECONNRESET']
}
```

## База данных

### Таблица email_logs
```sql
CREATE TABLE email_logs (
  id INT PRIMARY KEY AUTO_INCREMENT,
  created_at TIMESTAMP,
  updated_at TIMESTAMP,
  campaign_id VARCHAR(255),
  recipient_email VARCHAR(255) NOT NULL,
  recipient_note TEXT,
  subject VARCHAR(500) NOT NULL,
  message_preview TEXT,
  status VARCHAR(50) DEFAULT 'pending',
  error_message TEXT,
  attempts INT DEFAULT 0,
  last_attempt_at TIMESTAMP NULL,
  sent_at TIMESTAMP NULL,
  batch_number INT DEFAULT 0,
  checkpoint_index INT DEFAULT 0,
  user_id INT,
  user_name VARCHAR(255),
  
  INDEX idx_campaign_id (campaign_id),
  INDEX idx_status (status),
  INDEX idx_recipient_email (recipient_email),
  INDEX idx_created_at (created_at)
);
```

## Безопасность для больших рассылок (1500+ писем)

### 1. Checkpoint система
- Каждое письмо сохраняется в БД с индексом
- При сбое рассылка возобновляется с последнего checkpoint'а
- Не нужно отправлять повторно уже отправленные письма

### 2. Graceful shutdown
- Сервис может быть остановлен без потери данных
- Текущий батч завершается корректно
- Состояние сохраняется в БД

### 3. Мониторинг в реальном времени
- Прогресс кампании доступен через API
- Детальная статистика по ошибкам
- Возможность отмены кампании

### 4. Retry логика
- Автоматические повторы для временных ошибок
- Экспоненциальная задержка
- Ограничение количества попыток

### 5. Валидация и фильтрация
- Проверка email адресов перед отправкой
- Фильтрация неактивных получателей
- Предотвращение отправки на невалидные адреса

## Рекомендации по использованию

### Для рассылки 1500+ писем:
1. Используйте `batchSize: 20-50`
2. Устанавливайте `delayBetweenBatches: 15000-30000` (15-30 сек)
3. Включайте `enableCheckpoints: true`
4. Мониторьте прогресс через API
5. Настройте алерты на критические ошибки

### Тестирование:
1. Начните с малых батчей (5-10 писем)
2. Проверьте логи на ошибки
3. Убедитесь в корректности checkpoint'ов
4. Протестируйте graceful shutdown

## Troubleshooting

### Частые проблемы:
1. **Блокировка SMTP** - увеличьте задержки между отправками
2. **Timeout ошибки** - проверьте настройки SMTP сервера
3. **Невалидные email** - включите валидацию
4. **Потеря прогресса** - убедитесь что checkpoint'ы включены

### Логи для диагностики:
- Системные логи в `journals` таблице
- Детальные логи в `email_logs` таблице
- Console логи с прогрессом батчей
