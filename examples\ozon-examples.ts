/**
 * Примеры использования интеграции с Ozon Seller API
 */

import { ozonProvider } from 'App/Providers/OzonProvider'
import { ozonService } from 'App/Services/OzonService'
import { ozonConfig } from 'App/Services/OzonConfig'
import { OzonImageService } from 'App/Services/OzonImageService'
import { $prisma } from 'App/Services/Prisma'

/**
 * Пример 1: Базовая синхронизация товаров
 */
export async function basicProductSync() {
  console.log('=== Базовая синхронизация товаров ===')
  
  try {
    // Проверяем, включена ли синхронизация
    const isEnabled = await ozonConfig.isEnabled()
    if (!isEnabled) {
      console.log('Синхронизация отключена. Включите её в настройках.')
      return
    }

    // Синхронизируем несколько товаров
    const productIds = [1, 2, 3, 4, 5]
    const result = await ozonProvider.syncProducts(productIds)
    
    console.log(`Результат синхронизации:`)
    console.log(`- Всего товаров: ${result.total}`)
    console.log(`- Успешно: ${result.success.length}`)
    console.log(`- Ошибок: ${result.failed.length}`)
    
    if (result.failed.length > 0) {
      console.log('Ошибки:')
      result.failed.forEach(error => {
        console.log(`  - Товар ${error.productId}: ${error.error}`)
      })
    }

  } catch (error) {
    console.error('Ошибка синхронизации:', error.message)
  }
}

/**
 * Пример 2: Обновление остатков товаров
 */
export async function updateProductStocks() {
  console.log('=== Обновление остатков товаров ===')
  
  try {
    const warehouseId = await ozonConfig.getSetting<number>('ozon_warehouse_id')
    
    // Получаем товары с низкими остатками
    const lowStockProducts = await $prisma.products.findMany({
      where: {
        prod_count: {
          lt: 10
        },
        prod_sku: {
          not: null
        }
      },
      take: 10
    })

    if (lowStockProducts.length === 0) {
      console.log('Товары с низкими остатками не найдены')
      return
    }

    // Обновляем остатки в Ozon
    const stocks = lowStockProducts.map(product => ({
      offer_id: product.prod_sku!,
      stock: product.prod_count,
      warehouse_id: warehouseId
    }))

    const result = await ozonProvider.updateStocks(stocks)
    
    console.log(`Обновлено остатков: ${result.updated}`)
    if (result.failed.length > 0) {
      console.log('Ошибки обновления:')
      result.failed.forEach(error => {
        console.log(`  - ${error.offerId}: ${error.error}`)
      })
    }

  } catch (error) {
    console.error('Ошибка обновления остатков:', error.message)
  }
}

/**
 * Пример 3: Массовое обновление цен
 */
export async function updateProductPrices() {
  console.log('=== Массовое обновление цен ===')
  
  try {
    // Получаем товары для обновления цен
    const products = await $prisma.products.findMany({
      where: {
        prod_price: {
          gt: 0
        },
        prod_sku: {
          not: null
        }
      },
      take: 20
    })

    // Подготавливаем данные для обновления цен
    const prices = products.map(product => ({
      offer_id: product.prod_sku!,
      price: String(product.prod_price),
      old_price: product.prod_discount > 0 
        ? String(product.prod_price * (1 + product.prod_discount / 100))
        : undefined,
      currency_code: 'RUB'
    }))

    const result = await ozonProvider.updatePrices(prices)
    
    console.log(`Обновлено цен: ${result.updated}`)
    if (result.failed.length > 0) {
      console.log('Ошибки обновления цен:')
      result.failed.forEach(error => {
        console.log(`  - ${error.offerId}: ${error.error}`)
      })
    }

  } catch (error) {
    console.error('Ошибка обновления цен:', error.message)
  }
}

/**
 * Пример 4: Загрузка изображений товаров
 */
export async function uploadProductImages() {
  console.log('=== Загрузка изображений товаров ===')
  
  try {
    const imageService = new OzonImageService()
    
    // Получаем товары с изображениями
    const productsWithImages = await $prisma.products.findMany({
      where: {
        OR: [
          { prod_img: { not: null } },
          { prod_img_rumi: { not: null } },
          { prod_images: { not: null } }
        ]
      },
      take: 5
    })

    for (const product of productsWithImages) {
      console.log(`Обрабатываем товар ${product.prod_id} (${product.prod_sku})`)
      
      // Получаем локальные изображения
      const localImages = await imageService.getLocalImages(product)
      console.log(`  Найдено изображений: ${localImages.imagePaths.length}`)
      
      if (localImages.imagePaths.length > 0) {
        // Загружаем изображения в Ozon
        const result = await ozonProvider.uploadProductImages(product.prod_id)
        
        if (result.success) {
          console.log(`  Загружено: ${result.uploadedImages.length} изображений`)
        } else {
          console.log(`  Ошибки загрузки:`)
          result.failed.forEach(error => {
            console.log(`    - ${error.fileName}: ${error.error}`)
          })
        }
      }
    }

  } catch (error) {
    console.error('Ошибка загрузки изображений:', error.message)
  }
}

/**
 * Пример 5: Проверка статуса товаров в Ozon
 */
export async function checkProductStatuses() {
  console.log('=== Проверка статуса товаров в Ozon ===')
  
  try {
    // Получаем товары с SKU
    const products = await $prisma.products.findMany({
      where: {
        prod_sku: { not: null }
      },
      take: 10,
      select: {
        prod_id: true,
        prod_sku: true,
        prod_note: true
      }
    })

    for (const product of products) {
      try {
        const status = await ozonProvider.getProductStatus(product.prod_sku!)
        
        console.log(`Товар ${product.prod_sku}:`)
        console.log(`  Статус: ${status.status}`)
        console.log(`  Активен: ${status.isActive ? 'Да' : 'Нет'}`)
        
        if (status.errors.length > 0) {
          console.log(`  Ошибки: ${status.errors.join(', ')}`)
        }

      } catch (error) {
        console.log(`  Ошибка получения статуса: ${error.message}`)
      }
    }

  } catch (error) {
    console.error('Ошибка проверки статусов:', error.message)
  }
}

/**
 * Пример 6: Настройка и валидация конфигурации
 */
export async function configurationExample() {
  console.log('=== Настройка и валидация конфигурации ===')
  
  try {
    // Получаем текущие настройки
    const settings = await ozonConfig.getSettings()
    console.log('Текущие настройки:')
    console.log(`  API URL: ${settings.ozon_api_url}`)
    console.log(`  Синхронизация включена: ${settings.ozon_sync_enabled}`)
    console.log(`  ID склада: ${settings.ozon_warehouse_id}`)
    console.log(`  Загрузка изображений: ${settings.ozon_image_upload_enabled}`)

    // Валидация API настроек
    const validation = await ozonConfig.validateApiSettings()
    console.log(`\nВалидация API: ${validation.isValid ? 'Пройдена' : 'Не пройдена'}`)
    
    if (!validation.isValid) {
      console.log('Ошибки конфигурации:')
      validation.errors.forEach(error => {
        console.log(`  - ${error}`)
      })
    }

    // Статистика настроек
    const stats = await ozonConfig.getSettingsStats()
    console.log(`\nСтатистика настроек:`)
    console.log(`  Всего настроек: ${stats.totalSettings}`)
    console.log(`  Настроено: ${stats.configuredSettings}`)
    console.log(`  Не настроено: ${stats.missingSettings.length}`)
    
    if (stats.missingSettings.length > 0) {
      console.log('Отсутствующие настройки:')
      stats.missingSettings.forEach(setting => {
        console.log(`  - ${setting}`)
      })
    }

  } catch (error) {
    console.error('Ошибка работы с конфигурацией:', error.message)
  }
}

/**
 * Пример 7: Полный цикл работы с товаром
 */
export async function fullProductWorkflow() {
  console.log('=== Полный цикл работы с товаром ===')
  
  try {
    // 1. Получаем товар из БД
    const product = await $prisma.products.findFirst({
      where: {
        prod_sku: { not: null },
        prod_price: { gt: 0 }
      }
    })

    if (!product) {
      console.log('Подходящий товар не найден')
      return
    }

    console.log(`Работаем с товаром: ${product.prod_sku}`)

    // 2. Маппинг в формат Ozon
    const ozonProduct = await ozonService.mapProductToOzon(product)
    console.log(`Маппинг выполнен: ${ozonProduct.name}`)

    // 3. Валидация данных
    const validation = await ozonService.validateProductData(ozonProduct)
    console.log(`Валидация: ${validation.isValid ? 'Пройдена' : 'Не пройдена'}`)
    
    if (!validation.isValid) {
      console.log('Ошибки валидации:')
      validation.errors.forEach(error => console.log(`  - ${error}`))
      return
    }

    // 4. Проверяем существование в Ozon
    const existingProduct = await ozonProvider.getProductInfo(product.prod_sku!)
    
    if (existingProduct) {
      console.log('Товар уже существует в Ozon, обновляем...')
      
      // Обновляем товар
      const updateResult = await ozonProvider.updateProduct(product.prod_sku!, ozonProduct)
      console.log(`Обновление: ${updateResult.success ? 'Успешно' : 'Ошибка'}`)
      
    } else {
      console.log('Создаем новый товар в Ozon...')
      
      // Создаем товар
      const createResult = await ozonProvider.createProduct(ozonProduct)
      console.log(`Создание: ${createResult.success ? 'Успешно' : 'Ошибка'}`)
    }

    // 5. Загружаем изображения
    const imageResult = await ozonProvider.uploadProductImages(product.prod_id)
    console.log(`Изображения: ${imageResult.success ? 'Загружены' : 'Ошибка загрузки'}`)

    // 6. Обновляем остатки
    const warehouseId = await ozonConfig.getSetting<number>('ozon_warehouse_id')
    const stockResult = await ozonProvider.updateStocks([{
      offer_id: product.prod_sku!,
      stock: product.prod_count,
      warehouse_id: warehouseId
    }])
    console.log(`Остатки: ${stockResult.success ? 'Обновлены' : 'Ошибка обновления'}`)

    console.log('Полный цикл завершен успешно!')

  } catch (error) {
    console.error('Ошибка в полном цикле:', error.message)
  }
}

/**
 * Пример 8: Мониторинг и статистика
 */
export async function monitoringExample() {
  console.log('=== Мониторинг и статистика ===')
  
  try {
    // Получаем статистику товаров
    const totalProducts = await $prisma.products.count()
    const productsWithSku = await $prisma.products.count({
      where: { prod_sku: { not: null } }
    })
    const productsWithImages = await $prisma.products.count({
      where: {
        OR: [
          { prod_img: { not: null } },
          { prod_images: { not: null } }
        ]
      }
    })

    console.log('Статистика товаров:')
    console.log(`  Всего товаров: ${totalProducts}`)
    console.log(`  С SKU: ${productsWithSku}`)
    console.log(`  С изображениями: ${productsWithImages}`)
    console.log(`  Готовы к синхронизации: ${productsWithSku}`)

    // Проверяем настройки
    const settings = await ozonConfig.getSettings()
    const validation = await ozonConfig.validateApiSettings()
    
    console.log('\nСостояние интеграции:')
    console.log(`  Синхронизация включена: ${settings.ozon_sync_enabled}`)
    console.log(`  API настроен: ${validation.isValid}`)
    console.log(`  Загрузка изображений: ${settings.ozon_image_upload_enabled}`)

    if (!validation.isValid) {
      console.log('  Проблемы конфигурации:')
      validation.errors.forEach(error => console.log(`    - ${error}`))
    }

  } catch (error) {
    console.error('Ошибка мониторинга:', error.message)
  }
}

// Функция для запуска всех примеров
export async function runAllExamples() {
  console.log('🚀 Запуск всех примеров интеграции с Ozon\n')
  
  const examples = [
    { name: 'Базовая синхронизация', fn: basicProductSync },
    { name: 'Обновление остатков', fn: updateProductStocks },
    { name: 'Обновление цен', fn: updateProductPrices },
    { name: 'Загрузка изображений', fn: uploadProductImages },
    { name: 'Проверка статусов', fn: checkProductStatuses },
    { name: 'Конфигурация', fn: configurationExample },
    { name: 'Полный цикл', fn: fullProductWorkflow },
    { name: 'Мониторинг', fn: monitoringExample }
  ]

  for (const example of examples) {
    try {
      console.log(`\n📋 ${example.name}`)
      console.log('─'.repeat(50))
      await example.fn()
      console.log('✅ Завершено успешно')
    } catch (error) {
      console.log(`❌ Ошибка: ${error.message}`)
    }
  }

  console.log('\n🎉 Все примеры выполнены!')
}

// Экспорт для использования
export default {
  basicProductSync,
  updateProductStocks,
  updateProductPrices,
  uploadProductImages,
  checkProductStatuses,
  configurationExample,
  fullProductWorkflow,
  monitoringExample,
  runAllExamples
}
