const { MeiliSearch } = require('meilisearch');

async function fixRankingRules() {
  const client = new MeiliSearch({
    host: 'http://localhost:7700',
    apiKey: 'aSampleMasterKey'
  });

  try {
    console.log('🔧 Исправляем ranking rules для поиска...');
    
    // Сбалансированные ranking rules для поиска
    const correctRankingRules = [
      'words',      // Количество найденных слов (важно для частичных совпадений)
      'exactness',  // Точные совпадения
      'sort',       // Сортировка
      'typo',       // Опечатки
      'proximity',  // Близость слов
      'attribute'   // Важность атрибутов
    ];

    const task = await client.index('products').updateRankingRules(correctRankingRules);
    console.log('📝 Задача создана:', task.taskUid);
    
    // Ждем завершения
    await client.tasks.waitForTask(task.taskUid, { timeout: 30000 });
    console.log('✅ Ranking rules обновлены!');
    
    // Проверяем результат
    const currentRules = await client.index('products').getRankingRules();
    console.log('📋 Текущие ranking rules:', currentRules);
    
  } catch (error) {
    console.error('❌ Ошибка:', error);
  }
}

fixRankingRules();
