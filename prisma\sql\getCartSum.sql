-- @param {Int} $1:cartId
-- @param {Int} $2:discountStartSum
-- @param {Int} $3:cartId2

select sum( cart_items.qty * 
      IF( (select sum(cart_items.qty * products.prod_price) as cartsum 
      from cart_items 
      left outer join products 
      on products.prod_id = cart_items.prod_id 
      where `cart_id` = ? limit 1) > ?, 
      products.prod_price - ((products.prod_price / 100) * products.prod_discount), products.prod_price ) ) as cartsum,
      sum(cart_items.qty * products.prod_price) as defsum 
      from cart_items 
      left outer join products 
      on products.prod_id = cart_items.prod_id 
      where `cart_id` = ? limit 1