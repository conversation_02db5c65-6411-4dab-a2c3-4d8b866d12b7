
import { ApplicationContract } from '@ioc:Adonis/Core/Application'
import { initDynRoutes } from '../start/initDynRoutes'

export default class AppProvider {
	public static needsApplication = true

  constructor (protected app: ApplicationContract) {
  }

  public register () {
    // Register your own bindings
  }

  public async boot () {
    // IoC container is ready
    await initDynRoutes()
  }

  public async ready () {
    // App is ready
    if (this.app.environment === 'web') {
      await import('../start/socket')
    }
    

    // const Route = this.app.container.use('Adonis/Core/Route')
    // console.log(Route.toJSON())
  }

  public async shutdown () {
    // Cleanup, since app is going down

  }
}
