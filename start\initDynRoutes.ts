
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import GenerateDynamicPluginsRoutes from 'App/Plugins/GenerateDynamicPluginsRoutes'

export const initDynRoutes = async () => {
    const {default: Route} = await import('@ioc:Adonis/Core/Route')
    

    Route.group(async () => {
      const plugins: Array<dynPluginInterface> = await GenerateDynamicPluginsRoutes()

      plugins.map(plugin => {
          Route.route(plugin.route, plugin.httpmethods, async (httpCtx) => {
              return await plugin.cb(httpCtx)
          }).prefix('service')
      })

    //   console.log('init dynamic routes: ', plugins.map(i => i.route))
  })
}