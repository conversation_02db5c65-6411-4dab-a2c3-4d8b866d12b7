/*
|--------------------------------------------------------------------------
| Routes
|--------------------------------------------------------------------------
|
| This file is dedicated for defining HTTP routes. A single file is enough
| for majority of projects, however you can define routes in different
| files and just make sure to import them inside this file. For example
|
| Define routes in following two files
| ├── start/routes/cart.ts
| ├── start/routes/customer.ts
|
| and then import them inside `start/routes/index.ts` as follows
|
| import './cart'
| import './customer'
|
*/

import Route from '@ioc:Adonis/Core/Route'
import { handleHttpRequest } from 'App/Services/tRPC'

Route.on('/').render('welcome')

Route.get('init/', 'ServicesController.clientAppInit')

Route.get('products/findexist', 'ProductsController.findExist')

Route.get('products/duplicats/:id', 'ProductsController.findDuplicats')
Route.get('products/rkproducts/:id', 'ProductsController.getRkProducts')

Route.get('products/:category/', 'ProductsController.catalog')
Route.get('product/:id', 'ProductsController.id')

Route.get('categories/active', 'CategoriesController.catalog')

Route.get('search/global/:value/:page?/', 'SearchesController.global')
Route.get('search/fast/:value/', 'SearchesController.fast')

Route.post('service/parsexlsfile/', 'ServicesController.parseCartFromExcelFile')
Route.post('service/getfiltersdata/', 'ServicesController.filtersdata')
Route.post('service/findlocation/', 'ServicesController.findlocation')
Route.post('service/findstreet/', 'ServicesController.findstreet')

Route.get('service/filterspredata/:categoryid/:limit?', 'ServicesController.filtersPreData')
Route.get('service/filters/:category/', 'ServicesController.filters')
Route.get('service/iplocate/', 'ServicesController.iplocate')
Route.get('service/findorg/:query/', 'ServicesController.findorg')
Route.get('service/findorgbyinn/:query/', 'ServicesController.findorgbyinn')
Route.get('service/findbank/:query/', 'ServicesController.findbank')
Route.get('service/calcaluteshipping/:type/:country/:destinationIndex?', 'ServicesController.calcaluteShipping')
Route.get('service/subscribe/updates/:email', 'ServicesController.updateSubscribe')

Route.get('service/cart/weight/', 'ServicesController.getCartWeight')

Route.get('service/clear/snapshots/', 'ServicesController.clearSnapshots')
Route.get('service/clear/cart/', 'ServicesController.clearEmptyCarts')

// Новый Sitemap
Route.get('/sitemap.xml', 'SitemapsController.generateSitemapIndex')
Route.get('/sitemap-static.xml', 'SitemapsController.generateStaticSitemap')
Route.get('/sitemap-products/:page', 'SitemapsController.generateProductsSitemap')
// Старый генератор выключен
Route.get('service/sitemap/:page/:limit?', 'ServicesController.siteMap')
Route.get('service/yandex/yml/:page/:limit?', 'ServicesController.yml')

Route.get('service/emex/price', 'ServicesController.emexPrice')
Route.get('service/pricelist/', 'ServicesController.pricelist')
Route.get('service/countries/:locale?', 'ServicesController.countries')

Route.get('service/documents/prepare/', 'ServicesController.documents')
Route.get('service/documents/list', 'ServicesController.getDocumentsList')
Route.route('service/documents/offer/', ['POST', 'GET'], 'ServicesController.offerDocument')

//stripe
Route.post('service/create-payment-intent', 'ServicesController.createPaymentIntent')

//Route.get('service/ebay/listing', 'ServicesController.ebayListing')
//

Route.post('cart/push/', 'CartsController.push')
Route.get('cart/get/:page?/:number?/', 'CartsController.get')
Route.get('cart/mini/:page?/:number?/', 'CartsController.mini')
Route.get('cart/clear/', 'CartsController.clear')

Route.post('auth/login', 'AuthController.login')
Route.post('auth/login/setpassword', 'AuthController.setPassword')
Route.get('auth/login/check', 'AuthController.check')
Route.get('auth/logout', 'AuthController.logout')
Route.get('auth/login/resetpassword/:email', 'AuthController.resetpassword')

Route.post('client/update', 'ClientsController.update')
Route.get('client/dashboardstatistics/', 'ClientsController.dashboardStatistics')

Route.get('orders/list/:page?/:limit?', 'OrdersController.list')
Route.get('orders/items/:id/', 'OrdersController.items')
Route.post('orders/create/', 'OrdersController.create')

Route.get('statistics/w/:query', 'StatisticsController.write')
Route.post('preorder', 'ServicesController.preOrder')

Route.get('page/:key', 'PagesController.page')
Route.get('dictionary/:locale?', 'LangDictsController.dict')

Route.get('testr/', 'ServicesController.testr')

Route.route('service/sendmail/:to?/:message?/:subject?/', ['POST', 'GET'], 'ServicesController.sendMail')

Route.get('statistics/full', 'StatisticsController.export') // TODO: move to CPAN
Route.get('statistics/byperson', 'StatisticsController.workStatByPerson') // TODO: move to CPAN

Route.get('service/checkorders/', 'OrdersController.checkList')

Route.group(() => {
  Route.get('/init/', 'ServicesController.cpanInit')
  Route.get('/products/', 'ProductsController.cpanCatalog')
  Route.get('/product/:id', 'ProductsController.cpanProduct')
  Route.get('/products/delete/:id', 'ProductsController.delete')
  Route.post('/products/update', 'ProductsController.update')
  Route.post('/products/schema', 'ProductsController.createSchema')
  Route.get('/products/schema/delete/:id', 'ProductsController.deleteSchema')
  Route.get('/products/orders/:id', 'ProductsController.cpanOrderByProduct')
  Route.get('/products/snapshots/', 'ProductsController.getSnaphots')


  Route.get('/statistics/byoem/:oem', 'StatisticsController.statByOEM')
  Route.get('/statistics/dashboard/', 'StatisticsController.dashboard') //
  Route.get('/statistics/dashboard/ex', 'StatisticsController.exdashboard')
  Route.get('/statistics/byclient/', 'StatisticsController.statsByClient')
  Route.get('/statistics/topproducts/', 'StatisticsController.topProducts')
  Route.get('/statistics/orderprocessing/', 'StatisticsController.orderProcessingTime')

  Route.get('/statistics/orders/current', 'StatisticsController.ordersCurrentStats')

  Route.post('/products/bulkupload', 'ServicesController.productBulkUploadList')
  Route.get('/orders/list', 'OrdersController.cpanList')
  Route.get('/orders/merge', 'OrdersController.cpanMerge')
  Route.post('/order/save', 'OrdersController.cpanSave')
  Route.get('/order/:id', 'OrdersController.cpanId')
  Route.get('/order/price/:id', 'OrdersController.orderPrice')
  Route.get('orders/items/:id/', 'OrdersController.cpanItems')
  Route.get('orders/checkFullGtd/:id/', 'OrdersController.checkFullGtd')


  Route.post('/auth/login', 'AuthController.apilogin')
  Route.get('/auth/check', 'AuthController.acheck')

  Route.get('/clients/debtors', 'ClientsController.debtors')
  Route.get('/clients/', 'ClientsController.list')
  Route.get('/clients/:id', 'ClientsController.id')
  Route.post('/client/update', 'ClientsController.cpanUpdate')
  Route.get('/client/delete/:id', 'ClientsController.cpanDelete')
  Route.get('/client/restpassword/:id', 'ClientsController.cpanResetPassword')

  Route.get('statistics/remove/', 'StatisticsController.remove')

  Route.get('files/list', 'FilesController.list')
  Route.post('files/upload', 'FilesController.upload')
  Route.get('files/remove', 'FilesController.remove')

  Route.any('forms/make', 'PagesController.printForm')
  Route.get('forms/list', 'PagesController.printFormList')
  Route.any('forms/update', 'PagesController.update')

  Route.any('/shipping/calc/', 'ServicesController.cpanCalcaluteShipping')

  Route.get('/statistics/sales/bymonth', 'StatisticsController.salesByMonths')
  Route.get('/statistics/qty/bymonth', 'StatisticsController.qtyByMonths')

  Route.post('/statistics/sales/byfilter', 'StatisticsController.salesByFilter')
  Route.any('/statistics/byorders', 'StatisticsController.statsByOrders')

  Route.any('/statistics/list', 'StatisticsController.statsList')
  Route.any('/statistics/list/item', 'StatisticsController.statsListItem')

  Route.get('/statistics/list/delete', 'StatisticsController.removeStatItem')

  Route.get('/users/list/', 'ServicesController.getUsers')
  Route.post('/users/update/', 'ServicesController.updateUser')
}).prefix('/cpan')



Route.group(() => {
  // Тестовая отправка
  Route.post('/test-email', 'EmailTestController.testSingleEmail')

  // Статистика и мониторинг
  Route.get('/campaigns/:campaignId/stats', 'EmailTestController.getCampaignStats')
  Route.get('/campaigns/active', 'EmailTestController.getActiveCampaigns')
  Route.get('/logs', 'EmailTestController.getEmailLogs')

  // Управление рассылкой
  Route.post('/retry-failed', 'EmailTestController.retryFailedEmails')
  Route.get('/next-scheduled', 'EmailTestController.getNextScheduledRun')
  Route.post('/trigger-scheduled', 'EmailTestController.triggerScheduledEmail')
  Route.delete('/campaigns/:campaignId', 'EmailTestController.cancelCampaign')

  // Настройки
  Route.get('/settings', 'EmailTestController.getEmailSettings')
  Route.put('/settings', 'EmailTestController.updateEmailSettings')

  // Утилиты
  Route.delete('/cleanup-logs', 'EmailTestController.cleanupOldLogs')
}).prefix('/email')

Route.any('/trpc/*', handleHttpRequest)
