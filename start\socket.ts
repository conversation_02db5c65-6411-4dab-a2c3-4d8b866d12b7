import Ws from 'App/Services/Ws'
import AuthManager from '@ioc:Adonis/Addons/Auth'
import { HttpContext } from '@adonisjs/core/build/standalone'
import Session from '@ioc:Adonis/Addons/Session'
import Journal from 'App/Models/Journal'

async function auth(socket) {
    let ctx = HttpContext.create('/', {}, socket.request)

    const session = Session.create(ctx)
    await session.initiate(true)

    const auth = AuthManager.getAuthForRequest({ ...ctx, session })
    const user = await auth.authenticate()

    return user
}

Ws.boot()

Ws.io.on('connection', async (socket) => {

    const room = 'bulkupload_' /* + sessionUser.id */

    console.log('socket connected!', socket.id)
    
    socket.join(room)

    socket.on("disconnect", () => {
        console.log('socket disconnect: ', socket.id)
        socket.leave(room)
    })

    const journalData = await Journal.query().orderBy('id', 'desc').limit(100)

    socket.emit('journal', [...journalData])

})
