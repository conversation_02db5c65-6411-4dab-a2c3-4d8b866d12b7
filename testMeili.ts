import { MeiliSearch } from 'meilisearch'

async function run() {
  const client = new MeiliSearch({
    host: 'http://localhost:7700',
    apiKey: 'aSampleMasterKey'
  })

  async function checkIndexExists(indexUid: string) {
    try {
      const index = await client.getIndex(indexUid)
      return !!index
    } catch (error) {
      if (error.response.status === 404) {
        return false
      }
      throw error // если другая ошибка — пробрасываем дальше
    }
  }

  const result = await client.index('products').delete()
  console.log('result:', result)

  const result2 = await checkIndexExists('products')
  console.log('checkIndexExists:', result2)
}

await run()
