/**
 * Тесты для интеграции с Ozon Seller API
 * 
 * Для запуска тестов:
 * npm test tests/ozon-integration.test.ts
 */

import { ozonConfig } from 'App/Services/OzonConfig'
import { ozonService } from 'App/Services/OzonService'
import { OzonClient } from 'App/Clients/OzonClient'
import { OzonImageService } from 'App/Services/OzonImageService'

describe('Ozon Integration Tests', () => {
  
  describe('OzonConfig', () => {
    test('should load default settings', async () => {
      const settings = await ozonConfig.getSettings()
      
      expect(settings).toBeDefined()
      expect(settings.ozon_api_url).toBe('https://api-seller.ozon.ru')
      expect(settings.ozon_sync_enabled).toBeDefined()
      expect(settings.ozon_warehouse_id).toBeDefined()
    })

    test('should validate API settings', async () => {
      const validation = await ozonConfig.validateApiSettings()
      
      expect(validation).toHaveProperty('isValid')
      expect(validation).toHaveProperty('errors')
      expect(Array.isArray(validation.errors)).toBe(true)
    })

    test('should get specific setting', async () => {
      const syncEnabled = await ozonConfig.getSetting('ozon_sync_enabled')
      expect(typeof syncEnabled).toBe('boolean')
    })

    test('should get image settings', async () => {
      const imageSettings = await ozonConfig.getImageSettings()
      
      expect(imageSettings).toHaveProperty('enabled')
      expect(imageSettings).toHaveProperty('baseUrl')
      expect(imageSettings).toHaveProperty('maxImages')
      expect(typeof imageSettings.enabled).toBe('boolean')
      expect(typeof imageSettings.maxImages).toBe('number')
    })
  })

  describe('OzonService', () => {
    const mockProduct = {
      prod_id: 1,
      prod_sku: 'TEST-SKU-001',
      prod_note: 'Тестовый товар для проверки интеграции с Ozon',
      prod_price: 1500,
      prod_count: 10,
      prod_manuf: 'Test Brand',
      prod_cat: '17029016',
      prod_weight: '500',
      prod_images: 'test1.jpg,test2.jpg',
      prod_img: 'main-test',
      prod_img_rumi: 'main-test-rumi'
    }

    test('should map product to Ozon format', async () => {
      const ozonProduct = await ozonService.mapProductToOzon(mockProduct)
      
      expect(ozonProduct).toHaveProperty('offer_id', 'TEST-SKU-001')
      expect(ozonProduct).toHaveProperty('name')
      expect(ozonProduct).toHaveProperty('price', '1500')
      expect(ozonProduct).toHaveProperty('currency_code', 'RUB')
      expect(ozonProduct).toHaveProperty('category_id')
      expect(ozonProduct).toHaveProperty('attributes')
      expect(Array.isArray(ozonProduct.attributes)).toBe(true)
    })

    test('should validate product data', async () => {
      const ozonProduct = await ozonService.mapProductToOzon(mockProduct)
      const validation = await ozonService.validateProductData(ozonProduct)
      
      expect(validation).toHaveProperty('isValid')
      expect(validation).toHaveProperty('errors')
      expect(Array.isArray(validation.errors)).toBe(true)
    })

    test('should handle invalid product data', async () => {
      const invalidProduct = {
        offer_id: '', // Пустой offer_id
        name: '', // Пустое название
        price: '0', // Нулевая цена
        category_id: 0 // Неверная категория
      }

      const validation = await ozonService.validateProductData(invalidProduct as any)
      
      expect(validation.isValid).toBe(false)
      expect(validation.errors.length).toBeGreaterThan(0)
    })

    test('should get settings', async () => {
      const settings = await ozonService.getSettings()
      
      expect(settings).toBeDefined()
      expect(settings).toHaveProperty('ozon_api_key')
      expect(settings).toHaveProperty('ozon_client_id')
      expect(settings).toHaveProperty('ozon_sync_enabled')
    })
  })

  describe('OzonClient', () => {
    let client: OzonClient

    beforeEach(() => {
      client = new OzonClient()
    })

    test('should create client instance', () => {
      expect(client).toBeDefined()
      expect(typeof client.getSettings).toBe('function')
      expect(typeof client.healthCheck).toBe('function')
    })

    test('should get settings', async () => {
      const settings = await client.getSettings()
      
      expect(settings).toBeDefined()
      expect(settings).toHaveProperty('ozon_api_url')
    })

    // Этот тест требует реальных API ключей
    test.skip('should perform health check', async () => {
      const isHealthy = await client.healthCheck()
      expect(typeof isHealthy).toBe('boolean')
    })
  })

  describe('OzonImageService', () => {
    let imageService: OzonImageService

    beforeEach(() => {
      imageService = new OzonImageService()
    })

    test('should create image service instance', () => {
      expect(imageService).toBeDefined()
      expect(typeof imageService.getLocalImages).toBe('function')
      expect(typeof imageService.checkImageExists).toBe('function')
    })

    test('should get image URL', () => {
      const url = imageService.getImageUrl('test.jpg', 'rti')
      
      expect(url).toBeDefined()
      expect(typeof url).toBe('string')
      expect(url).toContain('test.jpg')
      expect(url).toContain('rti')
    })

    test('should validate images', async () => {
      const validation = await imageService.validateImages(['/nonexistent/path.jpg'])
      
      expect(validation).toHaveProperty('valid')
      expect(validation).toHaveProperty('invalid')
      expect(Array.isArray(validation.valid)).toBe(true)
      expect(Array.isArray(validation.invalid)).toBe(true)
    })

    test('should handle empty image paths', async () => {
      const validation = await imageService.validateImages([])
      
      expect(validation.valid).toHaveLength(0)
      expect(validation.invalid).toHaveLength(0)
    })
  })

  describe('Integration Flow', () => {
    const mockProduct = {
      prod_id: 999,
      prod_sku: 'INTEGRATION-TEST-001',
      prod_note: 'Интеграционный тест товара',
      prod_price: 2500,
      prod_count: 5,
      prod_manuf: 'Integration Brand',
      prod_cat: '17029016'
    }

    test('should complete full product mapping flow', async () => {
      // 1. Маппинг товара
      const ozonProduct = await ozonService.mapProductToOzon(mockProduct)
      expect(ozonProduct.offer_id).toBe('INTEGRATION-TEST-001')

      // 2. Валидация
      const validation = await ozonService.validateProductData(ozonProduct)
      expect(validation).toHaveProperty('isValid')

      // 3. Проверка настроек
      const settings = await ozonConfig.getSettings()
      expect(settings.ozon_api_url).toBeDefined()

      console.log('Integration test completed successfully')
    })

    test('should handle configuration changes', async () => {
      // Получаем текущие настройки
      const originalSettings = await ozonConfig.getSettings()
      
      // Проверяем, что настройки загружаются
      expect(originalSettings).toBeDefined()
      
      // Сбрасываем кэш
      ozonConfig.clearCache()
      
      // Загружаем настройки снова
      const reloadedSettings = await ozonConfig.getSettings()
      expect(reloadedSettings).toBeDefined()
      
      // Настройки должны быть одинаковыми
      expect(reloadedSettings.ozon_api_url).toBe(originalSettings.ozon_api_url)
    })
  })

  describe('Error Handling', () => {
    test('should handle missing product data', async () => {
      const emptyProduct = {}
      
      try {
        await ozonService.mapProductToOzon(emptyProduct)
      } catch (error) {
        // Ожидаем, что функция обработает отсутствующие данные
        expect(error).toBeDefined()
      }
    })

    test('should handle invalid settings', async () => {
      const invalidSettings = {
        ozon_api_key: '', // Пустой ключ
        ozon_warehouse_id: -1 // Отрицательный ID
      }

      try {
        await ozonService.updateSettings(invalidSettings)
      } catch (error) {
        expect(error).toBeDefined()
        expect(error.message).toContain('Invalid settings')
      }
    })
  })
})

// Утилиты для тестирования
export const TestUtils = {
  /**
   * Создание мокового товара для тестов
   */
  createMockProduct: (overrides = {}) => ({
    prod_id: 1,
    prod_sku: 'TEST-SKU-001',
    prod_note: 'Test Product',
    prod_price: 1000,
    prod_count: 10,
    prod_manuf: 'Test Brand',
    prod_cat: '17029016',
    prod_weight: '100',
    ...overrides
  }),

  /**
   * Проверка структуры Ozon товара
   */
  validateOzonProductStructure: (product: any) => {
    const requiredFields = [
      'offer_id', 'name', 'price', 'currency_code', 
      'category_id', 'vat', 'attributes'
    ]
    
    for (const field of requiredFields) {
      if (!(field in product)) {
        throw new Error(`Missing required field: ${field}`)
      }
    }
    
    return true
  },

  /**
   * Мок для API ответов
   */
  createMockApiResponse: (data: any, success = true) => ({
    result: success ? data : null,
    error: success ? null : { code: 'TEST_ERROR', message: 'Test error' }
  })
}

// Экспорт для использования в других тестах
export {
  ozonConfig,
  ozonService,
  OzonClient,
  OzonImageService
}
