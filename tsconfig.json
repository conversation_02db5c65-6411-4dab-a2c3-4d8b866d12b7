{"extends": "./node_modules/adonis-preset-ts/tsconfig", "include": ["**/*"], "exclude": ["node_modules", "build"], "compilerOptions": {"useDefineForClassFields": true, "outDir": "build", "rootDir": "./", "sourceMap": false, "declaration": false, "paths": {"App/*": ["./app/*"], "Config/*": ["./config/*"], "Contracts/*": ["./contracts/*"], "Database/*": ["./database/*"]}, "types": ["@adonisjs/core", "@adonisjs/repl", "@adonisjs/session", "@adonisjs/view", "@adonisjs/lucid", "@adonisjs/auth", "@adonisjs/mail"]}}